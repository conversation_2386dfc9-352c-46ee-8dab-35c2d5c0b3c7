# 图片生成记录信息Mock功能
# Image Generation Record Info Mock Feature

## 🎯 功能概述

为 `/api/v1/image/record-info/{task_id}` 接口添加了Mock功能，支持在开发和测试环境中返回模拟数据，无需依赖真实的第三方API。

## 🔧 配置方式

### 环境变量配置

在 `.env` 文件中添加以下配置：

```bash
# Mock Configuration
ENABLE_IMAGE_GENERATION_MOCK=True  # 启用Mock模式
```

### 快速启用脚本

使用提供的脚本快速启用Mock模式：

```bash
cd backend
./enable_mock.sh
```

## 📋 Mock数据格式

当Mock模式启用时，接口返回以下格式的数据：

```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "taskId": "{传入的任务ID}",
        "paramJson": "{\"callBackUrl\":\"http://c89de6f7.natappfree.cc/api/v1/image/callback\",\"enableFallback\":false,\"fallbackModel\":\"FLUX_MAX\",\"fileUrlOrPromptNotEmpty\":true,\"filesUrl\":[\"https://img-bridal.wenhaofree.com/uploads/image_1_8C839A46-3123-4B32-B4E3-0AA50A091AA8.jpeg\"],\"isEnhance\":false,\"nVariants\":1,\"nVariantsValid\":true,\"prompt\":\"Keep the same face from reference image, preserve facial identity, outdoor wedding photography, natural landscape, beautiful scenery, fresh air atmosphere, natural lighting, scenic background, countryside setting, garden wedding, outdoor ceremony, nature-inspired, rustic charm, outdoor romance, natural beauty, landscape wedding\",\"size\":\"1:1\",\"uploadCn\":false}",
        "completeTime": 1754020242059,
        "response": {
            "resultUrls": [
                "https://tempfile.aiquickdraw.com/s/{任务ID}_0_{时间戳}_7055.png"
            ]
        },
        "successFlag": 1,
        "status": "SUCCESS",
        "errorCode": null,
        "errorMessage": null,
        "createTime": 1754020099059,
        "progress": "1.00"
    }
}
```

### 关键特性

1. **动态任务ID**: `taskId` 字段会自动替换为请求中的任务ID
2. **动态时间戳**: `completeTime` 和 `createTime` 使用当前时间戳
3. **动态URL**: `resultUrls` 中的URL包含任务ID和时间戳
4. **完整数据结构**: 包含所有必要的字段，模拟真实API响应

## 🚀 使用方法

### 1. 启用Mock模式

```bash
# 方法1: 使用脚本
./enable_mock.sh

# 方法2: 手动编辑.env文件
echo "ENABLE_IMAGE_GENERATION_MOCK=True" >> ../.env
```

### 2. 重启服务器

```bash
uv run uvicorn app.main:app --reload
```

### 3. 测试接口

```bash
# 使用curl测试
curl -X GET "http://localhost:8000/api/v1/image/record-info/test_task_123" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 使用测试脚本
uv run python test/test_record_info_mock.py
```

## 🧪 测试验证

### 自动化测试

项目提供了完整的测试脚本：

```bash
cd backend
uv run python test/test_record_info_mock.py
```

### 测试覆盖

- ✅ **基本Mock功能**: 验证返回数据格式和内容
- ✅ **任务ID替换**: 验证任务ID正确替换
- ✅ **数据结构验证**: 验证所有必需字段存在
- ✅ **JSON格式验证**: 验证paramJson字段为有效JSON
- ✅ **多任务ID测试**: 验证不同任务ID的处理

### 测试结果示例

```
🎉 所有测试通过! (2/2)
✅ /record-info/ 接口Mock功能正常工作

📊 响应数据结构验证:
  ✅ code: 200
  ✅ msg: success
  ✅ taskId: test_task_8f9cca79b23244dd (正确匹配)
  ✅ paramJson: 有效的JSON字符串
  ✅ response.resultUrls: 1 个URL
  ✅ status: SUCCESS
  ✅ progress: 1.00
```

## 🔄 模式切换

### 启用Mock模式

```bash
# 设置环境变量
ENABLE_IMAGE_GENERATION_MOCK=True

# 重启服务器
uv run uvicorn app.main:app --reload
```

### 禁用Mock模式

```bash
# 设置环境变量
ENABLE_IMAGE_GENERATION_MOCK=False

# 或者删除该配置行
# 重启服务器
uv run uvicorn app.main:app --reload
```

## 📝 实现细节

### 代码结构

```python
# 配置检查
if settings.ENABLE_IMAGE_GENERATION_MOCK:
    logger.info(f"Mock mode enabled, returning mock data for task_id: {task_id}")
    mock_data = generate_mock_record_info(task_id)
    return mock_data

# 正常流程
service = ImageGenerationService(session=db)
result = service.get_generation_record_info(task_id)
return result
```

### Mock数据生成

```python
def generate_mock_record_info(task_id: str) -> Dict[str, Any]:
    """生成mock的图片生成记录信息"""
    current_time_ms = int(time.time() * 1000)
    
    return {
        "code": 200,
        "msg": "success",
        "data": {
            "taskId": task_id,  # 使用传入的任务ID
            "completeTime": current_time_ms,
            "createTime": current_time_ms - 143000,  # 创建时间比完成时间早143秒
            # ... 其他字段
        }
    }
```

## 🎯 使用场景

### 开发环境

- **前端开发**: 无需等待真实API响应，快速开发UI
- **接口测试**: 验证接口集成和数据处理逻辑
- **功能演示**: 向客户展示功能，无需真实数据

### 测试环境

- **单元测试**: 隔离外部依赖，专注业务逻辑测试
- **集成测试**: 测试完整流程，无需第三方API
- **性能测试**: 测试系统性能，排除网络延迟影响

### 故障排除

- **API故障**: 当第三方API不可用时的备用方案
- **调试模式**: 快速验证问题是否来自第三方API
- **离线开发**: 无网络环境下的开发支持

## ⚠️ 注意事项

1. **生产环境**: 确保生产环境中 `ENABLE_IMAGE_GENERATION_MOCK=False`
2. **数据一致性**: Mock数据仅用于开发测试，不代表真实API行为
3. **配置管理**: 建议使用环境变量管理，避免硬编码
4. **日志记录**: Mock模式会在日志中明确标识

## 📚 相关文件

- `backend/app/core/config.py` - 配置定义
- `backend/app/api/routes/image_generation.py` - 接口实现
- `backend/test/test_record_info_mock.py` - 测试脚本
- `backend/enable_mock.sh` - 快速启用脚本
- `backend/.env` - 环境变量配置

---

**更新时间**: 2025-08-01  
**功能状态**: ✅ 已实现并测试通过  
**兼容性**: 向后兼容，不影响现有功能
