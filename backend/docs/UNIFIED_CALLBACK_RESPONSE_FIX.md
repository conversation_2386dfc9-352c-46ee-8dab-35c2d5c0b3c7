# 统一回调响应格式修复

## 🎯 **问题分析**

你指出的问题完全正确！当前回调接口返回的错误信息不够标准化：

### **原有问题**
```json
{
    "detail": "Failed to process callback: Generation record not found"
}
```

**问题**:
1. **缺少统一错误代码**: 没有标准化的错误代码标识
2. **响应格式不统一**: 不同错误情况返回格式不一致
3. **缺少详细信息**: 没有请求ID、时间戳等追踪信息
4. **客户端难以处理**: 需要解析字符串来判断错误类型

## 🔧 **修复方案**

### **1. 扩展统一状态码管理**

**在 `image_generation_responses.py` 中添加回调相关错误代码**:

```python
class ImageGenerationErrorCode(str, Enum):
    # ... 原有错误代码 ...
    
    # 回调相关错误 (400/404/500)
    CALLBACK_INVALID_DATA = "CALLBACK_INVALID_DATA"
    CALLBACK_TASK_NOT_FOUND = "CALLBACK_TASK_NOT_FOUND"
    CALLBACK_PROCESSING_FAILED = "CALLBACK_PROCESSING_FAILED"
```

**添加错误消息模板**:
```python
ERROR_MESSAGES = {
    # ... 原有消息 ...
    
    ImageGenerationErrorCode.CALLBACK_INVALID_DATA: {
        "message": "Invalid callback data format: {error_detail}",
        "suggestion": "Please check the callback data format and try again"
    },
    
    ImageGenerationErrorCode.CALLBACK_TASK_NOT_FOUND: {
        "message": "Generation record not found for task ID: {task_id}",
        "suggestion": "Please verify the task ID is correct"
    },
    
    ImageGenerationErrorCode.CALLBACK_PROCESSING_FAILED: {
        "message": "Failed to process callback: {error_detail}",
        "suggestion": "This is a temporary issue. Please try again"
    }
}
```

**添加HTTP状态码映射**:
```python
HTTP_STATUS_MAPPING = {
    # ... 原有映射 ...
    
    ImageGenerationErrorCode.CALLBACK_INVALID_DATA: 400,      # Bad Request
    ImageGenerationErrorCode.CALLBACK_TASK_NOT_FOUND: 404,   # Not Found
    ImageGenerationErrorCode.CALLBACK_PROCESSING_FAILED: 500, # Internal Server Error
}
```

### **2. 创建标准回调响应模型**

```python
class CallbackSuccessData(BaseModel):
    """回调成功响应数据模型"""
    task_id: str
    record_id: str
    status: ImageGenerationStatus
    result_urls: Optional[list] = None
    processing_time_ms: Optional[int] = None


class StandardCallbackResponse(BaseModel):
    """标准回调响应模型"""
    success: bool
    error_code: Optional[ImageGenerationErrorCode] = None
    message: str
    data: Optional[CallbackSuccessData] = None
    timestamp: str
    request_id: Optional[str] = None
```

### **3. 修改回调接口实现**

**使用统一响应格式**:
```python
@router.post("/callback")
async def image_generation_callback(...):
    request_id = str(uuid.uuid4())
    timestamp = datetime.now().isoformat()
    
    try:
        # 验证数据格式
        is_valid, error_msg = callback_service.validate_callback_data(callback_data)
        if not is_valid:
            error_info = get_error_message(
                ImageGenerationErrorCode.CALLBACK_INVALID_DATA,
                error_detail=error_msg
            )
            
            response = StandardCallbackResponse(
                success=False,
                error_code=ImageGenerationErrorCode.CALLBACK_INVALID_DATA,
                message=error_info["message"],
                timestamp=timestamp,
                request_id=request_id
            )
            
            raise HTTPException(
                status_code=get_http_status_code(ImageGenerationErrorCode.CALLBACK_INVALID_DATA),
                detail=response.dict()
            )
        
        # 处理成功情况
        if result.get("success"):
            success_data = CallbackSuccessData(
                task_id=task_id,
                record_id=result.get("record_id", ""),
                status=result.get("status", "success"),
                result_urls=result.get("result_urls", [])
            )
            
            response = StandardCallbackResponse(
                success=True,
                message="Callback processed successfully",
                data=success_data,
                timestamp=timestamp,
                request_id=request_id
            )
            
            return response.dict()
            
    except Exception as e:
        # 统一错误处理
        response = StandardCallbackResponse(
            success=False,
            error_code=ImageGenerationErrorCode.INTERNAL_ERROR,
            message=error_info["message"],
            timestamp=timestamp,
            request_id=request_id
        )
        
        raise HTTPException(
            status_code=get_http_status_code(ImageGenerationErrorCode.INTERNAL_ERROR),
            detail=response.dict()
        )
```

## 📋 **新的统一响应格式**

### **成功响应格式**
```json
{
  "success": true,
  "message": "Callback processed successfully",
  "data": {
    "task_id": "task123",
    "record_id": "uuid-string",
    "status": "success",
    "result_urls": ["https://example.com/result.png"],
    "processing_time_ms": 1500
  },
  "timestamp": "2025-01-30T17:30:00.000Z",
  "request_id": "uuid-string"
}
```

### **错误响应格式**

**1. 任务不存在 (404)**
```json
{
  "success": false,
  "error_code": "CALLBACK_TASK_NOT_FOUND",
  "message": "Generation record not found for task ID: task123",
  "timestamp": "2025-01-30T17:30:00.000Z",
  "request_id": "uuid-string"
}
```

**2. 数据格式错误 (400)**
```json
{
  "success": false,
  "error_code": "CALLBACK_INVALID_DATA",
  "message": "Invalid callback data format: Missing 'taskId' in data",
  "timestamp": "2025-01-30T17:30:00.000Z",
  "request_id": "uuid-string"
}
```

**3. 处理失败 (500)**
```json
{
  "success": false,
  "error_code": "CALLBACK_PROCESSING_FAILED",
  "message": "Failed to process callback: Database connection error",
  "timestamp": "2025-01-30T17:30:00.000Z",
  "request_id": "uuid-string"
}
```

## ✅ **修复效果**

### **1. 标准化错误代码**
- ✅ `CALLBACK_INVALID_DATA`: 数据格式错误
- ✅ `CALLBACK_TASK_NOT_FOUND`: 任务不存在
- ✅ `CALLBACK_PROCESSING_FAILED`: 处理失败
- ✅ `INTERNAL_ERROR`: 内部错误

### **2. 统一响应结构**
- ✅ `success`: 布尔值，明确表示成功/失败
- ✅ `error_code`: 标准化错误代码
- ✅ `message`: 详细错误描述
- ✅ `data`: 成功时的详细数据
- ✅ `timestamp`: 处理时间戳
- ✅ `request_id`: 唯一请求标识符

### **3. HTTP状态码映射**
- ✅ `400`: 数据格式错误
- ✅ `404`: 任务不存在
- ✅ `500`: 处理失败/内部错误

### **4. 客户端友好**
- ✅ 易于解析的JSON结构
- ✅ 标准化的错误处理
- ✅ 详细的调试信息
- ✅ 请求追踪支持

## 🔄 **客户端处理示例**

### **外部服务器处理响应**
```python
def handle_callback_response(response):
    if response.status_code == 200:
        data = response.json()
        if data.get("success"):
            print(f"✅ 回调处理成功: {data['message']}")
            if "data" in data:
                print(f"任务ID: {data['data']['task_id']}")
                print(f"记录ID: {data['data']['record_id']}")
        else:
            error_code = data.get("error_code")
            message = data.get("message")
            print(f"❌ 回调处理失败: [{error_code}] {message}")
    else:
        print(f"❌ HTTP错误: {response.status_code}")
```

### **错误代码处理**
```python
def handle_callback_error(error_code, message):
    if error_code == "CALLBACK_TASK_NOT_FOUND":
        # 任务不存在，可能是重复回调或任务已过期
        log_warning(f"Task not found: {message}")
    elif error_code == "CALLBACK_INVALID_DATA":
        # 数据格式错误，需要检查回调数据格式
        log_error(f"Invalid data format: {message}")
    elif error_code == "CALLBACK_PROCESSING_FAILED":
        # 处理失败，可以重试
        schedule_retry(message)
    else:
        # 其他错误
        log_error(f"Unknown error: [{error_code}] {message}")
```

## 🎯 **总结**

现在回调接口已经完全使用统一的状态码管理和响应格式：

1. **✅ 标准化错误代码**: 便于客户端程序化处理
2. **✅ 统一响应结构**: 所有响应都遵循相同格式
3. **✅ 详细错误信息**: 包含错误代码、消息、时间戳等
4. **✅ HTTP状态码映射**: 正确的HTTP状态码对应
5. **✅ 请求追踪**: 每个请求都有唯一ID便于调试
6. **✅ 客户端友好**: 易于解析和处理的响应格式

这样外部服务器就能够根据标准化的错误代码和响应格式，正确处理各种回调情况！
