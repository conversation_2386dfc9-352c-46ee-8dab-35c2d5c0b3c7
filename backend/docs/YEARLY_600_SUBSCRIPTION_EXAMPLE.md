# 年度600次订阅套餐使用示例

## 套餐概述

**年度600次套餐** (`sub_yearly_600`) 是介于480次和720次之间的中档年度订阅选项：

- **年度总次数**: 600次图片生成
- **月度平均**: 50次/月
- **赠送积分**: 3000积分
- **积分价值**: 额外600次使用机会
- **总价值**: 1200次使用机会

## API 调用示例

### 1. 订阅年度600次套餐

```bash
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-yearly" \
  -H "Authorization: Bearer <user-jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "123e4567-e89b-12d3-a456-************",
    "images_per_year": 600
  }'
```

**响应:**
```json
{
  "subscription": {
    "id": "sub-uuid",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "product_id": "sub_yearly_600",
    "platform": "stripe",
    "start_date": "2025-01-31T10:00:00Z",
    "end_date": "2026-01-31T10:00:00Z",
    "is_active": true
  },
  "message": "New subscription created successfully. Bonus credits added: 3000",
  "is_new_subscription": true
}
```

### 2. 查看订阅状态

```bash
curl -X GET "http://localhost:8000/api/v1/subscriptions/status" \
  -H "Authorization: Bearer <user-jwt-token>"
```

**响应:**
```json
{
  "user_id": "123e4567-e89b-12d3-a456-************",
  "has_active_subscription": true,
  "subscription_end_date": "2026-01-31T10:00:00Z",
  "subscription_product_id": "sub_yearly_600",
  "total_credits": 3010,      // 试用10积分 + 赠送3000积分
  "monthly_usage_count": 0,
  "monthly_limit": 50,        // 每月50次
  "can_use_service": true
}
```

### 3. 查看用户积分详情

```bash
curl -X POST "http://localhost:8000/api/v1/login/test-token" \
  -H "Authorization: Bearer <user-jwt-token>"
```

**响应:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "is_active": true,
  "total_credits": 3010,      // 总积分
  "remaining_credits": 3010,  // 剩余积分
  "platform": "web",
  "auth_provider": "email"
}
```

## 年度套餐对比

### 套餐配置对比

| 套餐 | 年度次数 | 月度次数 | 赠送积分 | 积分价值 | 总价值 |
|------|----------|----------|----------|----------|--------|
| sub_yearly_480 | 480次 | 40次/月 | 2400积分 | 480次 | 960次 |
| **sub_yearly_600** | **600次** | **50次/月** | **3000积分** | **600次** | **1200次** |
| sub_yearly_720 | 720次 | 60次/月 | 3600积分 | 720次 | 1440次 |

### API 调用对比

```bash
# 年度480次套餐
curl -X POST ".../upgrade-to-yearly" -d '{"user_id": "...", "images_per_year": 480}'

# 年度600次套餐 (新增)
curl -X POST ".../upgrade-to-yearly" -d '{"user_id": "...", "images_per_year": 600}'

# 年度720次套餐
curl -X POST ".../upgrade-to-yearly" -d '{"user_id": "...", "images_per_year": 720}'
```

## 使用场景分析

### 场景1: 中等使用量用户

**用户画像**: 每月需要40-60次图片生成的用户

**使用模式**:
- 前10个月: 每月使用50次订阅配额
- 后2个月: 某些月份使用较多，超出50次限制
- 超限使用: 使用赠送的3000积分

**价值体现**:
- 比480次套餐多120次年度配额
- 比720次套餐少120次但价格更优惠
- 3000积分提供充足的超限使用缓冲

### 场景2: 不规律使用用户

**用户画像**: 使用量波动较大的用户

**使用模式**:
- 淡季: 每月20-30次
- 旺季: 每月70-80次
- 年度总计: 约600次左右

**价值体现**:
- 年度600次配额满足基本需求
- 3000积分应对旺季超限使用
- 灵活应对使用量波动

### 场景3: 成长型用户

**用户画像**: 使用量逐渐增长的用户

**使用模式**:
- 初期: 每月30-40次
- 中期: 每月50-60次
- 后期: 每月60-70次

**价值体现**:
- 600次年度配额适应成长需求
- 积分支持后期超限使用
- 为下年度升级720次套餐做准备

## 数据库记录示例

### 订阅记录

```sql
INSERT INTO subscription (
    id, user_id, product_id, platform, start_date, end_date, 
    is_active, original_transaction_id, last_verified_at
) VALUES (
    'yearly-600-sub-uuid',
    '123e4567-e89b-12d3-a456-************',
    'sub_yearly_600',
    'stripe',
    '2025-01-31 10:00:00+00',
    '2026-01-31 10:00:00+00',
    true,
    'admin_upgrade_def67890',
    '2025-01-31 10:00:00+00'
);
```

### 积分包记录

```sql
INSERT INTO creditpackage (
    id, user_id, credits, remaining_credits, product_id, platform, purchased_at
) VALUES (
    'yearly-600-bonus-uuid',
    '123e4567-e89b-12d3-a456-************',
    3000, 3000,
    'subscription_bonus_sub_yearly_600',
    'stripe',
    '2025-01-31 10:00:00+00'
);
```

## 业务指标监控

### 套餐使用率查询

```sql
-- 查询年度600次套餐的订阅数量
SELECT COUNT(*) as subscription_count
FROM subscription 
WHERE product_id = 'sub_yearly_600' 
  AND is_active = true;

-- 查询年度600次套餐用户的积分使用情况
SELECT 
    COUNT(*) as user_count,
    AVG(credits - remaining_credits) as avg_credits_used,
    AVG(remaining_credits) as avg_credits_remaining
FROM creditpackage 
WHERE product_id = 'subscription_bonus_sub_yearly_600';
```

### 月度使用分析

```sql
-- 查询年度600次套餐用户的月度使用情况
SELECT 
    u.id as user_id,
    COUNT(ur.id) as monthly_usage,
    s.product_id
FROM user u
JOIN subscription s ON u.id = s.user_id
LEFT JOIN usagerecord ur ON u.id = ur.user_id 
    AND ur.used_at >= date_trunc('month', CURRENT_DATE)
WHERE s.product_id = 'sub_yearly_600' 
  AND s.is_active = true
GROUP BY u.id, s.product_id
ORDER BY monthly_usage DESC;
```

## 错误处理示例

### 无效参数错误

```bash
curl -X POST ".../upgrade-to-yearly" \
  -d '{"user_id": "...", "images_per_year": 500}'
```

**错误响应:**
```json
{
  "detail": "Invalid images_per_year. Must be 480, 600, or 720"
}
```

### 积分创建失败

如果积分包创建失败，订阅仍然成功：

```json
{
  "subscription": {...},
  "message": "New subscription created successfully (bonus credits creation failed)",
  "is_new_subscription": true
}
```

## 升级路径建议

### 从月度套餐升级

- **月度40次** → **年度600次**: 年度节省成本，获得更多使用次数
- **月度60次** → **年度600次**: 适合使用量稳定的用户

### 从其他年度套餐调整

- **年度480次** → **年度600次**: 使用量增长时的自然升级
- **年度720次** → **年度600次**: 使用量下降时的降级选择

这个年度600次套餐为用户提供了更灵活的选择，满足中等使用量用户的需求，同时通过3000积分赠送提供了充足的超限使用保障。