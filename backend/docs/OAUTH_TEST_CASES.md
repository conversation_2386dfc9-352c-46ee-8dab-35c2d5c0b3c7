# 第三方登录测试案例

## 🍎 Apple Sign In 登录测试（✅ 完全可用）

### 测试令牌
以下是一个有效的测试用 Apple Identity Token：

```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.G7e6kK8N2bXvTDYfV7UDFlLN0HE1ekLvUrthLbxZpKk
```

### 测试命令

#### 1. Apple Sign In 专用端点
```bash
curl -X POST "http://localhost:8000/api/v1/oauth/apple/login" \
  -H "Content-Type: application/json" \
  -d '{
    "identity_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.G7e6kK8N2bXvTDYfV7UDFlLN0HE1ekLvUrthLbxZpKk",
    "platform": "ios"
  }'
```

#### 2. 通用 OAuth 端点
```bash
curl -X POST "http://localhost:8000/api/v1/oauth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "apple",
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.G7e6kK8N2bXvTDYfV7UDFlLN0HE1ekLvUrthLbxZpKk",
    "platform": "ios"
  }'
```

### 预期响应
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************...",
  "token_type": "bearer",
  "user": {
    "id": "51a8a41b-070e-45c7-8188-008c768261a7",
    "email": "<EMAIL>",
    "full_name": "Test User",
    "is_active": true,
    "is_superuser": false,
    "platform": "ios",
    "auth_provider": "apple",
    "provider_user_id": "001234.567890abcdef12345678901234567890abcdef",
    "avatar_url": null,
    "created_at": "2025-01-29T16:47:07.123456Z",
    "last_login": "2025-01-29T16:47:07.123456Z"
  },
  "is_new_user": true
}
```

## 🔍 Google OAuth 登录测试（⚠️ 需要真实令牌）

### 测试命令
```bash
curl -X POST "http://localhost:8000/api/v1/oauth/google/login" \
  -H "Content-Type: application/json" \
  -d '{
    "access_token": "ya29.a0ARrdaM9...",
    "platform": "web"
  }'
```

**注意**: Google OAuth 需要真实的 access_token，无法使用模拟令牌。

## 📋 获取支持的登录平台

```bash
curl -X GET "http://localhost:8000/api/v1/oauth/providers"
```

### 预期响应
```json
{
  "providers": {
    "apple": {
      "name": "Apple Sign In",
      "description": "使用 Apple ID 登录",
      "icon": "apple",
      "supported_platforms": ["ios", "web"],
      "implementation_status": "implemented"
    },
    "google": {
      "name": "Google",
      "description": "使用 Google 账户登录",
      "icon": "google",
      "supported_platforms": ["ios", "android", "web"],
      "implementation_status": "implemented"
    },
    "wechat": {
      "name": "微信",
      "description": "使用微信账户登录",
      "icon": "wechat",
      "supported_platforms": ["ios", "android", "web"],
      "implementation_status": "pending"
    },
    "facebook": {
      "name": "Facebook",
      "description": "使用 Facebook 账户登录",
      "icon": "facebook",
      "supported_platforms": ["ios", "android", "web"],
      "implementation_status": "pending"
    },
    "github": {
      "name": "GitHub",
      "description": "使用 GitHub 账户登录",
      "icon": "github",
      "supported_platforms": ["web"],
      "implementation_status": "pending"
    }
  },
  "total_count": 5,
  "implemented_count": 2
}
```

## 🧪 测试步骤

1. **启动服务器**:
   ```bash
   cd backend
   source .venv/bin/activate
   fastapi dev app/main.py
   ```

2. **测试 Apple Sign In**:
   - 使用上面提供的测试令牌
   - 应该成功创建用户并返回 JWT 令牌

3. **验证用户创建**:
   - 检查数据库中是否创建了新用户
   - 用户的 `auth_provider` 应该是 `apple`
   - `hashed_password` 应该是 `null`

4. **测试重复登录**:
   - 再次使用相同令牌登录
   - 应该返回现有用户，`is_new_user` 为 `false`

## 🔧 故障排除

### 常见错误

1. **"PyJWT library required"**
   - 解决方案: `uv sync` 重新安装依赖

2. **"Invalid Apple identity token format"**
   - 检查令牌格式是否正确
   - 确保没有换行符或额外空格

3. **"Apple token missing subject"**
   - 令牌可能已过期或格式不正确
   - 使用提供的测试令牌

4. **数据库连接错误**
   - 确保 PostgreSQL 服务正在运行
   - 检查数据库配置

## 📊 测试结果验证

成功的 Apple Sign In 登录应该：

1. ✅ 返回 200 状态码
2. ✅ 包含有效的 JWT access_token
3. ✅ 用户信息完整（email, full_name, auth_provider）
4. ✅ 在数据库中创建用户记录
5. ✅ 用户的 auth_provider 为 "apple"
6. ✅ 用户的 hashed_password 为 null

## 🚀 生产环境配置

在生产环境中使用时，需要：

1. **配置真实的 Apple 开发者证书**
2. **实现完整的 JWT 签名验证**
3. **添加速率限制和安全检查**
4. **配置正确的环境变量**
5. **启用 HTTPS**
