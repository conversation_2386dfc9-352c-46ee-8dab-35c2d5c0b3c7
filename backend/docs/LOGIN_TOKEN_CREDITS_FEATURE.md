# Login Test Token 积分功能增强

## 功能概述

`/login/test-token` 接口已增强，现在除了返回用户基本信息外，还会返回用户的积分统计信息。

## API 变更

### 请求
```http
POST /api/v1/login/test-token
Authorization: Bearer <your-jwt-token>
```

### 响应格式

**之前的响应 (UserPublic):**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "is_active": true,
  "is_superuser": false,
  "full_name": "User Name",
  "platform": "web",
  "created_at": "2025-01-01T00:00:00Z",
  "last_login": "2025-01-01T12:00:00Z",
  "auth_provider": "email",
  "provider_user_id": null,
  "avatar_url": null
}
```

**现在的响应 (UserPublicWithCredits):**
```json
{
  "id": "uuid",
  "email": "<EMAIL>",
  "is_active": true,
  "is_superuser": false,
  "full_name": "User Name",
  "platform": "web",
  "created_at": "2025-01-01T00:00:00Z",
  "last_login": "2025-01-01T12:00:00Z",
  "auth_provider": "email",
  "provider_user_id": null,
  "avatar_url": null,
  "total_credits": 160,
  "remaining_credits": 115
}
```

## 新增字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| `total_credits` | int | 用户购买的总积分数（所有积分包的积分总和） |
| `remaining_credits` | int | 用户当前剩余的可用积分数 |

## 积分计算逻辑

1. **总积分 (total_credits)**: 查询用户所有 `CreditPackage` 记录，对 `credits` 字段求和
2. **剩余积分 (remaining_credits)**: 查询用户所有 `CreditPackage` 记录，对 `remaining_credits` 字段求和

## 数据库查询

```sql
-- 查询用户的所有积分包
SELECT * FROM creditpackage WHERE user_id = '<user_id>';

-- 计算总积分
SELECT SUM(credits) as total_credits FROM creditpackage WHERE user_id = '<user_id>';

-- 计算剩余积分
SELECT SUM(remaining_credits) as remaining_credits FROM creditpackage WHERE user_id = '<user_id>';
```

## 使用场景

1. **客户端初始化**: 用户登录后获取积分状态，显示在UI上
2. **权限检查**: 在调用付费功能前检查用户是否有足够积分
3. **用户中心**: 显示用户的积分余额和历史

## 示例积分包数据

假设用户有以下积分包：

| 积分包类型 | 总积分 | 剩余积分 | 产品ID |
|------------|--------|----------|---------|
| 试用积分 | 10 | 5 | trial_credits_new_user |
| 购买积分包1 | 50 | 30 | credits_pack_50 |
| 购买积分包2 | 100 | 80 | credits_pack_100 |

**计算结果:**
- `total_credits`: 10 + 50 + 100 = 160
- `remaining_credits`: 5 + 30 + 80 = 115

## 兼容性说明

- ✅ **向后兼容**: 新增字段不会影响现有客户端
- ✅ **类型安全**: 使用 Pydantic 模型确保数据类型正确
- ✅ **性能优化**: 单次查询获取所有积分包数据

## 相关模型

### UserPublicWithCredits
```python
class UserPublicWithCredits(UserPublic):
    """用户公开信息，包含积分信息"""
    total_credits: int = Field(default=0, description="用户总积分数")
    remaining_credits: int = Field(default=0, description="用户剩余积分数")
```

### CreditPackage
```python
class CreditPackage(CreditPackageBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False, ondelete="CASCADE")
    credits: int = Field(ge=1)  # 总积分
    remaining_credits: int = Field(ge=0)  # 剩余积分
    # ... 其他字段
```

## 测试建议

1. **单元测试**: 测试积分计算逻辑
2. **集成测试**: 测试完整的API响应
3. **边界测试**: 测试用户无积分包的情况
4. **性能测试**: 测试大量积分包时的查询性能