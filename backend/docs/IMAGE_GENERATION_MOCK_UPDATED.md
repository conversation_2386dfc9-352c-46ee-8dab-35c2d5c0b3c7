# 图片生成Mock功能更新
# Image Generation Mock Feature Update

## 🎯 功能概述

图片生成接口现在支持Mock模式，当启用Mock配置时，会返回模拟数据而不调用真实的第三方API。Mock响应格式已更新为符合要求的标准格式。

## 🔧 配置方式

### 环境变量配置

在 `.env` 文件中设置以下配置：

```bash
# Mock Configuration
ENABLE_IMAGE_GENERATION_MOCK=True  # 启用Mock模式
```

### 快速启用脚本

使用提供的脚本快速启用Mock模式：

```bash
cd backend
./enable_mock.sh
```

## 📋 Mock响应格式

当Mock模式启用时，图片生成接口返回以下格式的数据：

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "taskId": "test-随机生成的12位字符"
  }
}
```

### 任务ID格式说明

- **前缀**: `test-`
- **随机部分**: 12位小写字母和数字组合
- **总长度**: 17个字符
- **格式**: `test-[a-z0-9]{12}`
- **示例**: `test-t6cnxy3adkul`, `test-4jkg8swhai42`

## 🚀 使用方法

### 1. 启用Mock模式

```bash
# 设置环境变量
export ENABLE_IMAGE_GENERATION_MOCK=True

# 或者在.env文件中设置
echo "ENABLE_IMAGE_GENERATION_MOCK=True" >> .env
```

### 2. 重启服务器

```bash
uv run uvicorn app.main:app --reload
```

### 3. 调用接口

```bash
curl -X POST "http://localhost:8000/api/v1/image/generate-image" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Beautiful wedding photography",
    "size": "1:1",
    "n_variants": 1,
    "is_enhance": false
  }'
```

### 4. 预期响应

```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "taskId": "test-abc123def456"
  }
}
```

## 🧪 测试验证

### 运行测试脚本

```bash
# 测试Mock函数
cd backend
uv run python test_mock_simple.py

# 测试完整接口（需要服务器运行）
uv run python test/test_image_generation_mock_format.py
```

### 测试内容

1. **响应格式验证**: 确保返回正确的JSON结构
2. **任务ID格式验证**: 确保任务ID符合 `test-[a-z0-9]{12}` 格式
3. **随机性验证**: 确保多次调用生成不同的任务ID
4. **接口集成验证**: 确保在真实HTTP请求中正常工作

## 🔄 模式切换

### 启用Mock模式

```bash
# 方法1: 环境变量
export ENABLE_IMAGE_GENERATION_MOCK=True

# 方法2: .env文件
ENABLE_IMAGE_GENERATION_MOCK=True

# 方法3: 使用脚本
./enable_mock.sh
```

### 禁用Mock模式

```bash
# 方法1: 环境变量
export ENABLE_IMAGE_GENERATION_MOCK=False

# 方法2: .env文件
ENABLE_IMAGE_GENERATION_MOCK=False

# 方法3: 删除配置行
# 注释或删除 ENABLE_IMAGE_GENERATION_MOCK 行
```

## 📝 实现细节

### 代码位置

- **Mock函数**: `backend/app/api/routes/image_generation.py` - `generate_mock_image_generation_response()`
- **配置检查**: `backend/app/api/routes/image_generation.py` - `submit_image_generation()` 函数
- **配置定义**: `backend/app/core/config.py` - `ENABLE_IMAGE_GENERATION_MOCK`

### Mock函数实现

```python
def generate_mock_image_generation_response() -> Dict[str, Any]:
    """
    生成mock的图片生成提交响应数据
    
    Returns:
        Dict[str, Any]: mock的图片生成提交响应数据，格式参考：
        {
          "code": 200,
          "msg": "success",
          "data": {
            "taskId": "test-随机生成"
          }
        }
    """
    import random
    import string

    # 生成随机任务ID，前缀为 "test-"
    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))
    mock_task_id = f"test-{random_suffix}"

    return {
        "code": 200,
        "msg": "success",
        "data": {
            "taskId": mock_task_id
        }
    }
```

## 💡 使用场景

1. **开发环境**: 避免消耗真实API配额
2. **测试环境**: 提供可预测的响应格式
3. **演示环境**: 快速展示功能而无需真实API
4. **集成测试**: 验证接口集成逻辑

## ⚠️ 注意事项

1. **生产环境**: 确保在生产环境中禁用Mock模式
2. **任务ID唯一性**: Mock模式下每次调用都会生成新的随机任务ID
3. **配置重启**: 修改Mock配置后需要重启服务器才能生效
4. **测试覆盖**: Mock模式只影响图片生成提交接口，不影响其他相关接口

## 🔍 故障排除

### 常见问题

1. **Mock模式未生效**
   - 检查环境变量是否正确设置
   - 确认服务器已重启
   - 查看服务器日志确认配置加载

2. **任务ID格式错误**
   - 确认使用的是最新版本的Mock函数
   - 检查是否有其他代码修改了响应格式

3. **测试失败**
   - 确认服务器正在运行
   - 检查认证token是否有效
   - 验证网络连接是否正常

### 调试方法

```bash
# 检查配置
echo $ENABLE_IMAGE_GENERATION_MOCK

# 查看服务器日志
tail -f logs/app.log

# 测试Mock函数
uv run python test_mock_simple.py
```
