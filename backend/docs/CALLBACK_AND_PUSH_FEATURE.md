# 图片生成回调和推送通知功能

本文档描述了新添加的图片生成回调处理和iOS推送通知功能。

## 功能概述

实现了完整的图片生成异步处理流程：
1. 用户提交图片生成请求
2. 系统调用第三方API并配置回调URL
3. 第三方API完成处理后回调我们的服务器
4. 系统处理回调结果并通过APNS向iOS设备发送推送通知

## 新增功能

### 1. 回调URL配置
- 在配置文件中设置统一的回调URL
- 所有图片生成请求自动使用配置的回调URL
- 支持本地开发和生产环境的不同配置

### 2. 设备Token管理
- 用户可以注册iOS设备的推送token
- 支持多设备注册
- 自动管理token的活跃状态

### 3. 回调处理服务
- 接收第三方API的回调通知
- 解析成功/失败结果
- 更新数据库中的生成记录状态

### 4. APNS推送通知
- 集成Apple Push Notification Service
- 支持沙盒和生产环境
- 自动向用户的所有iOS设备发送通知

## 技术实现

### 配置项 (config.py)
```python
# 图片生成回调URL - 完整路径构成
# 主应用前缀: /api/v1 (API_V1_STR)
# 图片路由前缀: /image (在 api/main.py 中配置)
# 回调端点: /callback (在 image_generation.py 中定义)
# 完整URL: http://localhost:8000/api/v1/image/callback
IMAGE_GENERATION_CALLBACK_URL: str = "http://localhost:8000/api/v1/image/callback"

# APNS配置
APNS_KEY_ID: str = ""
APNS_TEAM_ID: str = ""
APNS_BUNDLE_ID: str = ""
APNS_KEY_PATH: str = ""
APNS_USE_SANDBOX: bool = True
```

### 数据模型 (models.py)
- `DeviceToken`: 设备推送token管理
- `DeviceTokenRegisterRequest/Response`: API请求/响应模型

### 核心服务

#### APNSService (apns_service.py)
- JWT token生成和管理
- 推送通知发送
- 多设备批量推送

#### DeviceTokenService (device_token_service.py)
- 设备token注册和管理
- token状态更新
- 清理过期token

#### ImageCallbackService (image_callback_service.py)
- 回调数据验证
- 生成记录查找和更新
- 推送通知触发

### API端点

#### 设备Token管理
- `POST /api/v1/device-tokens/register` - 注册设备token
- `GET /api/v1/device-tokens/list` - 获取token列表
- `DELETE /api/v1/device-tokens/deactivate` - 停用token
- `GET /api/v1/device-tokens/health` - 健康检查

#### 回调处理
- `POST /api/v1/image/callback` - 接收图片生成回调

**回调URL路径构成说明**:
- 基础URL: `http://localhost:8000` (或生产环境域名)
- API版本前缀: `/api/v1` (来自 `settings.API_V1_STR`)
- 图片生成路由前缀: `/image` (在 `app/api/main.py` 中配置)
- 回调端点: `/callback` (在 `image_generation.py` 中定义)
- **完整URL**: `http://localhost:8000/api/v1/image/callback`

**回调数据格式**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "taskId": "task-uuid-here",
    "info": {
      "result_urls": ["https://example.com/result.png"]
    }
  }
}
```

## 使用流程

### 1. iOS应用集成
```swift
// 1. 获取设备token
func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
    let tokenString = deviceToken.map { String(format: "%02.2hhx", $0) }.joined()
    
    // 2. 上传到服务器
    registerDeviceToken(tokenString)
}

// 3. 处理推送通知
func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse) {
    let userInfo = response.notification.request.content.userInfo
    
    if let type = userInfo["type"] as? String,
       type == "image_generation_success",
       let recordId = userInfo["record_id"] as? String {
        // 跳转到结果页面
        showImageResult(recordId: recordId)
    }
}
```

### 2. 服务端配置
```bash
# .env文件配置
IMAGE_GENERATION_CALLBACK_URL=https://your-domain.com/api/v1/image/callback
APNS_KEY_ID=your_key_id
APNS_TEAM_ID=your_team_id
APNS_BUNDLE_ID=com.yourapp.bundleid
APNS_KEY_PATH=/path/to/AuthKey_XXXXXXXXXX.p8
APNS_USE_SANDBOX=false  # 生产环境设为false
```

### 3. 完整流程示例
```python
# 1. 用户注册设备token
POST /api/v1/device-tokens/register
{
    "device_token": "abc123...",
    "platform": "ios"
}

# 2. 用户提交图片生成请求
POST /api/v1/image/generate-image
{
    "prompt": "A beautiful landscape",
    "size": "1:1"
}

# 3. 系统自动配置callback URL并调用第三方API

# 4. 第三方API完成后回调
POST /api/v1/image/callback
{
    "code": 200,
    "msg": "success",
    "data": {
        "taskId": "task123",
        "info": {
            "result_urls": ["https://result.png"]
        }
    }
}

# 5. 系统自动发送推送通知到用户设备
```

## 推送通知格式

### 成功通知
```json
{
    "aps": {
        "alert": {
            "title": "图片生成完成",
            "body": "您的图片已成功生成，点击查看结果"
        },
        "sound": "default"
    },
    "type": "image_generation_success",
    "task_id": "task123",
    "record_id": "record-uuid",
    "result_urls": ["https://result.png"]
}
```

### 失败通知
```json
{
    "aps": {
        "alert": {
            "title": "图片生成失败",
            "body": "您的内容被 OpenAI 标记为违反内容政策"
        },
        "sound": "default"
    },
    "type": "image_generation_failed",
    "task_id": "task123",
    "record_id": "record-uuid",
    "error_message": "违反内容政策"
}
```

## 测试方法

### 1. 配置测试
```bash
python test_callback_and_push.py
```

### 2. API测试
```bash
./test_callback_api.sh
```

### 3. 手动测试回调
```bash
curl -X POST "http://localhost:8000/api/v1/image/callback" \
  -H "Content-Type: application/json" \
  -d '{
    "code": 200,
    "msg": "success",
    "data": {
      "taskId": "test123",
      "info": {
        "result_urls": ["https://example.com/result.png"]
      }
    }
  }'
```

## 注意事项

1. **APNS证书**: 需要从Apple Developer获取.p8密钥文件
2. **Bundle ID**: 必须与iOS应用的Bundle ID完全匹配
3. **沙盒环境**: 开发时使用沙盒，生产时切换到生产环境
4. **回调URL**: 必须是公网可访问的URL
5. **设备Token**: 每次应用安装后都会变化，需要重新注册
6. **权限**: iOS用户需要授权推送通知权限

## 故障排除

1. **推送失败**: 检查APNS配置和设备token有效性
2. **回调失败**: 确认回调URL可访问且格式正确
3. **找不到记录**: 检查taskId是否正确匹配
4. **权限错误**: 确认JWT token有效且用户活跃

## 安全考虑

1. 回调接口不需要认证，但应验证数据来源
2. 设备token应定期清理无效记录
3. APNS密钥文件应安全存储
4. 推送内容不应包含敏感信息
