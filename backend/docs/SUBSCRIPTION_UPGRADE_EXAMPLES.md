# 用户订阅升级完整测试案例

## 🚀 快速开始

### 1. 准备工作
```bash
# 启动服务器
cd backend
source .venv/bin/activate
fastapi dev app/main.py

# 获取管理员令牌（用于升级其他用户）
curl -X POST "http://localhost:8000/api/v1/login/access-token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=changethis"

# 保存返回的 access_token 用于后续请求
```

### 2. 查看用户当前状态
```bash
# 查看用户订阅状态
curl -X GET "http://localhost:8000/api/v1/subscriptions/status" \
  -H "Authorization: Bearer YOUR_USER_TOKEN"

# 预期响应（免费用户）
{
  "user_id": "123e4567-e89b-12d3-a456-426614174000",
  "has_active_subscription": false,
  "subscription_end_date": null,
  "subscription_product_id": null,
  "total_credits": 0,
  "monthly_usage_count": 0,
  "monthly_limit": 0,
  "can_use_service": false
}
```

## 📈 订阅升级方案

### 方案一：快速升级到月度订阅（推荐）

#### 升级到月度基础版（40张图片/月）
```bash
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-monthly?user_id=123e4567-e89b-12d3-a456-426614174000&images_per_month=40" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 成功响应
{
  "subscription": {
    "id": "sub_abc123-def4-5678-9012-345678901234",
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "product_id": "sub_monthly_40",
    "platform": "stripe",
    "start_date": "2025-01-29T10:00:00Z",
    "end_date": "2025-02-28T10:00:00Z",
    "is_active": true,
    "original_transaction_id": "admin_upgrade_abc12345",
    "last_verified_at": "2025-01-29T10:00:00Z"
  },
  "message": "New subscription created successfully",
  "is_new_subscription": true
}
```

#### 升级到月度高级版（60张图片/月）
```bash
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-monthly?user_id=123e4567-e89b-12d3-a456-426614174000&images_per_month=60" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### 方案二：快速升级到年度订阅

#### 升级到年度基础版（40张图片/月，12个月）
```bash
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-yearly?user_id=123e4567-e89b-12d3-a456-426614174000&images_per_month=40" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 成功响应
{
  "subscription": {
    "id": "sub_xyz789-abc1-2345-6789-012345678901",
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "product_id": "sub_yearly_480",
    "platform": "stripe",
    "start_date": "2025-01-29T10:00:00Z",
    "end_date": "2026-01-29T10:00:00Z",
    "is_active": true,
    "original_transaction_id": "admin_upgrade_xyz78901",
    "last_verified_at": "2025-01-29T10:00:00Z"
  },
  "message": "New subscription created successfully",
  "is_new_subscription": true
}
```

#### 升级到年度高级版（60张图片/月，12个月）
```bash
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-yearly?user_id=123e4567-e89b-12d3-a456-426614174000&images_per_month=60" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

### 方案三：自定义订阅升级

#### 自定义时长和产品
```bash
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "product_id": "sub_monthly_40",
    "duration_days": 90,
    "platform": "stripe"
  }'

# 这将创建一个90天的订阅（3个月）
```

## 🔍 验证升级结果

### 1. 检查用户订阅状态
```bash
curl -X GET "http://localhost:8000/api/v1/subscriptions/status" \
  -H "Authorization: Bearer YOUR_USER_TOKEN"

# 升级后的响应
{
  "user_id": "123e4567-e89b-12d3-a456-426614174000",
  "has_active_subscription": true,
  "subscription_end_date": "2025-02-28T10:00:00Z",
  "subscription_product_id": "sub_monthly_40",
  "total_credits": 0,
  "monthly_usage_count": 0,
  "monthly_limit": 40,
  "can_use_service": true
}
```

### 2. 检查服务访问权限
```bash
curl -X GET "http://localhost:8000/api/v1/subscriptions/check-access" \
  -H "Authorization: Bearer YOUR_USER_TOKEN"

# 升级后的响应
{
  "can_access": true,
  "remaining_credits": 0,
  "remaining_monthly_usage": 40,
  "subscription_status": "active"
}
```

### 3. 测试图片生成功能
```bash
curl -X POST "http://localhost:8000/api/v1/usage/consume" \
  -H "Authorization: Bearer YOUR_USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "usage_type": "image_generation",
    "count": 1
  }'

# 成功响应
{
  "success": true,
  "message": "Usage recorded against subscription",
  "remaining_credits": 0,
  "remaining_monthly_usage": 39
}
```

## 🔄 订阅延长示例

### 延长现有订阅
如果用户已有活跃订阅，再次调用升级接口会延长订阅时间：

```bash
# 用户当前有30天订阅，再升级30天
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-monthly?user_id=123e4567-e89b-12d3-a456-426614174000&images_per_month=40" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 响应
{
  "subscription": {
    "id": "sub_abc123-def4-5678-9012-345678901234",
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "product_id": "sub_monthly_40",
    "platform": "stripe",
    "start_date": "2025-01-29T10:00:00Z",
    "end_date": "2025-03-30T10:00:00Z",  // 延长了30天
    "is_active": true
  },
  "message": "Subscription extended successfully",
  "is_new_subscription": false
}
```

## 🛠️ 常见使用场景

### 场景1：新用户首次订阅
```bash
# 1. 用户注册后默认为免费状态
# 2. 用户选择月度基础版
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-monthly?user_id=NEW_USER_ID&images_per_month=40" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 场景2：用户升级套餐
```bash
# 1. 用户当前是月度基础版（40张/月）
# 2. 升级到月度高级版（60张/月）
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-monthly?user_id=USER_ID&images_per_month=60" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 场景3：用户续费
```bash
# 1. 用户订阅即将到期
# 2. 续费相同套餐
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-monthly?user_id=USER_ID&images_per_month=40" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

### 场景4：批量升级用户
```bash
# 为多个用户批量升级（需要脚本循环调用）
for user_id in "user1" "user2" "user3"; do
  curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-monthly?user_id=$user_id&images_per_month=40" \
    -H "Authorization: Bearer ADMIN_TOKEN"
done
```

## ⚠️ 注意事项

1. **权限要求**：只有管理员可以升级其他用户，普通用户只能升级自己
2. **订阅叠加**：多次升级会延长订阅时间，不会创建多个订阅
3. **产品切换**：升级到不同产品ID会更新订阅的产品类型
4. **平台标识**：默认使用 Stripe 平台，可根据实际支付方式调整
5. **事务安全**：所有操作都在数据库事务中执行，确保数据一致性

## 🔧 故障排除

### 常见错误及解决方案

1. **403 Forbidden**: 权限不足，确保使用管理员令牌
2. **404 User not found**: 用户ID不存在，检查用户ID是否正确
3. **400 Invalid images_per_month**: 只支持40或60张图片/月
4. **401 Unauthorized**: 令牌无效或过期，重新获取令牌
