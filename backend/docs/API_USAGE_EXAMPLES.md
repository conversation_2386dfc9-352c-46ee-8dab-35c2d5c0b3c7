# 图片生成API使用示例

本文档提供了完整的图片生成API使用示例，包括用户权限检查和使用记录功能。

## 前置条件

1. 用户已登录并获得JWT token
2. 在.env文件中配置了有效的API token
3. 用户有有效的订阅或积分

## API端点概览

| 端点 | 方法 | 描述 | 权限检查 |
|------|------|------|----------|
| `/api/v1/image/generate-image` | POST | 异步图片生成 | ✅ |
| `/api/v1/image/generate-image-sync` | POST | 同步图片生成 | ✅ |
| `/api/v1/image/history` | GET | 获取生成历史 | ✅ |
| `/api/v1/image/history/{id}` | GET | 获取特定记录 | ✅ |
| `/api/v1/image/record-info/{task_id}` | GET | 获取生成记录详情 | ✅ |
| `/api/v1/image/image-generation/health` | GET | 健康检查 | ❌ |

## 1. 检查用户订阅状态

在调用图片生成API之前，建议先检查用户的订阅状态：

```bash
curl -X GET "http://localhost:8000/api/v1/subscriptions/status" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**响应示例**：
```json
{
  "user_id": "123e4567-e89b-12d3-a456-426614174000",
  "has_active_subscription": true,
  "subscription_end_date": "2024-08-29T10:00:00Z",
  "subscription_product_id": "sub_monthly_40",
  "total_credits": 0,
  "monthly_usage_count": 5,
  "monthly_limit": 40,
  "can_use_service": true
}
```

## 2. 同步图片生成

### 基础请求

```bash
curl -X POST "http://localhost:8000/api/v1/image/generate-image-sync" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A beautiful sunset over the mountains",
    "size": "1:1",
    "n_variants": 1
  }'
```

### 高级请求（带参考图片和增强）

```bash
curl -X POST "http://localhost:8000/api/v1/image/generate-image-sync" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "files_url": ["https://example.com/reference1.jpg"],
    "prompt": "Create a landscape inspired by this reference image",
    "size": "16:9",
    "is_enhance": true,
    "n_variants": 2,
    "enable_fallback": true,
    "fallback_model": "FLUX_MAX"
  }'
```

### 成功响应示例

```json
{
  "success": true,
  "message": "Image generation successful. Usage recorded against subscription",
  "data": {
    "task_id": "img_gen_123456",
    "status": "completed",
    "images": [
      {
        "url": "https://generated-image-url.com/image1.jpg",
        "variant": 1
      }
    ],
    "remaining_monthly_usage": 34,
    "remaining_credits": 0,
    "generation_record_id": "987fcdeb-51a2-4567-8901-234567890abc"
  },
  "error": null
}
```

### 权限不足响应示例

```json
{
  "success": false,
  "message": "Monthly usage limit reached",
  "data": {
    "remaining_credits": 0,
    "remaining_monthly_usage": 0,
    "subscription_status": "limit_reached"
  },
  "error": "Monthly usage limit reached"
}
```

## 3. 异步图片生成

异步生成适用于需要回调通知的场景：

```bash
curl -X POST "http://localhost:8000/api/v1/image/generate-image" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A futuristic city with flying cars",
    "size": "16:9",
    "callback_url": "https://your-app.com/api/image-callback",
    "n_variants": 1
  }'
```

## 4. 查看生成历史

### 获取历史记录列表

```bash
curl -X GET "http://localhost:8000/api/v1/image/history?skip=0&limit=10&status=success" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

**查询参数**：
- `skip`: 跳过的记录数（分页）
- `limit`: 返回的记录数（1-100）
- `status`: 状态过滤（pending/success/failed）

**响应示例**：
```json
{
  "data": [
    {
      "id": "987fcdeb-51a2-4567-8901-234567890abc",
      "user_id": "123e4567-e89b-12d3-a456-426614174000",
      "prompt": "A beautiful sunset over the mountains",
      "size": "1:1",
      "files_url": null,
      "callback_url": null,
      "is_enhance": false,
      "n_variants": 1,
      "api_response": "{\"task_id\":\"img_gen_123456\",\"status\":\"completed\"}",
      "status": "success",
      "error_message": null,
      "created_at": "2024-07-29T10:15:30Z"
    }
  ],
  "count": 1
}
```

### 获取特定记录详情

```bash
curl -X GET "http://localhost:8000/api/v1/image/history/987fcdeb-51a2-4567-8901-234567890abc" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 5. 健康检查

检查图片生成服务的配置状态：

```bash
curl -X GET "http://localhost:8000/api/v1/image/image-generation/health"
```

**响应示例**：
```json
{
  "status": "healthy",
  "message": "Image generation service is configured",
  "api_url": "https://api.kie.ai/api/v1/gpt4o-image/generate"
}
```

## 6. 错误处理

### 常见错误响应

#### 权限不足
```json
{
  "detail": "No active subscription or credits available"
}
```

#### 配额用完
```json
{
  "detail": "Monthly usage limit reached"
}
```

#### 积分不足
```json
{
  "detail": "Insufficient credits. Need 5, have 2"
}
```

#### API Token无效
```json
{
  "success": false,
  "message": "API returned error",
  "error": "API error code 401: You do not have access permissions"
}
```

## 7. 使用流程建议

### 完整的调用流程

1. **检查用户状态**
   ```bash
   GET /api/v1/subscriptions/status
   ```

2. **确认可以使用服务**
   ```bash
   GET /api/v1/subscriptions/check-access?usage_type=image_generation
   ```

3. **调用图片生成**
   ```bash
   POST /api/v1/image/generate-image-sync
   ```

4. **查看生成历史**（可选）
   ```bash
   GET /api/v1/image/history
   ```

5. **获取生成记录详情**（可选）
   ```bash
   GET /api/v1/image/record-info/{task_id}
   ```

## 8. 获取生成记录详情

### 基本用法

```bash
curl -X GET "http://localhost:8000/api/v1/image/record-info/2857db2e20044233c72c35cbbb168008" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 成功响应示例

```json
{
  "success": true,
  "message": "Record info fetched successfully",
  "data": {
    "code": 200,
    "msg": "success",
    "data": {
      "taskId": "2857db2e20044233c72c35cbbb168008",
      "paramJson": "{\"enableFallback\":false,\"fallbackModel\":\"FLUX_MAX\",\"fileUrlOrPromptNotEmpty\":true,\"filesUrl\":[\"https://image.wenhaofree.com/2025/07/44e676a8392bb570c2d29b03e263dfbe.jpeg\"],\"isEnhance\":false,\"nVariants\":1,\"nVariantsValid\":true,\"prompt\":\"Elegant traditional wedding dress style, soft lighting, delicate lace details, romantic bouquet, beautiful background, warm atmosphere, premium texture, flowing white veil, classic posing, refined makeup, dreamy glow, natural posture, tranquil mood.\",\"size\":\"1:1\",\"uploadCn\":false}",
      "completeTime": 1753805660000,
      "response": {
        "resultUrls": ["https://tempfile.aiquickdraw.com/s/2857db2e20044233c72c35cbbb168008_0_1753805658_1649.png"]
      },
      "successFlag": 1,
      "status": "SUCCESS",
      "errorCode": null,
      "errorMessage": null,
      "createTime": 1753805480000,
      "progress": "1.00"
    }
  }
}
```

### 错误响应示例

#### 任务不存在
```json
{
  "success": false,
  "message": "API returned error",
  "error": "API error code 404: Task not found",
  "error_code": "API_ERROR",
  "http_status_code": 200
}
```

#### 任务ID为空
```json
{
  "success": false,
  "message": "Task ID is required",
  "error": "Task ID parameter is missing or empty",
  "error_code": "MISSING_TASK_ID",
  "http_status_code": 400
}
```

### Python客户端示例

```python
import requests

class ImageGenerationClient:
    def __init__(self, base_url, jwt_token):
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {jwt_token}",
            "Content-Type": "application/json"
        }
    
    def check_user_status(self):
        """检查用户订阅状态"""
        response = requests.get(
            f"{self.base_url}/api/v1/subscriptions/status",
            headers=self.headers
        )
        return response.json()
    
    def generate_image(self, prompt, size="1:1", **kwargs):
        """生成图片"""
        data = {
            "prompt": prompt,
            "size": size,
            **kwargs
        }
        response = requests.post(
            f"{self.base_url}/api/v1/image/generate-image-sync",
            headers=self.headers,
            json=data
        )
        return response.json()
    
    def get_history(self, skip=0, limit=10, status=None):
        """获取生成历史"""
        params = {"skip": skip, "limit": limit}
        if status:
            params["status"] = status

        response = requests.get(
            f"{self.base_url}/api/v1/image/history",
            headers=self.headers,
            params=params
        )
        return response.json()

    def get_record_info(self, task_id):
        """获取生成记录详情"""
        response = requests.get(
            f"{self.base_url}/api/v1/image/record-info/{task_id}",
            headers=self.headers
        )
        return response.json()

# 使用示例
client = ImageGenerationClient("http://localhost:8000", "your_jwt_token")

# 检查状态
status = client.check_user_status()
print(f"Can use service: {status['can_use_service']}")

# 生成图片
if status['can_use_service']:
    result = client.generate_image(
        prompt="A serene lake surrounded by mountains",
        size="16:9",
        is_enhance=True
    )
    print(f"Generation success: {result['success']}")

# 查看历史
history = client.get_history(limit=5, status="success")
print(f"Found {history['count']} successful generations")

# 获取特定任务的详情（如果有task_id）
if result.get('success') and result.get('data', {}).get('data', {}).get('taskId'):
    task_id = result['data']['data']['taskId']
    record_info = client.get_record_info(task_id)
    if record_info['success']:
        task_data = record_info['data']['data']
        print(f"Task status: {task_data['status']}")
        print(f"Progress: {task_data['progress']}")
        if task_data.get('response', {}).get('resultUrls'):
            print(f"Result URLs: {task_data['response']['resultUrls']}")
    else:
        print(f"Failed to get record info: {record_info['message']}")
```

## 9. 注意事项

1. **权限检查是自动的**：所有图片生成端点都会自动检查用户权限
2. **配额自动扣除**：成功调用后会自动扣除相应的月度配额或积分
3. **记录自动创建**：每次调用都会在数据库中创建记录，无论成功或失败
4. **错误也会记录**：权限不足或API错误都会被记录到数据库中
5. **分页查询**：历史记录支持分页和状态过滤
6. **JWT认证必需**：除了健康检查外，所有端点都需要有效的JWT token
7. **记录详情实时获取**：`/record-info/{task_id}` 端点直接从第三方API获取最新状态，不依赖本地数据库
8. **任务ID来源**：task_id 通常从图片生成API的响应中获取，格式为32位十六进制字符串
