# 订阅积分功能使用示例

## API 调用示例

### 1. 用户订阅月度40次套餐

```bash
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-monthly" \
  -H "Authorization: Bearer <user-jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "123e4567-e89b-12d3-a456-************",
    "images_per_month": 40
  }'
```

**响应:**
```json
{
  "subscription": {
    "id": "sub-uuid",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "product_id": "sub_monthly_40",
    "platform": "stripe",
    "start_date": "2025-01-31T10:00:00Z",
    "end_date": "2025-02-28T10:00:00Z",
    "is_active": true
  },
  "message": "New subscription created successfully. Bonus credits added: 200",
  "is_new_subscription": true
}
```

### 2. 查看用户积分状态

```bash
curl -X POST "http://localhost:8000/api/v1/login/test-token" \
  -H "Authorization: Bearer <user-jwt-token>"
```

**响应:**
```json
{
  "id": "123e4567-e89b-12d3-a456-************",
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "is_active": true,
  "total_credits": 210,      // 试用10积分 + 赠送200积分
  "remaining_credits": 210,  // 全部未使用
  "platform": "web",
  "auth_provider": "email"
}
```

### 3. 查看订阅状态

```bash
curl -X GET "http://localhost:8000/api/v1/subscriptions/status" \
  -H "Authorization: Bearer <user-jwt-token>"
```

**响应:**
```json
{
  "user_id": "123e4567-e89b-12d3-a456-************",
  "has_active_subscription": true,
  "subscription_end_date": "2025-02-28T10:00:00Z",
  "subscription_product_id": "sub_monthly_40",
  "total_credits": 210,
  "monthly_usage_count": 0,
  "monthly_limit": 40,
  "can_use_service": true
}
```

## 数据库记录示例

### 订阅记录 (subscription 表)

```sql
INSERT INTO subscription (
    id, user_id, product_id, platform, start_date, end_date, 
    is_active, original_transaction_id, last_verified_at
) VALUES (
    'sub-uuid',
    '123e4567-e89b-12d3-a456-************',
    'sub_monthly_40',
    'stripe',
    '2025-01-31 10:00:00+00',
    '2025-02-28 10:00:00+00',
    true,
    'admin_upgrade_abc12345',
    '2025-01-31 10:00:00+00'
);
```

### 积分包记录 (creditpackage 表)

```sql
-- 试用积分包
INSERT INTO creditpackage (
    id, user_id, credits, remaining_credits, product_id, platform, purchased_at
) VALUES (
    'trial-credit-uuid',
    '123e4567-e89b-12d3-a456-************',
    10, 10,
    'trial_credits_new_user',
    'stripe',
    '2025-01-30 09:00:00+00'
);

-- 订阅赠送积分包
INSERT INTO creditpackage (
    id, user_id, credits, remaining_credits, product_id, platform, purchased_at
) VALUES (
    'bonus-credit-uuid',
    '123e4567-e89b-12d3-a456-************',
    200, 200,
    'subscription_bonus_sub_monthly_40',
    'stripe',
    '2025-01-31 10:00:00+00'
);
```

## 使用场景演示

### 场景1: 正常使用订阅次数

用户在月度40次限制内使用服务：

1. **第1-40次生成**: 使用订阅配额，不消耗积分
2. **积分余额**: 保持210积分不变
3. **月度使用**: 40/40次

### 场景2: 超出订阅限制使用积分

用户超出月度40次限制后继续使用：

1. **第1-40次生成**: 使用订阅配额
2. **第41次生成**: 消耗5积分 (优先使用试用积分)
   - 试用积分: 10 → 5
   - 总剩余积分: 210 → 205
3. **第42-43次生成**: 继续消耗试用积分
   - 试用积分: 5 → 0 (用完)
   - 总剩余积分: 205 → 195
4. **第44次生成**: 开始使用赠送积分
   - 赠送积分: 200 → 195
   - 总剩余积分: 195 → 190

### 场景3: 续费订阅

用户在现有订阅基础上续费：

```bash
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-monthly" \
  -H "Authorization: Bearer <user-jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "123e4567-e89b-12d3-a456-************",
    "images_per_month": 40
  }'
```

**结果:**
- 订阅延长30天
- 在现有赠送积分包基础上增加200积分
- 总积分: 原有积分 + 200

## 错误处理示例

### 积分创建失败

如果积分包创建过程中出现错误：

**响应:**
```json
{
  "subscription": {
    "id": "sub-uuid",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "product_id": "sub_monthly_40",
    "is_active": true
  },
  "message": "New subscription created successfully (bonus credits creation failed)",
  "is_new_subscription": true
}
```

**日志记录:**
```
ERROR: Failed to create bonus credits for user 123e4567-e89b-12d3-a456-************: Database connection error
```

## 监控查询示例

### 查询用户所有积分包

```sql
SELECT 
    id,
    credits,
    remaining_credits,
    product_id,
    platform,
    purchased_at
FROM creditpackage 
WHERE user_id = '123e4567-e89b-12d3-a456-************'
ORDER BY purchased_at DESC;
```

### 统计赠送积分使用情况

```sql
SELECT 
    product_id,
    COUNT(*) as package_count,
    SUM(credits) as total_credits_given,
    SUM(remaining_credits) as total_credits_remaining,
    SUM(credits - remaining_credits) as total_credits_used
FROM creditpackage 
WHERE product_id LIKE 'subscription_bonus_%'
GROUP BY product_id;
```

### 查询超限使用用户

```sql
-- 查询本月使用积分的用户（即超出订阅限制的用户）
SELECT DISTINCT ur.user_id
FROM usagerecord ur
JOIN creditpackage cp ON ur.user_id = cp.user_id
WHERE ur.used_at >= date_trunc('month', CURRENT_DATE)
  AND cp.product_id LIKE 'subscription_bonus_%'
  AND cp.credits > cp.remaining_credits;
```

## 业务指标计算

### 积分使用率

```sql
SELECT 
    (SUM(credits - remaining_credits)::float / SUM(credits)::float * 100) as usage_rate_percent
FROM creditpackage 
WHERE product_id LIKE 'subscription_bonus_%';
```

### 平均每用户积分余额

```sql
SELECT 
    AVG(remaining_credits) as avg_remaining_credits
FROM creditpackage 
WHERE product_id LIKE 'subscription_bonus_%'
  AND remaining_credits > 0;
```

这些示例展示了订阅积分功能的完整使用流程，从API调用到数据库记录，再到业务监控，为开发和运营提供了全面的参考。