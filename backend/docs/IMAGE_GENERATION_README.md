# 图片生成服务使用指南

本文档介绍如何使用项目中的图片生成功能。

## 📚 相关文档

- 📖 [详细API使用指南](./IMAGE_GENERATION_API_GUIDE.md) - 完整的API参数说明和示例
- 🔧 [故障排除指南](./IMAGE_GENERATION_TROUBLESHOOTING.md) - 常见问题解决方案
- 🚀 [接口重构总结](../API_INTERFACE_OPTIMIZATION_SUMMARY.md) - 最新接口优化说明
- 📊 [数据库优化分析](../IMAGE_GENERATION_OPTIMIZATION_ANALYSIS.md) - 数据库结构优化

## ⚠️ 重要更新

**新接口推荐**: 建议使用新的 `/generate-image` 接口替代 `/generate-image-sync`，新接口提供更清晰的异步处理和状态查询功能。详见 [API使用指南](./IMAGE_GENERATION_API_GUIDE.md)。

## 配置

### 1. 环境变量配置

在项目根目录的 `.env` 文件中添加以下配置：

```env
# Image Generation API Configuration
IMAGE_GENERATION_API_URL=https://api.kie.ai/api/v1/gpt4o-image/generate
IMAGE_GENERATION_API_TOKEN=your_actual_api_token_here
IMAGE_GENERATION_CALLBACK_URL=http://localhost:8000/api/v1/image/callback

# Apple Push Notification Service (APNS) Configuration
APNS_KEY_ID=your_apns_key_id
APNS_TEAM_ID=your_apns_team_id
APNS_BUNDLE_ID=your.app.bundle.id
APNS_KEY_PATH=/path/to/your/AuthKey_XXXXXXXXXX.p8
APNS_USE_SANDBOX=true
```

**重要**: 请将 `your_actual_api_token_here` 替换为您的实际API token。

### 2. 验证配置

运行测试脚本验证配置是否正确：

```bash
cd backend
python test_image_generation.py
```

## API 端点

### 1. 异步图片生成

**端点**: `POST /api/v1/image/generate-image`

**需要认证**: 是

**请求体**:
```json
{
  "files_url": ["https://example.com/image1.png"],  // 可选，参考图片URL列表
  "prompt": "A beautiful sunset over the mountains",  // 必需，图片描述
  "size": "1:1",  // 可选，图片尺寸 (1:1, 16:9, 9:16, 4:3, 3:4)
  "callback_url": "https://your-callback-url.com/callback",  // 可选，回调URL
  "is_enhance": false,  // 可选，是否增强
  "upload_cn": false,  // 可选，是否上传到中国服务器
  "n_variants": 1,  // 可选，生成变体数量
  "enable_fallback": false,  // 可选，是否启用备用模型
  "fallback_model": "FLUX_MAX"  // 可选，备用模型名称
}
```

**响应**:
```json
{
  "success": true,
  "message": "Image generation request submitted successfully",
  "data": {
    // API返回的具体数据
  },
  "error": null
}
```

### 2. 同步图片生成

**端点**: `POST /api/v1/image/generate-image-sync`

**需要认证**: 是

请求和响应格式与异步版本相同。

### 3. 健康检查

**端点**: `GET /api/v1/image/image-generation/health`

**需要认证**: 否

**响应**:
```json
{
  "status": "healthy",
  "message": "Image generation service is configured",
  "api_url": "https://api.kie.ai/api/v1/gpt4o-image/generate"
}
```

### 4. 获取生成记录详情

**端点**: `GET /api/v1/image/record-info/{task_id}`

**需要认证**: 是

**路径参数**:
- `task_id` (string): 图片生成任务的ID

**响应**:
```json
{
  "success": true,
  "message": "Record info fetched successfully",
  "data": {
    "code": 200,
    "msg": "success",
    "data": {
      "taskId": "2857db2e20044233c72c35cbbb168008",
      "paramJson": "{\"enableFallback\":false,\"fallbackModel\":\"FLUX_MAX\",\"fileUrlOrPromptNotEmpty\":true,\"filesUrl\":[\"https://image.wenhaofree.com/2025/07/44e676a8392bb570c2d29b03e263dfbe.jpeg\"],\"isEnhance\":false,\"nVariants\":1,\"nVariantsValid\":true,\"prompt\":\"Elegant traditional wedding dress style, soft lighting, delicate lace details, romantic bouquet, beautiful background, warm atmosphere, premium texture, flowing white veil, classic posing, refined makeup, dreamy glow, natural posture, tranquil mood.\",\"size\":\"1:1\",\"uploadCn\":false}",
      "completeTime": 1753805660000,
      "response": {
        "resultUrls": ["https://tempfile.aiquickdraw.com/s/2857db2e20044233c72c35cbbb168008_0_1753805658_1649.png"]
      },
      "successFlag": 1,
      "status": "SUCCESS",
      "errorCode": null,
      "errorMessage": null,
      "createTime": 1753805480000,
      "progress": "1.00"
    }
  },
  "error": null,
  "error_code": null,
  "http_status_code": null
}
```

**错误响应**:
```json
{
  "success": false,
  "message": "Failed to fetch record info",
  "error": "API error code 404: Task not found",
  "error_code": "API_ERROR",
  "http_status_code": 200,
  "data": null
}
```

### 5. 图片生成回调接收

**端点**: `POST /api/v1/image/callback`

**完整URL路径构成**:
- 基础URL: `http://localhost:8000` (或生产环境域名)
- API版本前缀: `/api/v1` (来自 `settings.API_V1_STR`)
- 图片生成路由前缀: `/image` (在 `app/api/main.py` 中配置)
- 回调端点: `/callback` (在 `image_generation.py` 中定义)
- **完整URL**: `http://localhost:8000/api/v1/image/callback`

**需要认证**: 否（第三方API回调）

**请求体**:
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "taskId": "task12345",
    "info": {
      "result_urls": [
        "https://example.com/result/image1.png"
      ]
    }
  }
}
```

**失败回调**:
```json
{
  "code": 400,
  "msg": "您的内容被 OpenAI 标记为违反内容政策",
  "data": {
    "taskId": "task12345",
    "info": null
  }
}
```

### 6. 设备Token管理

#### 注册设备Token
**端点**: `POST /api/v1/device-tokens/register`

**需要认证**: 是

**请求体**:
```json
{
  "device_token": "your_device_token_here",
  "platform": "ios"
}
```

#### 获取设备Token列表
**端点**: `GET /api/v1/device-tokens/list`

**需要认证**: 是

#### 停用设备Token
**端点**: `DELETE /api/v1/device-tokens/deactivate?device_token=your_token`

**需要认证**: 是

## 使用示例

### Python 代码示例

```python
from app.services.generation_image import ImageGenerationRequest, image_generation_service

# 创建请求
request = ImageGenerationRequest(
    prompt="A serene lake surrounded by mountains at sunset",
    size="16:9",
    n_variants=2,
    is_enhance=True
)

# 同步调用
result = image_generation_service.generate_image_sync(request)

if result.success:
    print("图片生成成功!")
    print(f"数据: {result.data}")
else:
    print(f"生成失败: {result.error}")
```

### cURL 示例

```bash
# 首先获取认证token (假设您已经有了用户认证)
curl -X POST "http://localhost:8000/api/v1/image/generate-image-sync" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A beautiful sunset over the mountains",
    "size": "1:1",
    "n_variants": 1
  }'
```

## 支持的图片尺寸

- `1:1` - 正方形
- `16:9` - 宽屏横向
- `9:16` - 竖屏纵向  
- `4:3` - 标准横向
- `3:4` - 标准纵向

## 错误处理

服务包含完整的错误处理机制：

1. **配置错误**: API token未配置时返回相应错误信息
2. **网络错误**: 请求超时或网络问题时返回详细错误
3. **API错误**: 远程API返回错误时转换为友好的错误信息
4. **验证错误**: 请求参数不正确时返回验证错误

## 日志记录

服务包含详细的日志记录：

- 请求发送前记录请求信息
- 成功响应时记录响应数据
- 错误时记录详细错误信息

日志级别可以通过环境变量 `LOG_LEVEL` 控制。

## 安全注意事项

1. **API Token安全**: 确保API token不会泄露到版本控制系统中
2. **用户认证**: 所有图片生成端点都需要用户认证
3. **请求限制**: 建议在生产环境中添加请求频率限制
4. **输入验证**: 服务对所有输入进行验证，防止恶意请求

## 故障排除

### 常见问题

1. **"API token not configured"**
   - 检查 `.env` 文件中的 `IMAGE_GENERATION_API_TOKEN` 配置
   - 确保token值正确且有效

2. **"Request failed"**
   - 检查网络连接
   - 验证API URL是否正确
   - 确认API服务是否可用

3. **认证失败**
   - 确保请求包含有效的JWT token
   - 检查用户是否已登录且token未过期

### 调试步骤

1. 运行健康检查端点确认服务配置
2. 查看应用日志获取详细错误信息
3. 使用测试脚本验证基本功能
4. 检查网络连接和防火墙设置
