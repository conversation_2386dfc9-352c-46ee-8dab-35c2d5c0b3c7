# 图片生成API错误代码参考

## 概述

图片生成API使用详细的错误代码系统，帮助客户端准确识别和处理不同类型的错误。每个错误代码都有特定的含义和建议的处理方式。

## 错误代码分类

### 成功代码
| 代码 | 名称 | 说明 |
|------|------|------|
| 0 | SUCCESS | 请求成功 |

### 1xxx - 权限和积分相关错误
| 代码 | 名称 | 说明 | HTTP状态码 | 处理建议 |
|------|------|------|-----------|----------|
| 1001 | INSUFFICIENT_CREDITS | 积分不足 | 403 | 引导用户购买积分 |
| 1002 | NO_SUBSCRIPTION | 无有效订阅 | 403 | 引导用户订阅或使用积分 |
| 1003 | MONTHLY_LIMIT_REACHED | 月度限制已达 | 403 | 等待下月或升级订阅 |
| 1004 | PERMISSION_DENIED | 权限被拒绝 | 403 | 检查用户权限 |

### 2xxx - 参数验证错误
| 代码 | 名称 | 说明 | HTTP状态码 | 处理建议 |
|------|------|------|-----------|----------|
| 2001 | INVALID_PARAMETERS | 参数错误 | 422 | 检查请求参数格式 |
| 2002 | INVALID_PROMPT | 提示词无效 | 422 | 修改提示词内容 |
| 2003 | INVALID_SIZE | 尺寸无效 | 422 | 使用支持的尺寸 |
| 2004 | INVALID_VARIANTS | 变体数量无效 | 422 | 调整变体数量(1-10) |

### 3xxx - 第三方API和服务错误
| 代码 | 名称 | 说明 | HTTP状态码 | 处理建议 |
|------|------|------|-----------|----------|
| 3001 | API_TOKEN_ERROR | API令牌错误 | 503 | 联系技术支持 |
| 3002 | API_AUTH_FAILED | API认证失败 | 401 | 稍后重试或联系支持 |
| 3003 | API_REQUEST_FAILED | API请求失败 | 400 | 稍后重试 |
| 3004 | SERVICE_UNAVAILABLE | 服务不可用 | 503 | 稍后重试 |
| 3005 | SERVICE_MAINTENANCE | 服务维护中 | 503 | 等待维护完成 |
| 3006 | QUEUE_FULL | 队列已满 | 503 | 稍后重试 |

### 5xxx - 系统内部错误
| 代码 | 名称 | 说明 | HTTP状态码 | 处理建议 |
|------|------|------|-----------|----------|
| 5001 | INTERNAL_ERROR | 内部错误 | 500 | 联系技术支持 |

### 通用错误
| 代码 | 名称 | 说明 | HTTP状态码 | 处理建议 |
|------|------|------|-----------|----------|
| 1 | GENERAL_ERROR | 通用错误 | 400 | 根据message内容处理 |

## 响应格式示例

### 成功响应
```json
{
  "code": 0,
  "message": "Image generation task submitted successfully",
  "data": {
    "task_id": "img_gen_123456789",
    "generation_record_id": "uuid-string",
    "status": "pending",
    "remaining_credits": 20
  }
}
```

### 积分不足错误
```json
{
  "code": 1001,
  "message": "Insufficient credits. Need 5, have 2",
  "data": {
    "remaining_credits": 2,
    "credits_needed": 5,
    "subscription_status": "inactive"
  }
}
```

### 无订阅错误
```json
{
  "code": 1002,
  "message": "No active subscription or credits available",
  "data": {
    "remaining_credits": 0,
    "remaining_monthly_usage": 0,
    "subscription_status": "inactive"
  }
}
```

### 月度限制错误
```json
{
  "code": 1003,
  "message": "Monthly usage limit reached",
  "data": {
    "monthly_usage_count": 100,
    "monthly_limit": 100,
    "subscription_status": "active"
  }
}
```

### 参数错误
```json
{
  "code": 2003,
  "message": "Invalid image size specified. Supported sizes: 1:1, 16:9, 9:16, 4:3, 3:4",
  "data": {
    "provided_size": "invalid_size",
    "supported_sizes": ["1:1", "16:9", "9:16", "4:3", "3:4"]
  }
}
```

### API认证失败
```json
{
  "code": 3002,
  "message": "API error code 401: You do not have access permissions",
  "data": {
    "code": 401,
    "msg": "You do not have access permissions"
  }
}
```

### 服务维护中
```json
{
  "code": 3005,
  "message": "图片生成服务正在维护中，请稍后重试: Service under maintenance",
  "data": {
    "code": 455,
    "msg": "Service under maintenance"
  }
}
```

## 客户端处理建议

### 1. 错误代码处理逻辑

```javascript
function handleApiResponse(response) {
  const { code, message, data } = response;
  
  switch (code) {
    case 0:
      // 成功处理
      return handleSuccess(data);
      
    case 1001: // 积分不足
      return showCreditsPurchaseDialog(data);
      
    case 1002: // 无订阅
      return showSubscriptionDialog(data);
      
    case 1003: // 月度限制
      return showMonthlyLimitMessage(data);
      
    case 2001:
    case 2002:
    case 2003:
    case 2004: // 参数错误
      return showParameterError(message);
      
    case 3002: // API认证失败
    case 3004: // 服务不可用
    case 3005: // 服务维护
    case 3006: // 队列满
      return showRetryMessage(message);
      
    case 5001: // 内部错误
      return showContactSupportMessage(message);
      
    default:
      return showGenericError(message);
  }
}
```

### 2. 重试策略

```javascript
const RETRY_CODES = [3003, 3004, 3006]; // 可重试的错误代码
const MAX_RETRIES = 3;
const RETRY_DELAY = 2000; // 2秒

async function callApiWithRetry(apiCall, retries = 0) {
  try {
    const response = await apiCall();
    
    if (response.code === 0) {
      return response;
    }
    
    if (RETRY_CODES.includes(response.code) && retries < MAX_RETRIES) {
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
      return callApiWithRetry(apiCall, retries + 1);
    }
    
    throw new Error(`API Error: ${response.code} - ${response.message}`);
    
  } catch (error) {
    if (retries < MAX_RETRIES) {
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
      return callApiWithRetry(apiCall, retries + 1);
    }
    throw error;
  }
}
```

### 3. 用户友好的错误提示

```javascript
const ERROR_MESSAGES = {
  1001: "您的积分不足，请购买积分后继续使用",
  1002: "请订阅服务或购买积分来使用图片生成功能",
  1003: "本月使用次数已达上限，请等待下月重置或升级订阅",
  2002: "提示词包含不当内容，请修改后重试",
  2003: "图片尺寸不支持，请选择其他尺寸",
  3005: "服务正在维护中，请稍后重试",
  3006: "当前请求较多，请稍后重试"
};

function getUserFriendlyMessage(code, originalMessage) {
  return ERROR_MESSAGES[code] || originalMessage;
}
```

## 监控和分析

### 错误统计查询

```sql
-- 按错误代码统计
SELECT 
    CASE 
        WHEN response LIKE '%"code":0%' THEN 'SUCCESS'
        WHEN response LIKE '%"code":1001%' THEN 'INSUFFICIENT_CREDITS'
        WHEN response LIKE '%"code":1002%' THEN 'NO_SUBSCRIPTION'
        WHEN response LIKE '%"code":3002%' THEN 'API_AUTH_FAILED'
        ELSE 'OTHER'
    END as error_type,
    COUNT(*) as count
FROM api_logs 
WHERE endpoint = '/generate-image'
  AND created_at >= NOW() - INTERVAL '24 hours'
GROUP BY error_type
ORDER BY count DESC;
```

### 关键指标

- **成功率**: `SUCCESS / 总请求数`
- **积分不足率**: `INSUFFICIENT_CREDITS / 总请求数`
- **API错误率**: `3xxx错误 / 总请求数`
- **重试成功率**: 重试后成功的请求比例

---

**注意**: 错误代码设计遵循以下原则：
- 0表示成功
- 1xxx表示权限和积分问题
- 2xxx表示参数验证问题  
- 3xxx表示第三方服务问题
- 5xxx表示系统内部问题
- 1表示通用错误（向后兼容）
