# 回调URL路径验证文档

## 🔍 **回调接口URL路径分析**

### **URL构成组件**

回调接口的完整URL由以下几个部分组成：

1. **基础域名**: `http://localhost:8000` (开发环境) 或 `https://your-domain.com` (生产环境)
2. **API版本前缀**: `/api/v1` (来自 `settings.API_V1_STR`)
3. **图片生成路由前缀**: `/image` (在 `app/api/main.py` 中配置)
4. **回调端点路径**: `/callback` (在 `image_generation.py` 中定义)

### **完整URL路径**

```
http://localhost:8000/api/v1/image/callback
```

## 📁 **代码位置追踪**

### 1. **主应用配置** (`app/main.py`)
```python
from app.api.main import api_router
from app.core.config import settings

app.include_router(api_router, prefix=settings.API_V1_STR)
```

### 2. **API路由配置** (`app/api/main.py`)
```python
from app.api.routes import image_generation

api_router.include_router(image_generation.router, prefix="/image", tags=["image"])
```

### 3. **回调端点定义** (`app/api/routes/image_generation.py`)
```python
@router.post("/callback")
async def image_generation_callback(
    callback_data: dict,
    session: SessionDep
) -> Any:
    """接收图片生成回调"""
```

### 4. **配置文件** (`app/core/config.py`)
```python
class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    IMAGE_GENERATION_CALLBACK_URL: str = "http://localhost:8000/api/v1/image/callback"
```

## ✅ **URL路径验证**

### **测试方法1: 健康检查**
```bash
curl -X GET "http://localhost:8000/api/v1/image/image-generation/health"
```

**预期响应**:
```json
{
  "status": "healthy",
  "message": "Image generation service is running"
}
```

### **测试方法2: 回调接口测试**
```bash
curl -X POST "http://localhost:8000/api/v1/image/callback" \
  -H "Content-Type: application/json" \
  -d '{
    "code": 200,
    "msg": "success",
    "data": {
      "taskId": "test-task-123",
      "info": {
        "result_urls": ["https://example.com/test-image.png"]
      }
    }
  }'
```

**预期响应**:
```json
{
  "message": "Callback received successfully"
}
```

或者如果找不到对应的生成记录：
```json
{
  "detail": "Generation record not found for task_id: test-task-123"
}
```

## 🔧 **常见问题排查**

### **问题1: 404 Not Found**
- **原因**: URL路径不正确
- **检查**: 确认API前缀、路由前缀和端点路径
- **解决**: 使用完整路径 `/api/v1/image/callback`

### **问题2: 422 Unprocessable Entity**
- **原因**: 请求数据格式不正确
- **检查**: 确认使用正确的字段名 (`taskId`, `msg`, `result_urls`)
- **解决**: 使用标准的回调数据格式

### **问题3: 500 Internal Server Error**
- **原因**: 服务内部错误
- **检查**: 查看服务器日志
- **解决**: 检查数据库连接和服务配置

## 📋 **回调数据格式规范**

### **成功回调格式**
```json
{
  "code": 200,
  "msg": "success",
  "data": {
    "taskId": "uuid-string",
    "info": {
      "result_urls": [
        "https://example.com/image1.png",
        "https://example.com/image2.png"
      ]
    }
  }
}
```

### **失败回调格式**
```json
{
  "code": 400,
  "msg": "generation failed",
  "data": {
    "taskId": "uuid-string",
    "error": "Error description"
  }
}
```

## 🌐 **环境配置**

### **开发环境**
```bash
IMAGE_GENERATION_CALLBACK_URL=http://localhost:8000/api/v1/image/callback
```

### **生产环境**
```bash
IMAGE_GENERATION_CALLBACK_URL=https://your-domain.com/api/v1/image/callback
```

### **测试环境**
```bash
IMAGE_GENERATION_CALLBACK_URL=https://test.your-domain.com/api/v1/image/callback
```

## 🔍 **调试工具**

### **查看所有路由**
```bash
curl -X GET "http://localhost:8000/docs"
```

### **查看OpenAPI规范**
```bash
curl -X GET "http://localhost:8000/openapi.json"
```

### **日志监控**
```bash
# 查看应用日志
tail -f logs/app.log

# 查看回调相关日志
grep "callback" logs/app.log
```

## ✅ **验证清单**

- [ ] URL路径构成正确: `/api/v1/image/callback`
- [ ] 健康检查端点可访问
- [ ] 回调接口响应正常
- [ ] 回调数据格式符合规范
- [ ] 配置文件中的URL正确
- [ ] 生产环境域名配置正确
- [ ] HTTPS证书配置正确（生产环境）

## 📞 **技术支持**

如果遇到回调URL相关问题，请提供以下信息：
1. 完整的错误信息
2. 请求的URL和数据
3. 服务器响应
4. 相关的日志信息
5. 环境配置信息
