我来详细解释Apple ID登录接口中的`identity_token`参数。

现在让我查看测试文档中的具体示例：

现在让我解码这个测试token来展示其结构：

让我使用在线JWT解码器的方式来展示结构：

## Apple ID登录接口的`identity_token`详解

### 🔍 **什么是`identity_token`**

`identity_token`是Apple Sign In返回的**JWT (JSON Web Token)**，它包含了用户的身份信息。这是Apple官方提供的用户身份验证凭证。

### 📋 **JWT结构分析**

从上面的解码结果可以看到，Apple Identity Token是一个标准的JWT，包含三个部分：

#### **1. Header（头部）**
````json path=JWT Header mode=EXCERPT
{
  "alg": "HS256",    // 签名算法
  "typ": "JWT"       // Token类型
}
````

#### **2. Payload（载荷）- 核心用户信息**
````json path=JWT Payload mode=EXCERPT
{
  "iss": "https://appleid.apple.com",                    // 发行者（Apple）
  "aud": "com.yourapp.service",                          // 受众（你的应用ID）
  "exp": 1753782827,                                     // 过期时间戳
  "iat": 1753779227,                                     // 签发时间戳
  "sub": "001234.567890abcdef12345678901234567890abcdef", // 用户唯一标识符
  "email": "<EMAIL>",        // 用户邮箱
  "email_verified": true,                                // 邮箱是否已验证
  "is_private_email": true,                              // 是否使用隐私邮箱
  "real_user_status": 2,                                 // 真实用户状态
  "name": {                                              // 用户姓名（可选）
    "firstName": "Test",
    "lastName": "User"
  }
}
````

#### **3. Signature（签名）**
用于验证Token的完整性和真实性。

### 🛠️ **如何构建`identity_token`参数**

#### **方法1: 客户端SDK获取（推荐）**

**iOS客户端示例：**
```swift
import AuthenticationServices

func signInWithApple() {
    let request = ASAuthorizationAppleIDProvider().createRequest()
    request.requestedScopes = [.fullName, .email]
    
    let authorizationController = ASAuthorizationController(authorizationRequests: [request])
    authorizationController.delegate = self
    authorizationController.presentationContextProvider = self
    authorizationController.performRequests()
}

// 获取identity_token
func authorizationController(controller: ASAuthorizationController, 
                           didCompleteWithAuthorization authorization: ASAuthorization) {
    if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
        let identityToken = appleIDCredential.identityToken
        let identityTokenString = String(data: identityToken!, encoding: .utf8)!
        
        // 发送到后端
        sendToBackend(identityToken: identityTokenString)
    }
}
```

**JavaScript/Web客户端示例：**
```javascript
// 配置Apple Sign In
AppleID.auth.init({
    clientId: 'com.yourapp.service',
    scope: 'name email',
    redirectURI: 'https://yourapp.com/callback',
    usePopup: true
});

// 执行登录
AppleID.auth.signIn().then((data) => {
    const identityToken = data.authorization.id_token;
    
    // 发送到后端
    fetch('/api/v1/oauth/apple/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            identity_token: identityToken,
            platform: 'web'
        })
    });
});
```

#### **方法2: 测试环境手动构建**

````python path=backend/app/services/oauth_service.py mode=EXCERPT
# 在代码中可以看到，开发环境跳过了签名验证
decoded_token = jwt.decode(
    identity_token,
    options={"verify_signature": False, "verify_exp": False}
)
````

**测试Token构建规则：**

1. **Header**: 固定格式
```json
{
  "alg": "HS256",
  "typ": "JWT"
}
```

2. **Payload**: 必须包含以下字段
```json
{
  "iss": "https://appleid.apple.com",           // 必须是Apple的域名
  "aud": "com.yourapp.service",                 // 你的应用Bundle ID
  "exp": 1753782827,                            // 过期时间（未来时间戳）
  "iat": 1753779227,                            // 签发时间（当前时间戳）
  "sub": "001234.567890abcdef...",               // 用户唯一ID（Apple格式）
  "email": "<EMAIL>",     // 用户邮箱
  "email_verified": true,                       // 必须为true
  "is_private_email": true,                     // 可选
  "real_user_status": 2,                        // 可选
  "name": {                                     // 可选
    "firstName": "Test",
    "lastName": "User"
  }
}
```

3. **使用在线JWT生成器**：
   - 访问 https://jwt.io
   - 输入Header和Payload
   - 生成JWT Token

### 🔧 **后端验证流程**

````python path=backend/app/services/oauth_service.py mode=EXCERPT
async def _verify_apple_token(self, identity_token: str):
    # 1. 解码JWT
    decoded_token = jwt.decode(identity_token, options={"verify_signature": False})
    
    # 2. 验证必要字段
    subject = decoded_token.get('sub')          # Apple用户ID
    email = decoded_token.get('email')          # 用户邮箱
    email_verified = decoded_token.get('email_verified', False)
    
    # 3. 提取用户信息
    name_info = decoded_token.get('name', {})
    full_name = f"{name_info.get('firstName', '')} {name_info.get('lastName', '')}".strip()
    
    # 4. 构建用户信息对象
    user_info = OAuthUserInfo(
        provider=AuthProviderEnum.apple,
        provider_user_id=subject,
        email=email,
        full_name=full_name,
        avatar_url=None
    )
````

### 📝 **API调用示例**

**请求格式：**
```bash
curl -X POST "http://localhost:8000/api/v1/oauth/apple/login" \
  -H "Content-Type: application/json" \
  -d '{
    "identity_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "platform": "ios"
  }'
```

**响应格式：**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "user": {
    "id": "51a8a41b-070e-45c7-8188-008c768261a7",
    "email": "<EMAIL>",
    "full_name": "Test User",
    "auth_provider": "apple",
    "provider_user_id": "001234.567890abcdef..."
  },
  "is_new_user": true
}
```

### ⚠️ **重要注意事项**

1. **生产环境**：必须验证JWT签名，需要从Apple获取公钥
2. **测试环境**：当前代码跳过签名验证，仅用于开发测试
3. **Token有效期**：Apple Identity Token通常有效期为10分钟
4. **隐私邮箱**：Apple可能提供隐私中继邮箱地址
5. **用户信息**：首次登录时才提供姓名信息，后续登录可能为空

### 🔒 **安全建议**

1. **验证issuer**: 确保`iss`字段为`https://appleid.apple.com`
2. **验证audience**: 确保`aud`字段匹配你的应用ID
3. **验证过期时间**: 检查`exp`字段确保Token未过期
4. **验证签名**: 生产环境必须验证JWT签名
5. **HTTPS传输**: 确保Token通过HTTPS传输

这样构建的`identity_token`就可以成功调用Apple ID登录接口了！
