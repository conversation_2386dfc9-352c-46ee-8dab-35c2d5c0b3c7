# 项目结构说明
# Project Structure Documentation

本文档描述了婚纱照片处理系统的项目结构和文件组织。

## 📁 目录结构

```
backend/
├── 📁 app/                          # 主应用代码
│   ├── 📁 api/                      # API路由
│   ├── 📁 core/                     # 核心功能（配置、数据库、安全）
│   ├── 📁 services/                 # 业务逻辑服务
│   ├── 📁 email-templates/          # 邮件模板
│   ├── 📁 tests/                    # 单元测试
│   ├── 📄 models.py                 # 数据模型定义
│   ├── 📄 crud.py                   # 数据库操作
│   ├── 📄 main.py                   # FastAPI应用入口
│   └── 📄 utils.py                  # 工具函数
│
├── 📁 docs/                         # 📚 项目文档
│   ├── 📄 README.md                 # 文档总览
│   ├── 📄 API_*.md                  # API相关文档
│   ├── 📄 IMAGE_*.md                # 图片处理文档
│   ├── 📄 CALLBACK_*.md             # 回调功能文档
│   ├── 📄 OAUTH_*.md                # OAuth登录文档
│   ├── 📄 SUBSCRIPTION_*.md         # 订阅系统文档
│   ├── 📄 apns_config_sample.env    # APNS配置示例
│   └── 📄 *.md                      # 其他技术文档
│
├── 📁 test/                         # 🧪 测试文件
│   ├── 📄 test_*.py                 # 功能测试
│   ├── 📄 example_*.py              # 示例代码
│   ├── 📄 *_script.py               # 工具脚本
│   └── 📄 setup_*.py                # 设置脚本
│
├── 📁 scripts/                      # 🔧 开发脚本
│   ├── 📄 format.sh                 # 代码格式化
│   ├── 📄 lint.sh                   # 代码检查
│   ├── 📄 test.sh                   # 测试运行
│   └── 📄 prestart.sh               # 启动前脚本
│
├── 📁 alembic/                      # 🗃️ 数据库迁移
│   ├── 📁 versions/                 # 迁移版本文件
│   └── 📄 env.py                    # Alembic配置
│
├── 📄 pyproject.toml                # Python项目配置
├── 📄 alembic.ini                   # Alembic配置文件
├── 📄 Dockerfile                    # Docker构建文件
├── 📄 README.md                     # 项目说明（中文）
├── 📄 README-zh.md                  # 项目说明（英文）
└── 📄 uv.lock                       # 依赖锁定文件
```

## 📚 文档分类

### API 文档
- `API_RESPONSE_STANDARDS.md` - API响应标准
- `API_USAGE_EXAMPLES.md` - API使用示例
- `API_INTERFACE_OPTIMIZATION_SUMMARY.md` - API接口优化总结

### 图片处理文档
- `IMAGE_GENERATION_API_GUIDE.md` - 图片生成API指南
- `IMAGE_GENERATION_README.md` - 图片生成功能说明
- `IMAGE_GENERATION_TROUBLESHOOTING.md` - 图片生成故障排除
- `IMAGE_UPLOAD_R2_README.md` - 图片上传R2存储说明

### 认证与登录文档
- `LOGIN.md` - 登录功能说明
- `OAUTH_TEST_CASES.md` - OAuth测试用例
- `APPLE_LOGIN_FIX_SUMMARY.md` - Apple登录修复总结
- `APPLE_LOGIN_ENHANCED_SUMMARY.md` - Apple登录增强总结

### 回调功能文档
- `CALLBACK_AND_PUSH_FEATURE.md` - 回调和推送功能
- `CALLBACK_OPTIMIZATION_SUMMARY.md` - 回调优化总结
- `CALLBACK_URL_VERIFICATION.md` - 回调URL验证

### 订阅系统文档
- `SUBSCRIPTION_UPGRADE_EXAMPLES.md` - 订阅升级示例

### 推送通知文档
- `APNS_FIX_SUMMARY.md` - APNS修复总结
- `APNS_VERIFICATION_GUIDE.md` - APNS验证指南
- `DEVICE_TOKEN_INTEGRATION_SUMMARY.md` - 设备Token集成总结

### 技术实现文档
- `CLOUDFLARE_R2_IMPLEMENTATION_SUMMARY.md` - Cloudflare R2实现总结
- `COLORED_LOGGING_IMPLEMENTATION.md` - 彩色日志实现
- `ERROR_CODES_REFERENCE.md` - 错误代码参考
- `STANDARD_RESPONSE_FORMAT_FIX.md` - 标准响应格式修复

## 🧪 测试文件分类

### 功能测试
- `test_api_*.py` - API测试
- `test_image_*.py` - 图片处理测试
- `test_callback_*.py` - 回调功能测试
- `test_oauth_*.py` - OAuth登录测试
- `test_subscription_*.py` - 订阅系统测试

### 工具脚本
- `add_trial_credits_script.py` - 添加试用积分脚本
- `check_apns_config.py` - APNS配置检查
- `setup_apns_test.py` - APNS测试设置

### 示例代码
- `example_image_generation.py` - 图片生成示例

## 🔧 开发工具

### 代码质量
- `scripts/format.sh` - 代码格式化（使用ruff）
- `scripts/lint.sh` - 代码检查
- `scripts/test.sh` - 运行测试

### 部署相关
- `scripts/prestart.sh` - 启动前检查
- `Dockerfile` - 容器化部署
- `pyproject.toml` - 项目依赖和配置

## 📋 文件移动记录

最近的文件整理操作：

### 移动到 docs/ 目录
- 所有 `*.md` 文档文件
- `apns_config_sample.env` 配置示例
- `third_party_api_status_mapping.md` 第三方API状态映射

### 移动到 test/ 目录
- 所有 `test_*.py` 测试文件
- 工具脚本：`add_trial_credits_script.py`, `check_apns_config.py`, `setup_apns_test.py`
- 示例文件：`example_*.py`

## 🎯 使用建议

### 开发者
1. 查看 `docs/README.md` 了解项目概况
2. 参考 `docs/API_*.md` 了解API使用
3. 运行 `test/` 目录下的测试确保功能正常

### 部署人员
1. 查看 `docs/` 目录下的部署相关文档
2. 使用 `scripts/` 目录下的脚本进行部署
3. 参考配置示例文件进行环境配置

### 测试人员
1. 使用 `test/` 目录下的测试文件
2. 参考 `docs/` 目录下的测试用例文档
3. 运行 `scripts/test.sh` 执行完整测试

---

**注意**: 此文档会随着项目结构的变化而更新，请定期查看最新版本。
