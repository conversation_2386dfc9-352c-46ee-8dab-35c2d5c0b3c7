# Cloudflare R2 图片上传服务

本文档介绍如何使用Cloudflare R2图片上传服务。

## 🚀 功能特性

- ✅ **多种上传方式**: 支持文件上传、URL下载上传、字节数据上传
- ✅ **自动类型检测**: 自动检测图片格式和内容类型
- ✅ **安全认证**: 使用AWS S3兼容的签名认证
- ✅ **完整日志**: 详细的操作日志记录
- ✅ **错误处理**: 完善的错误处理和返回信息
- ✅ **便捷函数**: 提供简化的便捷调用函数
- ✅ **删除功能**: 支持删除已上传的图片

## 📋 环境配置

### 1. 环境变量设置

在 `.env` 文件中添加以下配置：

```env
# Cloudflare R2 Configuration
R2_ACCOUNT_ID=your_account_id
R2_ACCESS_KEY_ID=your_access_key_id
R2_SECRET_ACCESS_KEY=your_secret_access_key
R2_BUCKET=your_bucket_name
R2_PUBLIC_DOMAIN=your_custom_domain  # 可选，如果没有则使用默认R2域名
```

### 2. 获取R2配置信息

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 进入 R2 Object Storage
3. 创建存储桶或选择现有存储桶
4. 在 "Manage R2 API tokens" 中创建API令牌
5. 获取Account ID、Access Key ID和Secret Access Key

## 🔧 API 端点

### 1. 上传图片文件

```http
POST /api/v1/upload/upload
Content-Type: multipart/form-data

file: <图片文件>
folder: uploads  # 可选，默认为"uploads"
custom_name: my_image.jpg  # 可选，自定义文件名
```

**响应示例:**
```json
{
  "success": true,
  "message": "Image uploaded successfully",
  "url": "https://pub-xxx.r2.dev/uploads/my_image.jpg",
  "object_name": "uploads/my_image.jpg",
  "data": {
    "object_name": "uploads/my_image.jpg",
    "content_type": "image/jpeg",
    "size": 12345,
    "bucket": "my-bucket"
  }
}
```

### 2. 从URL上传图片

```http
POST /api/v1/upload/upload-from-url
Content-Type: application/json

{
  "image_url": "https://example.com/image.jpg",
  "file_name": "downloaded_image.jpg",  // 可选
  "folder": "downloads",  // 可选，默认为"uploads"
  "content_type": "image/jpeg"  // 可选
}
```

### 3. 删除图片

```http
DELETE /api/v1/upload/delete
Content-Type: application/json

{
  "object_name": "uploads/my_image.jpg"
}
```

### 4. 测试配置

```http
GET /api/v1/upload/test-config
```

## 💻 代码使用示例

### 1. 服务类使用

```python
from app.services.image_upload_r2 import CloudflareR2Service, ImageUploadRequest

# 初始化服务
service = CloudflareR2Service()

# 上传字节数据
with open('image.jpg', 'rb') as f:
    image_data = f.read()

request = ImageUploadRequest(
    file_name="my_image.jpg",
    folder="uploads",
    content_type="image/jpeg"
)

result = service.upload_image_from_bytes(image_data, request)
if result.success:
    print(f"上传成功: {result.url}")
else:
    print(f"上传失败: {result.error}")
```

### 2. 便捷函数使用

```python
from app.services.image_upload_r2 import upload_image_bytes, upload_image_url, upload_image_file

# 上传字节数据
result = upload_image_bytes(image_data, file_name="test.jpg")

# 从URL上传
result = upload_image_url("https://example.com/image.jpg")

# 从文件路径上传
result = upload_image_file("/path/to/image.jpg")
```

## 🧪 测试

### 1. 运行测试脚本

```bash
# 测试配置
python test_image_upload_r2.py test-config

# 测试字节上传
python test_image_upload_r2.py test-bytes

# 测试URL上传
python test_image_upload_r2.py test-url

# 测试文件上传
python test_image_upload_r2.py test-file /path/to/image.jpg

# 测试删除
python test_image_upload_r2.py test-delete uploads/test_image.jpg
```

### 2. API测试

使用curl测试API端点：

```bash
# 测试配置
curl -X GET "http://localhost:8000/api/v1/upload/test-config" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 上传文件
curl -X POST "http://localhost:8000/api/v1/upload/upload" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@image.jpg" \
  -F "folder=test"

# 从URL上传
curl -X POST "http://localhost:8000/api/v1/upload/upload-from-url" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"image_url": "https://httpbin.org/image/jpeg", "folder": "test"}'
```

## 📝 支持的图片格式

- JPEG (.jpg, .jpeg)
- PNG (.png)
- GIF (.gif)
- WebP (.webp)
- BMP (.bmp)

## ⚠️ 注意事项

1. **文件大小限制**: 默认限制为10MB
2. **权限要求**: 需要用户登录认证
3. **配置检查**: 使用前请确保R2配置正确
4. **网络超时**: 上传和下载操作有30秒超时限制
5. **错误处理**: 所有操作都有完整的错误处理和日志记录

## 🔍 日志记录

服务会记录以下关键操作：

- 服务初始化状态
- 上传请求详情（用户ID、文件信息等）
- 上传结果（成功URL或失败原因）
- 删除操作结果
- 错误信息和异常堆栈

日志级别为INFO，可以通过调整logging配置来控制输出详细程度。

## 🛠️ 故障排除

### 常见问题

1. **配置错误**: 检查环境变量是否正确设置
2. **权限问题**: 确认R2 API令牌有足够权限
3. **网络问题**: 检查网络连接和防火墙设置
4. **文件格式**: 确认上传的是支持的图片格式

### 调试方法

1. 使用 `test-config` 命令检查配置
2. 查看应用日志获取详细错误信息
3. 使用测试脚本逐步验证功能
4. 检查Cloudflare R2控制台的操作日志
