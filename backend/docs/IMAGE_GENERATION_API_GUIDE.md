# 图片生成 API 使用指南

## 目录

- [接口概述](#接口概述)
- [接口详情](#接口详情)
  - [提交图片生成任务](#提交图片生成任务)
  - [查询任务状态](#查询任务状态)
  - [获取历史记录](#获取历史记录)
- [权限与积分](#权限与积分)
  - [权限检查机制](#权限检查机制)
  - [积分消耗规则](#积分消耗规则)
  - [试用积分管理](#试用积分管理)
- [常见问题与解决方案](#常见问题与解决方案)
- [最佳实践](#最佳实践)

## 接口概述

图片生成服务提供了一套完整的API，用于生成AI图片、查询任务状态和管理历史记录。所有接口都采用标准的RESTful设计，并使用统一的响应格式。

### 基本流程

1. 调用 `/generate-image` 提交图片生成任务，获取 `task_id`
2. 通过 `/generation-status/{task_id}` 查询任务状态
3. 当任务完成后，获取生成的图片URL

### 响应格式

所有接口都使用统一的标准响应格式：

```json
{
  "code": 0,                // 业务状态码，0表示成功，非0表示错误
  "message": "success",     // 提示信息
  "data": { ... }           // 具体返回数据
}
```

## 接口详情

### 提交图片生成任务

**接口路径**: `POST /api/v1/image/generate-image`

**需要认证**: 是

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|-------|------|------|------|--------|
| prompt | string | 是 | 图片描述 | "A beautiful sunset" |
| size | string | 否 | 图片尺寸 | "1:1", "16:9", "9:16", "4:3", "3:4" |
| n_variants | integer | 否 | 生成变体数量 | 1-10 |
| callback_url | string | 否 | 回调URL | "https://your-app.com/callback" |
| is_enhance | boolean | 否 | 是否增强 | false |
| upload_cn | boolean | 否 | 是否上传到中国服务器 | false |
| files_url | array | 否 | 参考图片URL列表 | ["https://example.com/image.png"] |
| enable_fallback | boolean | 否 | 是否启用备用模型 | false |
| fallback_model | string | 否 | 备用模型名称 | "FLUX_MAX" |

**请求示例**:

```bash
curl --location --request POST 'http://127.0.0.1:8000/api/v1/image/generate-image' \
--header 'Authorization: Bearer YOUR_TOKEN' \
--header 'Content-Type: application/json' \
--data-raw '{
  "prompt": "A beautiful sunset",
  "size": "1:1",
  "n_variants": 1,
  "callback_url": "https://your-app.com/callback"
}'
```

**成功响应**:

```json
{
  "code": 0,
  "message": "Image generation task submitted successfully",
  "data": {
    "task_id": "img_gen_123456789",
    "generation_record_id": "uuid-string",
    "status": "pending",
    "estimated_completion_time": "30-60 seconds",
    "remaining_credits": 45,
    "remaining_monthly_usage": 35,
    "callback_url": "https://your-app.com/callback",
    "subscription_status": "active"
  }
}
```

**错误响应**:

```json
{
  "code": 1002,
  "message": "No active subscription or credits available",
  "data": {
    "remaining_credits": 0,
    "remaining_monthly_usage": 0,
    "subscription_status": "inactive"
  }
}
```

**常见错误代码**:
- `1001`: 积分不足
- `1002`: 无有效订阅
- `1003`: 月度限制已达
- `2001-2004`: 参数验证错误
- `3001-3006`: 第三方API和服务错误
- `5001`: 系统内部错误

详细的错误代码说明请参考 [错误代码参考文档](./ERROR_CODES_REFERENCE.md)。

### 查询任务状态

**接口路径**: `GET /api/v1/image/generation-status/{task_id}`

**需要认证**: 是

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|-------|------|------|------|--------|
| task_id | string | 是 | 任务ID | "img_gen_123456789" |

**请求示例**:

```bash
curl --location --request GET 'http://127.0.0.1:8000/api/v1/image/generation-status/img_gen_123456789' \
--header 'Authorization: Bearer YOUR_TOKEN'
```

**成功响应** (任务完成):

```json
{
  "code": 0,
  "message": "Task completed successfully",
  "data": {
    "task_id": "img_gen_123456789",
    "status": "completed",
    "result_urls": ["https://example.com/image1.jpg"],
    "result_url": "https://example.com/image1.jpg",
    "completed_at": "2025-01-31T12:00:00Z"
  }
}
```

**成功响应** (任务进行中):

```json
{
  "code": 0,
  "message": "Task is in progress",
  "data": {
    "task_id": "img_gen_123456789",
    "status": "pending",
    "progress": 50,
    "estimated_remaining_time": "15-30 seconds"
  }
}
```

### 获取历史记录

**接口路径**: `GET /api/v1/image/history`

**需要认证**: 是

**查询参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|-------|------|------|------|--------|
| skip | integer | 否 | 跳过记录数 | 0 |
| limit | integer | 否 | 返回记录数 | 20 |
| status | string | 否 | 状态过滤 | "pending", "success", "failed" |

**请求示例**:

```bash
curl --location --request GET 'http://127.0.0.1:8000/api/v1/image/history?skip=0&limit=20&status=success' \
--header 'Authorization: Bearer YOUR_TOKEN'
```

**成功响应**:

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "records": [
      {
        "id": "uuid-string",
        "task_id": "img_gen_123456789",
        "prompt": "A beautiful sunset",
        "size": "1:1",
        "status": "success",
        "created_at": "2025-01-31T12:00:00Z",
        "result_url": "https://example.com/image1.jpg"
      }
    ],
    "pagination": {
      "total": 100,
      "count": 20,
      "skip": 0,
      "limit": 20,
      "has_more": true
    },
    "summary": {
      "total_generations": 100,
      "successful": 85,
      "failed": 10,
      "pending": 5
    }
  }
}
```

## 权限与积分

### 权限检查机制

图片生成服务需要用户拥有有效的订阅或足够的积分。系统会在每次请求时进行权限检查：

1. 检查用户是否有有效订阅
2. 检查用户是否有足够的积分
3. 检查用户是否达到月度使用限制

只有满足以下条件之一，用户才能使用图片生成服务：
- 有有效订阅且未达到月度使用限制
- 有足够的积分

### 积分消耗规则

每次生成图片会消耗积分，具体规则如下：

- 基础图片生成：5积分/张
- 增强图片生成：10积分/张
- 多变体生成：N × 基础积分（N为变体数量）

### 试用积分管理

新用户可以获得试用积分，用于体验图片生成服务。

**检查用户积分状态**:

```bash
curl --location --request GET 'http://127.0.0.1:8000/api/v1/subscriptions/status' \
--header 'Authorization: Bearer YOUR_TOKEN'
```

**检查用户访问权限**:

```bash
curl --location --request GET 'http://127.0.0.1:8000/api/v1/subscriptions/check-access' \
--header 'Authorization: Bearer YOUR_TOKEN'
```

**为用户添加试用积分**:

可以使用提供的脚本为用户添加试用积分：

```bash
cd backend

# 检查用户当前状态
uv run python add_trial_credits_script.py --user-id USER_UUID --action check-status

# 为新用户添加试用积分
uv run python add_trial_credits_script.py --user-id USER_UUID --action add-trial

# 为已有用户手动添加积分（如果试用积分已用完）
uv run python add_trial_credits_script.py --user-id USER_UUID --action add-credits --credits 25
```

## 常见问题与解决方案

### 1. "No active subscription or credits available"

**问题**: 用户没有有效订阅或积分

**解决方案**:
1. 检查用户订阅状态
2. 为用户添加试用积分
3. 引导用户购买积分或订阅

```bash
# 检查用户状态
python add_trial_credits_script.py --user-id USER_UUID --action check-status

# 添加试用积分
python add_trial_credits_script.py --user-id USER_UUID --action add-trial
```

### 2. "Monthly usage limit reached"

**问题**: 用户已达到月度使用限制

**解决方案**:
1. 等待下个月度周期
2. 升级到更高级别的订阅
3. 使用积分而非订阅配额

### 3. "API token not configured"

**问题**: 服务器未配置API令牌

**解决方案**:
1. 检查环境变量 `IMAGE_GENERATION_API_TOKEN` 是否正确设置
2. 重启服务以加载新的环境变量

### 4. 回调URL未收到通知

**问题**: 生成完成后未收到回调通知

**解决方案**:
1. 确保回调URL可以从公网访问
2. 检查回调URL格式是否正确
3. 使用状态查询接口主动获取结果

## 最佳实践

### 1. 异步处理

图片生成是异步过程，建议：
- 不要在UI上阻塞等待结果
- 实现轮询或使用WebSocket获取实时状态
- 提供进度指示器提升用户体验

### 2. 错误处理

- 实现完善的错误处理逻辑
- 对不同错误类型提供不同的用户提示
- 在权限不足时引导用户购买积分或订阅

### 3. 资源管理

- 定期清理不再需要的历史记录
- 实现图片缓存机制减少重复生成
- 监控用户积分使用情况，及时提醒用户

### 4. 安全性

- 不要在客户端存储API令牌
- 实现请求频率限制防止滥用
- 对敏感提示内容进行过滤

---

## 附录: 第三方API状态码映射

| 第三方状态码 | HTTP状态码 | 错误消息 |
|-------------|-----------|----------|
| 200 | 200 | 成功 |
| 400 | 400 | 图片生成请求格式错误，请检查参数格式 |
| 401 | 401 | 图片生成服务认证失败，请稍后重试 |
| 402 | 402 | 图片生成积分不足，请充值后重试 |
| 404 | 404 | 图片生成服务端点不存在 |
| 422 | 422 | 图片生成参数验证失败，请检查输入参数 |
| 429 | 429 | 图片生成请求过于频繁，请稍后重试 |
| 455 | 503 | 图片生成服务正在维护中，请稍后重试 |
| 500 | 500 | 图片生成服务内部错误，请稍后重试 |
| 550 | 503 | 图片生成服务队列已满，请稍后重试 |
