# generate-image-sync 接口标准返回规范

## 接口概述

**端点**: `POST /api/v1/image/generate-image-sync`

**功能**: 同步生成图片，自动检查用户权限和消耗配额

## 标准返回格式

### 成功响应 (HTTP 200)

```json
{
  "success": true,
  "message": "Image generation successful. Usage recorded against subscription",
  "data": {
    "task_id": "img_gen_123456789",
    "images": [
      {
        "url": "https://generated-image-url.com/image1.jpg",
        "variant": 1,
        "size": "1:1"
      }
    ],
    "remaining_credits": 45,
    "remaining_monthly_usage": 35,
    "subscription_status": "active",
    "generation_record_id": "987fcdeb-51a2-4567-8901-234567890abc",
    "trial_message": null,
    "is_trial_user": false,
    "processing_time_ms": 3500
  },
  "error": null
}
```

### 错误响应格式

所有错误响应都包含以下字段：

```json
{
  "error_code": "ERROR_CODE_HERE",
  "message": "Human readable error message",
  "data": {
    "remaining_credits": 0,
    "remaining_monthly_usage": 0,
    "subscription_status": "inactive"
  },
  "success": false
}
```

## 详细状态码和错误类型

### 1. 权限相关错误 (HTTP 403 Forbidden)

#### 1.1 无订阅且积分不足
```json
{
  "error_code": "NO_SUBSCRIPTION_OR_CREDITS",
  "message": "You don't have an active subscription or sufficient credits to generate images",
  "data": {
    "remaining_credits": 0,
    "remaining_monthly_usage": 0,
    "subscription_status": "inactive",
    "suggestion": "Please subscribe to a plan or purchase credits to continue"
  },
  "success": false
}
```

#### 1.2 积分不足
```json
{
  "error_code": "INSUFFICIENT_CREDITS",
  "message": "Insufficient credits to generate image. Need 5, have 2",
  "data": {
    "remaining_credits": 2,
    "credits_needed": 5,
    "remaining_monthly_usage": 0,
    "subscription_status": "inactive",
    "suggestion": "Please purchase more credits or upgrade your subscription"
  },
  "success": false
}
```

#### 1.3 月度次数用完
```json
{
  "error_code": "MONTHLY_LIMIT_REACHED",
  "message": "You have reached your monthly generation limit of 40 images",
  "data": {
    "remaining_credits": 0,
    "remaining_monthly_usage": 0,
    "monthly_limit": 40,
    "subscription_status": "limit_reached",
    "suggestion": "Please wait for next month or upgrade to a higher plan"
  },
  "success": false
}
```

### 2. 服务不可用错误 (HTTP 503 Service Unavailable)

#### 2.1 API Token未配置
```json
{
  "error_code": "API_TOKEN_NOT_CONFIGURED",
  "message": "Image generation service is temporarily unavailable due to configuration issues",
  "data": {
    "suggestion": "Please try again later or contact support"
  },
  "success": false
}
```

### 3. API调用错误 (HTTP 400 Bad Request)

#### 3.1 API认证失败
```json
{
  "error_code": "API_AUTHENTICATION_FAILED",
  "message": "Failed to authenticate with image generation service",
  "data": {
    "api_status_code": 401,
    "api_message": "You do not have access permissions",
    "suggestion": "This is a temporary issue. Please try again in a few minutes"
  },
  "success": false
}
```

#### 3.2 API请求失败
```json
{
  "error_code": "API_REQUEST_FAILED",
  "message": "Image generation request failed: Invalid prompt format",
  "data": {
    "api_status_code": 400,
    "api_error": "Prompt contains prohibited content",
    "suggestion": "Please check your request parameters and try again"
  },
  "success": false
}
```

### 4. 参数验证错误 (HTTP 422 Unprocessable Entity)

#### 4.1 无效提示词
```json
{
  "error_code": "INVALID_PROMPT",
  "message": "Invalid or empty prompt provided",
  "data": {
    "field": "prompt",
    "value": "",
    "suggestion": "Please provide a valid text description for image generation"
  },
  "success": false
}
```

#### 4.2 无效尺寸
```json
{
  "error_code": "INVALID_SIZE",
  "message": "Invalid image size specified. Supported sizes: 1:1, 16:9, 9:16, 4:3, 3:4",
  "data": {
    "field": "size",
    "value": "invalid_size",
    "supported_sizes": ["1:1", "16:9", "9:16", "4:3", "3:4"],
    "suggestion": "Please use one of the supported aspect ratios"
  },
  "success": false
}
```

#### 4.3 无效变体数量
```json
{
  "error_code": "INVALID_VARIANTS_COUNT",
  "message": "Invalid number of variants. Must be between 1 and 10",
  "data": {
    "field": "n_variants",
    "value": 15,
    "min": 1,
    "max": 10,
    "suggestion": "Please specify a valid number of image variants to generate"
  },
  "success": false
}
```

### 5. 系统错误 (HTTP 500 Internal Server Error)

#### 5.1 数据库错误
```json
{
  "error_code": "DATABASE_ERROR",
  "message": "Database operation failed",
  "data": {
    "suggestion": "This is a temporary issue. Please try again"
  },
  "success": false
}
```

#### 5.2 内部错误
```json
{
  "error_code": "INTERNAL_ERROR",
  "message": "An unexpected error occurred while processing your request",
  "data": {
    "request_id": "req_123456789",
    "suggestion": "Please try again later. If the problem persists, contact support"
  },
  "success": false
}
```

## 试用用户特殊响应

### 试用用户成功响应
```json
{
  "success": true,
  "message": "Image generation successful. Usage recorded against trial credits",
  "data": {
    "task_id": "img_gen_123456789",
    "images": [...],
    "remaining_credits": 5,
    "remaining_monthly_usage": 0,
    "subscription_status": "trial",
    "generation_record_id": "987fcdeb-51a2-4567-8901-234567890abc",
    "trial_message": "Trial user: 1 free generations remaining (5/10 trial credits left)",
    "is_trial_user": true
  },
  "error": null
}
```

### 试用积分用完
```json
{
  "error_code": "INSUFFICIENT_CREDITS",
  "message": "Trial exhausted: You've used all 10 trial credits. Please purchase credits or subscribe to continue.",
  "data": {
    "remaining_credits": 0,
    "trial_credits_used": 10,
    "is_trial_user": true,
    "subscription_status": "trial_exhausted",
    "suggestion": "Please subscribe to a plan or purchase credits to continue generating images"
  },
  "success": false
}
```

## 客户端处理建议

### 错误处理流程

```javascript
async function handleImageGeneration(requestData) {
  try {
    const response = await fetch('/api/v1/image/generate-image-sync', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestData)
    });
    
    const data = await response.json();
    
    if (response.ok && data.success) {
      // 成功处理
      handleSuccess(data);
    } else {
      // 错误处理
      handleError(response.status, data);
    }
  } catch (error) {
    // 网络错误处理
    handleNetworkError(error);
  }
}

function handleError(statusCode, errorData) {
  const { error_code, message, data } = errorData;
  
  switch (error_code) {
    case 'NO_SUBSCRIPTION_OR_CREDITS':
    case 'INSUFFICIENT_CREDITS':
      // 引导用户购买积分或订阅
      showPurchaseDialog(data.suggestion);
      break;
      
    case 'MONTHLY_LIMIT_REACHED':
      // 显示限制提示
      showLimitReachedMessage(data.monthly_limit);
      break;
      
    case 'API_AUTHENTICATION_FAILED':
    case 'SERVICE_UNAVAILABLE':
      // 显示服务暂时不可用
      showServiceUnavailableMessage();
      break;
      
    case 'INVALID_PROMPT':
    case 'INVALID_SIZE':
      // 显示参数错误提示
      showValidationError(message);
      break;
      
    default:
      // 显示通用错误信息
      showGenericError(message);
  }
}
```

## 状态码总结

| HTTP状态码 | 错误类型 | 说明 |
|-----------|---------|------|
| 200 | 成功 | 图片生成成功 |
| 400 | API调用错误 | 第三方API调用失败 |
| 401 | 认证错误 | JWT token无效或过期 |
| 403 | 权限错误 | 无订阅、积分不足、次数用完 |
| 422 | 参数错误 | 请求参数验证失败 |
| 500 | 系统错误 | 服务器内部错误 |
| 503 | 服务不可用 | 图片生成服务配置问题 |
