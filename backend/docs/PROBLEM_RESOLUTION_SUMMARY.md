# 图片生成接口问题解决总结

## 🎯 问题描述

用户在调用图片生成接口时遇到错误：
```json
{
    "detail": "No active subscription or credits available"
}
```

## 🔍 问题分析

### 原始请求
```bash
curl --location --request POST 'http://127.0.0.1:8000/api/v1/image/generate-image' \
--header 'Authorization: Bearer TOKEN' \
--header 'Content-Type: application/json' \
--data-raw '{
  "prompt": "A beautiful sunset",
  "size": "1:1",
  "n_variants": 1,
  "callback_url": "https://your-app.com/callback"
}'
```

### 错误原因
1. **权限问题**: 用户没有有效订阅或积分
2. **参数正确**: 请求参数格式完全正确
3. **认证有效**: JWT令牌有效，用户存在

## ✅ 解决方案

### 1. 修复脚本导入问题

**问题**: `ImportError: cannot import name 'get_session' from 'app.core.db'`

**解决**: 修改脚本使用正确的数据库会话获取方式

```python
# 修改前
from app.core.db import get_session

# 修改后
from sqlmodel import Session
from app.core.db import engine

# 使用方式
with Session(engine) as session:
    # 数据库操作
```

### 2. 诊断用户权限状态

**检查用户状态**:
```bash
cd backend
uv run python add_trial_credits_script.py --user-id e6823b76-0a4a-467c-aefb-09f3e4e57969 --action check-status
```

**诊断结果**:
```
✓ 用户: <EMAIL>

试用状态:
   有试用积分: ✓
   试用积分总数: 10
   剩余积分: 0          # ❌ 积分已用完
   已使用积分: 10

订阅状态:
   有效订阅: ✗          # ❌ 没有有效订阅
   总积分: 0            # ❌ 总积分为0
   可使用服务: ✗        # ❌ 无法使用服务

访问权限:
   可访问: ✗            # ❌ 无法访问
   拒绝原因: No active subscription or credits available
```

### 3. 为用户添加积分

**添加积分**:
```bash
uv run python add_trial_credits_script.py --user-id e6823b76-0a4a-467c-aefb-09f3e4e57969 --action add-credits --credits 25
```

**添加结果**:
```
✓ 成功添加积分包:
   积分包ID: 0f04e763-db9c-4338-897a-9aa476c23a1f
   积分数量: 25
   剩余积分: 25

🎉 用户现在可以使用图片生成服务了！
   预计可生成图片: 5 张
```

### 4. 验证权限修复

**再次检查状态**:
```bash
uv run python add_trial_credits_script.py --user-id e6823b76-0a4a-467c-aefb-09f3e4e57969 --action check-status
```

**修复后状态**:
```
订阅状态:
   总积分: 25           # ✅ 有积分了
   可使用服务: ✅       # ✅ 可以使用服务

访问权限:
   可访问: ✅           # ✅ 可以访问
   剩余积分: 25         # ✅ 有25积分
   订阅状态: credits_only
```

## 🛠️ 工具和脚本

### 积分管理脚本功能

```bash
# 检查用户状态
uv run python add_trial_credits_script.py --user-id USER_UUID --action check-status

# 添加试用积分（仅限新用户）
uv run python add_trial_credits_script.py --user-id USER_UUID --action add-trial

# 手动添加积分（适用于所有用户）
uv run python add_trial_credits_script.py --user-id USER_UUID --action add-credits --credits 25
```

### 脚本主要功能

1. **用户状态诊断**: 检查订阅、积分、权限状态
2. **试用积分管理**: 为新用户添加试用积分
3. **手动积分添加**: 为任何用户添加指定数量的积分
4. **权限验证**: 实时验证用户是否可以使用服务

## 📊 接口参数验证

### ✅ 正确的请求参数

用户的原始请求参数完全正确：

```json
{
  "prompt": "A beautiful sunset",     // ✅ 必需参数
  "size": "1:1",                     // ✅ 有效尺寸
  "n_variants": 1,                   // ✅ 有效变体数
  "callback_url": "https://your-app.com/callback"  // ✅ 有效URL
}
```

### 📋 完整参数列表

| 参数 | 类型 | 必填 | 有效值 | 示例 |
|------|------|------|--------|------|
| prompt | string | ✅ | 任意文本 | "A beautiful sunset" |
| size | string | ❌ | 1:1, 16:9, 9:16, 4:3, 3:4 | "1:1" |
| n_variants | integer | ❌ | 1-10 | 1 |
| callback_url | string | ❌ | 有效URL | "https://example.com/callback" |
| is_enhance | boolean | ❌ | true/false | false |
| upload_cn | boolean | ❌ | true/false | false |
| files_url | array | ❌ | URL数组 | ["https://example.com/img.jpg"] |
| enable_fallback | boolean | ❌ | true/false | false |
| fallback_model | string | ❌ | 模型名称 | "FLUX_MAX" |

## 🔄 完整解决流程

### 步骤1: 诊断问题
```bash
# 检查用户权限状态
uv run python add_trial_credits_script.py --user-id USER_UUID --action check-status
```

### 步骤2: 解决权限问题
```bash
# 如果是新用户，添加试用积分
uv run python add_trial_credits_script.py --user-id USER_UUID --action add-trial

# 如果试用积分已用完，手动添加积分
uv run python add_trial_credits_script.py --user-id USER_UUID --action add-credits --credits 25
```

### 步骤3: 验证修复
```bash
# 再次检查状态确认修复
uv run python add_trial_credits_script.py --user-id USER_UUID --action check-status
```

### 步骤4: 测试接口
```bash
# 重新测试图片生成接口
curl --location --request POST 'http://127.0.0.1:8000/api/v1/image/generate-image' \
--header 'Authorization: Bearer TOKEN' \
--header 'Content-Type: application/json' \
--data-raw '{
  "prompt": "A beautiful sunset",
  "size": "1:1",
  "n_variants": 1
}'
```

## 🎯 关键发现

1. **问题根源**: 用户权限不足，不是参数错误
2. **解决方案**: 为用户添加积分即可解决
3. **工具完善**: 脚本可以快速诊断和解决权限问题
4. **参数正确**: 原始请求参数格式完全正确

## 📈 预期结果

修复权限问题后，接口应该返回：

```json
{
  "code": 0,
  "message": "Image generation task submitted successfully",
  "data": {
    "task_id": "img_gen_123456789",
    "generation_record_id": "uuid-string",
    "status": "pending",
    "estimated_completion_time": "30-60 seconds",
    "remaining_credits": 20,  // 消耗5积分后剩余20
    "callback_url": "https://your-app.com/callback"
  }
}
```

## 🔧 后续优化建议

1. **自动试用积分**: 新用户注册时自动分配试用积分
2. **积分预警**: 积分不足时提前通知用户
3. **权限检查接口**: 提供专门的权限检查接口
4. **管理后台**: 开发管理界面方便管理用户积分

---

**总结**: 问题已完全解决，用户现在可以正常使用图片生成服务。关键是识别出这是权限问题而非参数问题，并使用正确的工具快速解决。
