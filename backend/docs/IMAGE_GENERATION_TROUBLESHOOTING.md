# 图片生成服务故障排除指南

## 目录

- [常见错误及解决方案](#常见错误及解决方案)
- [权限问题诊断](#权限问题诊断)
- [用户积分管理](#用户积分管理)
- [API调试工具](#api调试工具)
- [系统监控](#系统监控)

## 常见错误及解决方案

### 1. 权限相关错误

#### 错误: "No active subscription or credits available"

**原因**: 用户没有有效订阅或积分

**诊断步骤**:
```bash
# 1. 检查用户订阅状态
curl -X GET 'http://127.0.0.1:8000/api/v1/subscriptions/status' \
  -H 'Authorization: Bearer YOUR_TOKEN'

# 2. 检查访问权限
curl -X GET 'http://127.0.0.1:8000/api/v1/subscriptions/check-access' \
  -H 'Authorization: Bearer YOUR_TOKEN'

# 3. 使用脚本检查详细状态
python add_trial_credits_script.py --user-id USER_UUID --action check-status
```

**解决方案**:
```bash
# 检查用户状态
uv run python add_trial_credits_script.py --user-id USER_UUID --action check-status

# 为用户添加试用积分（如果没有试用积分）
uv run python add_trial_credits_script.py --user-id USER_UUID --action add-trial

# 手动为用户添加积分（如果试用积分已用完）
uv run python add_trial_credits_script.py --user-id USER_UUID --action add-credits --credits 25
```

#### 错误: "Monthly usage limit reached"

**原因**: 用户已达到月度使用限制

**解决方案**:
1. 等待下个月度周期重置
2. 升级用户订阅计划
3. 使用积分代替订阅配额

#### 错误: "Insufficient credits. Need X, have Y"

**原因**: 用户积分不足

**解决方案**:
```bash
# 检查用户积分详情
python add_trial_credits_script.py --user-id USER_UUID --action check-status

# 手动添加积分（管理员操作）
# 通过数据库或管理接口为用户充值积分
```

### 2. API调用错误

#### 错误: "API token not configured"

**原因**: 服务器未配置第三方API令牌

**解决方案**:
```bash
# 检查环境变量
echo $IMAGE_GENERATION_API_TOKEN

# 设置环境变量
export IMAGE_GENERATION_API_TOKEN="your_api_token_here"

# 重启服务
systemctl restart your-app-service
```

#### 错误: "API error code 401: 缺少身份验证凭据或凭据无效"

**原因**: 第三方API认证失败

**解决方案**:
1. 检查API令牌是否正确
2. 检查API令牌是否过期
3. 联系第三方API提供商确认账户状态

#### 错误: "API error code 429: 已超过对此资源的请求限制"

**原因**: 第三方API请求频率过高

**解决方案**:
1. 实现请求频率限制
2. 添加重试机制
3. 考虑升级第三方API计划

### 3. 任务状态查询错误

#### 错误: "Task not found"

**原因**: 任务ID不存在或已过期

**解决方案**:
```bash
# 检查数据库中的任务记录
SELECT * FROM imagegenerationrecord WHERE task_id = 'your_task_id';

# 检查用户权限
SELECT * FROM imagegenerationrecord WHERE task_id = 'your_task_id' AND user_id = 'user_uuid';
```

## 权限问题诊断

### 诊断流程图

```
用户请求图片生成
    ↓
检查用户是否存在
    ↓
检查用户订阅状态
    ↓
检查用户积分余额
    ↓
检查月度使用限制
    ↓
权限验证通过/失败
```

### 详细诊断脚本

创建诊断脚本来快速定位权限问题：

```python
#!/usr/bin/env python3
"""
权限诊断脚本
使用方法: python diagnose_permissions.py --user-id USER_UUID
"""

import sys
import uuid
from app.core.db import get_session
from app.models import User
from app.services.billing import BillingService
from app.services.trial_service import TrialService

def diagnose_user_permissions(user_id_str: str):
    """诊断用户权限问题"""
    try:
        user_id = uuid.UUID(user_id_str)
        
        with get_session() as session:
            # 1. 检查用户存在性
            user = session.get(User, user_id)
            if not user:
                print("❌ 用户不存在")
                return
            
            print(f"✓ 用户存在: {user.email}")
            
            # 2. 检查订阅状态
            billing_service = BillingService(session)
            subscription_status = billing_service.get_user_subscription_status(user_id)
            
            print(f"\n📊 订阅状态:")
            print(f"   有效订阅: {'✓' if subscription_status.has_active_subscription else '✗'}")
            print(f"   总积分: {subscription_status.total_credits}")
            print(f"   月度使用: {subscription_status.monthly_usage_count}/{subscription_status.monthly_limit}")
            print(f"   可使用服务: {'✓' if subscription_status.can_use_service else '✗'}")
            
            # 3. 检查试用状态
            trial_service = TrialService(session)
            trial_status = trial_service.get_trial_status(user_id)
            
            print(f"\n🎯 试用状态:")
            print(f"   有试用积分: {'✓' if trial_status['has_trial'] else '✗'}")
            print(f"   试用积分: {trial_status['remaining_credits']}/{trial_status['trial_credits']}")
            
            # 4. 权限检查
            from app.models import UsageTypeEnum
            access_check = billing_service.check_access(user_id, UsageTypeEnum.image_generation)
            
            print(f"\n🔐 权限检查:")
            print(f"   可访问: {'✓' if access_check.can_access else '✗'}")
            print(f"   剩余积分: {access_check.remaining_credits}")
            print(f"   剩余月度使用: {access_check.remaining_monthly_usage}")
            print(f"   订阅状态: {access_check.subscription_status}")
            
            if not access_check.can_access:
                print(f"   ❌ 拒绝原因: {access_check.reason}")
                
                # 提供解决建议
                print(f"\n💡 解决建议:")
                if "no active subscription" in access_check.reason.lower():
                    print("   - 为用户添加试用积分")
                    print("   - 引导用户购买积分或订阅")
                elif "monthly usage limit" in access_check.reason.lower():
                    print("   - 等待月度周期重置")
                    print("   - 升级用户订阅计划")
                elif "insufficient credits" in access_check.reason.lower():
                    print("   - 为用户充值积分")
            else:
                print(f"   ✓ 权限正常，可以使用图片生成服务")
                
    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {str(e)}")

if __name__ == "__main__":
    if len(sys.argv) != 3 or sys.argv[1] != "--user-id":
        print("使用方法: python diagnose_permissions.py --user-id USER_UUID")
        sys.exit(1)
    
    diagnose_user_permissions(sys.argv[2])
```

## 用户积分管理

### 积分类型

1. **试用积分**: 新用户免费获得，用于体验服务
2. **购买积分**: 用户付费购买的积分
3. **订阅配额**: 订阅用户的月度使用配额

### 积分管理操作

#### 查看用户积分详情

```sql
-- 查看用户所有积分包
SELECT 
    cp.id,
    cp.credits,
    cp.remaining_credits,
    cp.product_id,
    cp.platform,
    cp.purchased_at
FROM creditpackage cp
WHERE cp.user_id = 'USER_UUID'
ORDER BY cp.purchased_at DESC;

-- 查看用户使用记录
SELECT 
    ur.used_at,
    ur.usage_type,
    ur.count,
    ur.related_record_id
FROM usagerecord ur
WHERE ur.user_id = 'USER_UUID'
ORDER BY ur.used_at DESC
LIMIT 20;
```

#### 手动添加积分

```sql
-- 为用户添加积分包
INSERT INTO creditpackage (
    id, user_id, credits, remaining_credits, 
    product_id, platform, purchased_at
) VALUES (
    gen_random_uuid(),
    'USER_UUID',
    50,  -- 积分数量
    50,  -- 剩余积分
    'manual_credit',
    'stripe',
    NOW()
);
```

## API调试工具

### 完整的测试脚本

```bash
#!/bin/bash
# 图片生成API测试脚本

USER_TOKEN="YOUR_TOKEN_HERE"
BASE_URL="http://127.0.0.1:8000/api/v1"

echo "=== 图片生成API测试 ==="

# 1. 检查用户状态
echo "1. 检查用户订阅状态..."
curl -s -X GET "${BASE_URL}/subscriptions/status" \
  -H "Authorization: Bearer ${USER_TOKEN}" | jq .

# 2. 检查访问权限
echo -e "\n2. 检查访问权限..."
curl -s -X GET "${BASE_URL}/subscriptions/check-access" \
  -H "Authorization: Bearer ${USER_TOKEN}" | jq .

# 3. 提交图片生成任务
echo -e "\n3. 提交图片生成任务..."
RESPONSE=$(curl -s -X POST "${BASE_URL}/image/generate-image" \
  -H "Authorization: Bearer ${USER_TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "A beautiful sunset over mountains",
    "size": "1:1",
    "n_variants": 1
  }')

echo $RESPONSE | jq .

# 4. 提取task_id并查询状态
TASK_ID=$(echo $RESPONSE | jq -r '.data.task_id // empty')

if [ ! -z "$TASK_ID" ] && [ "$TASK_ID" != "null" ]; then
    echo -e "\n4. 查询任务状态 (Task ID: $TASK_ID)..."
    curl -s -X GET "${BASE_URL}/image/generation-status/${TASK_ID}" \
      -H "Authorization: Bearer ${USER_TOKEN}" | jq .
else
    echo -e "\n4. 无法获取task_id，跳过状态查询"
fi

# 5. 获取历史记录
echo -e "\n5. 获取历史记录..."
curl -s -X GET "${BASE_URL}/image/history?limit=5" \
  -H "Authorization: Bearer ${USER_TOKEN}" | jq .
```

### 日志监控

监控关键日志文件：

```bash
# 监控应用日志
tail -f /var/log/your-app/app.log | grep -E "(image_generation|ERROR|WARNING)"

# 监控数据库查询
tail -f /var/log/postgresql/postgresql.log | grep -E "(imagegenerationrecord|usagerecord|creditpackage)"

# 监控API调用
tail -f /var/log/nginx/access.log | grep -E "(/generate-image|/generation-status)"
```

## 系统监控

### 关键指标

1. **成功率**: 图片生成成功的比例
2. **响应时间**: API响应时间
3. **错误率**: 各类错误的发生频率
4. **用户活跃度**: 活跃用户数量和使用频率

### 监控查询

```sql
-- 今日图片生成统计
SELECT 
    status,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
FROM imagegenerationrecord 
WHERE created_at >= CURRENT_DATE
GROUP BY status;

-- 用户积分使用情况
SELECT 
    COUNT(DISTINCT user_id) as active_users,
    SUM(count) as total_usage,
    AVG(count) as avg_usage_per_request
FROM usagerecord 
WHERE used_at >= CURRENT_DATE - INTERVAL '7 days';

-- 错误分析
SELECT 
    error_message,
    COUNT(*) as error_count
FROM imagegenerationrecord 
WHERE status = 'failed' 
  AND created_at >= CURRENT_DATE - INTERVAL '1 day'
GROUP BY error_message
ORDER BY error_count DESC;
```

---

## 快速故障排除检查清单

### 用户无法生成图片

- [ ] 用户是否存在且已认证
- [ ] 用户是否有有效订阅或积分
- [ ] 用户是否达到月度使用限制
- [ ] API令牌是否正确配置
- [ ] 第三方API服务是否正常
- [ ] 数据库连接是否正常

### 任务状态查询失败

- [ ] task_id是否正确
- [ ] 用户是否有权限查看该任务
- [ ] 数据库中是否存在该任务记录
- [ ] 任务是否已过期被清理

### 回调通知未收到

- [ ] 回调URL是否可访问
- [ ] 回调URL格式是否正确
- [ ] 网络连接是否正常
- [ ] 第三方API是否正常发送回调

使用这些工具和方法，可以快速定位和解决图片生成服务中的各种问题。
