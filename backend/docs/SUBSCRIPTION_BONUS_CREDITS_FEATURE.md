# 订阅赠送积分功能

## 功能概述

当用户成功订阅月度或年度套餐时，系统会自动为用户创建对应的赠送积分包，用于超出订阅限制后的额外图片生成。

## 积分赠送规则

### 月度订阅
- **月度40次套餐** (`sub_monthly_40`): 赠送 **200积分**
- **月度60次套餐** (`sub_monthly_60`): 赠送 **300积分**

### 年度订阅
- **年度480次套餐** (`sub_yearly_480`): 赠送 **2400积分** (200积分/月 × 12月)
- **年度600次套餐** (`sub_yearly_600`): 赠送 **3000积分** (600次 × 5积分)
- **年度720次套餐** (`sub_yearly_720`): 赠送 **3600积分** (300积分/月 × 12月)

## 积分计算逻辑

积分数量基于订阅包含的图片生成次数计算：
- **每次图片生成** = 5积分
- **月度40次** = 40次 × 5积分 = 200积分
- **月度60次** = 60次 × 5积分 = 300积分
- **年度480次** = 480次 × 5积分 = 2400积分
- **年度600次** = 600次 × 5积分 = 3000积分
- **年度720次** = 720次 × 5积分 = 3600积分

## 技术实现

### 1. 配置定义

在 `BillingService` 中定义积分配置：

```python
SUBSCRIPTION_BONUS_CREDITS = {
    "sub_monthly_40": {"bonus_credits": 200, "description": "月度40次订阅赠送积分"},
    "sub_monthly_60": {"bonus_credits": 300, "description": "月度60次订阅赠送积分"},
    "sub_yearly_480": {"bonus_credits": 2400, "description": "年度480次订阅赠送积分"},
    "sub_yearly_600": {"bonus_credits": 3000, "description": "年度600次订阅赠送积分"},
    "sub_yearly_720": {"bonus_credits": 3600, "description": "年度720次订阅赠送积分"},
}
```

### 2. 积分包创建

```python
def create_subscription_bonus_credits(
    self, 
    user_id: uuid.UUID, 
    product_id: str, 
    platform: SubscriptionPlatformEnum = SubscriptionPlatformEnum.stripe
) -> Optional[CreditPackage]:
    """为订阅用户创建赠送积分包"""
```

### 3. 订阅流程集成

在订阅升级成功后自动调用积分创建：

```python
# 订阅创建/更新成功后
billing_service = BillingService(session)
bonus_credits = billing_service.create_subscription_bonus_credits(
    user_id=user_id,
    product_id=product_id,
    platform=platform
)
```

## 数据库设计

### 积分包标识

赠送积分包使用特殊的产品ID格式进行标识：

| 订阅产品ID | 赠送积分包产品ID |
|------------|------------------|
| `sub_monthly_40` | `subscription_bonus_sub_monthly_40` |
| `sub_monthly_60` | `subscription_bonus_sub_monthly_60` |
| `sub_yearly_480` | `subscription_bonus_sub_yearly_480` |
| `sub_yearly_720` | `subscription_bonus_sub_yearly_720` |

### CreditPackage 表结构

```sql
CREATE TABLE creditpackage (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES user(id) ON DELETE CASCADE,
    credits INTEGER NOT NULL,           -- 总积分数
    remaining_credits INTEGER NOT NULL, -- 剩余积分数
    product_id VARCHAR(100) NOT NULL,   -- 产品标识
    platform subscription_platform_enum NOT NULL,
    purchased_at TIMESTAMP WITH TIME ZONE NOT NULL
);
```

## API 响应变化

### 订阅升级响应

成功创建赠送积分后，响应消息会包含积分信息：

```json
{
    "subscription": {...},
    "message": "New subscription created successfully. Bonus credits added: 200",
    "is_new_subscription": true
}
```

### 用户积分查询

通过 `/login/test-token` 接口可以查看用户的总积分和剩余积分：

```json
{
    "id": "user-uuid",
    "email": "<EMAIL>",
    "total_credits": 210,      // 包含赠送积分
    "remaining_credits": 185,  // 包含赠送积分余额
    ...
}
```

## 业务逻辑

### 1. 积分使用优先级

系统按以下优先级消耗积分：
1. **试用积分** (`trial_credits_new_user`)
2. **购买积分包** (按购买时间排序)
3. **订阅赠送积分** (`subscription_bonus_*`)

### 2. 重复订阅处理

- **新订阅**: 创建新的赠送积分包
- **续费/升级**: 在现有赠送积分包基础上增加积分

### 3. 错误处理

- 积分创建失败不影响订阅成功
- 失败时记录错误日志，返回相应提示信息

## 使用场景

### 1. 订阅用户超限使用

用户月度40次用完后，可以使用赠送的200积分继续生成图片：
- 200积分 ÷ 5积分/次 = 额外40次生成机会

### 2. 灵活的使用模式

- **轻度用户**: 可能用不完订阅次数，积分作为备用
- **重度用户**: 订阅次数用完后，积分提供额外使用机会
- **不规律用户**: 某些月份使用较多时，积分提供缓冲

## 监控和统计

### 1. 积分包统计

```sql
-- 查询用户的赠送积分包
SELECT * FROM creditpackage 
WHERE user_id = ? AND product_id LIKE 'subscription_bonus_%';

-- 统计各类型赠送积分包数量
SELECT product_id, COUNT(*), SUM(credits), SUM(remaining_credits)
FROM creditpackage 
WHERE product_id LIKE 'subscription_bonus_%'
GROUP BY product_id;
```

### 2. 业务指标

- 赠送积分使用率
- 超限使用用户比例
- 积分包创建成功率

## 配置管理

### 环境变量

无需额外环境变量，积分配置硬编码在 `BillingService` 中。

### 动态调整

如需调整积分数量，修改 `SUBSCRIPTION_BONUS_CREDITS` 配置并重新部署。

## 测试建议

### 1. 单元测试

- 测试积分配置正确性
- 测试积分包创建逻辑
- 测试重复订阅处理

### 2. 集成测试

- 测试订阅升级 + 积分创建流程
- 测试积分使用优先级
- 测试错误处理机制

### 3. 业务测试

- 验证不同订阅套餐的积分数量
- 验证积分在图片生成中的使用
- 验证用户积分余额显示

## 注意事项

1. **积分不可退款**: 赠送积分仅用于系统内消费
2. **积分不过期**: 赠送积分永久有效
3. **积分不可转移**: 积分仅限用户本人使用
4. **订阅取消**: 取消订阅不影响已获得的赠送积分