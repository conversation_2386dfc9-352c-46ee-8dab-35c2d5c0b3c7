# 图片生成服务文档中心

欢迎使用图片生成服务！这里是所有相关文档的导航中心。

## 📋 文档目录

### 🚀 快速开始
- [图片生成服务使用指南](./IMAGE_GENERATION_README.md) - 基础配置和使用说明

### 📖 API文档
- [API使用指南](./IMAGE_GENERATION_API_GUIDE.md) - 详细的API参数说明和示例
- [接口重构总结](../API_INTERFACE_OPTIMIZATION_SUMMARY.md) - 最新接口优化说明

### 🔧 故障排除
- [故障排除指南](./IMAGE_GENERATION_TROUBLESHOOTING.md) - 常见问题解决方案

### 🛠️ 技术文档
- [数据库优化分析](../IMAGE_GENERATION_OPTIMIZATION_ANALYSIS.md) - 数据库结构优化
- [第三方API状态码映射](../third_party_api_status_mapping.md) - API错误码说明

## 🎯 核心功能

### ✨ 主要特性
- **AI图片生成**: 支持多种尺寸和参数的AI图片生成
- **异步处理**: 真正的异步任务处理，支持状态查询
- **权限管理**: 完整的用户权限和积分系统
- **推送通知**: iOS APNS推送通知支持
- **标准响应**: 统一的API响应格式
- **错误处理**: 完善的错误处理和状态码映射

### 🔄 工作流程
1. **提交任务**: 调用 `/generate-image` 接口提交图片生成任务
2. **获取任务ID**: 接收返回的 `task_id` 和任务状态
3. **查询状态**: 使用 `/generation-status/{task_id}` 查询任务进度
4. **获取结果**: 任务完成后获取生成的图片URL
5. **推送通知**: 自动发送推送通知给用户（如果配置）

## 🚀 快速开始

### 1. 环境配置
```bash
# 设置环境变量
export IMAGE_GENERATION_API_TOKEN="your_api_token"
export DATABASE_URL="postgresql://user:pass@localhost/db"

# 启动服务
cd backend
uv run fastapi run --reload app/main.py
```

### 2. 测试接口
```bash
# 检查用户权限
curl -X GET 'http://127.0.0.1:8000/api/v1/subscriptions/check-access' \
  -H 'Authorization: Bearer YOUR_TOKEN'

# 提交图片生成任务
curl -X POST 'http://127.0.0.1:8000/api/v1/image/generate-image' \
  -H 'Authorization: Bearer YOUR_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{"prompt": "A beautiful sunset", "size": "1:1"}'
```

### 3. 权限问题解决
如果遇到权限错误，使用积分管理脚本：
```bash
# 检查用户状态
python add_trial_credits_script.py --user-id USER_UUID --action check-status

# 添加试用积分
python add_trial_credits_script.py --user-id USER_UUID --action add-trial
```

## 📊 API接口概览

### 核心接口

| 接口 | 方法 | 说明 | 状态 |
|------|------|------|------|
| `/generate-image` | POST | 提交图片生成任务 | ✅ 推荐 |
| `/generation-status/{task_id}` | GET | 查询任务状态 | ✅ 新增 |
| `/history` | GET | 获取历史记录 | ✅ 优化 |
| `/generate-image-sync` | POST | 同步生成接口 | ⚠️ 已弃用 |

### 管理接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/subscriptions/status` | GET | 用户订阅状态 |
| `/subscriptions/check-access` | GET | 权限检查 |
| `/callback` | POST | 第三方API回调 |

## 🔐 权限系统

### 权限类型
1. **订阅用户**: 有月度使用配额
2. **积分用户**: 使用购买的积分
3. **试用用户**: 使用免费试用积分

### 积分消耗
- 基础生成: 5积分/张
- 增强生成: 10积分/张
- 多变体: N × 基础积分

## 📈 监控与维护

### 关键指标
- 成功率: 图片生成成功比例
- 响应时间: API平均响应时间
- 错误率: 各类错误发生频率
- 用户活跃度: 日活跃用户数

### 日志监控
```bash
# 监控应用日志
tail -f logs/app.log | grep -E "(image_generation|ERROR)"

# 监控数据库性能
SELECT * FROM pg_stat_activity WHERE query LIKE '%imagegenerationrecord%';
```

## 🛡️ 安全考虑

### API安全
- 所有接口都需要JWT认证
- 实现请求频率限制
- 敏感信息不在日志中记录

### 数据安全
- 用户数据加密存储
- 定期清理过期数据
- 备份重要数据

## 🔄 版本更新

### 最新更新 (v2.0)
- ✅ 新增任务状态查询接口
- ✅ 优化数据库结构，添加task_id字段
- ✅ 统一API响应格式
- ✅ 改进错误处理和状态码映射
- ✅ 添加UsageRecord关联功能

### 向后兼容
- 保留旧接口但标记为弃用
- 提供迁移指南和工具
- 渐进式升级支持

## 📞 支持与反馈

### 常见问题
参考 [故障排除指南](./IMAGE_GENERATION_TROUBLESHOOTING.md) 获取常见问题的解决方案。

### 技术支持
- 查看详细日志定位问题
- 使用诊断脚本检查系统状态
- 联系技术团队获取支持

### 贡献指南
欢迎提交问题报告和功能建议，帮助我们改进服务质量。

---

## 📚 相关资源

- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [SQLModel文档](https://sqlmodel.tiangolo.com/)
- [PostgreSQL文档](https://www.postgresql.org/docs/)
- [APNs开发指南](https://developer.apple.com/documentation/usernotifications)

---

*最后更新: 2025-01-31*
