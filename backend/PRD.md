以下是基于你需求整理的标准化《订阅支付功能 PRD 文档》示例，适用于基于 iOS 平台（SwiftUI + StoreKit 2）+ FastAPI 后端架构的 AI 生图应用：

---

## 📄 产品需求文档（PRD）

**功能名称：** 用户订阅支付系统
**适用平台：** iOS（iPhone/iPad）
**版本：** MVP v1.0
**负责人：** 产品经理（可填写姓名）
**编写时间：** 2025年7月29日

---

### 🔍 一、功能背景

应用为一款 AI 生成图片 App，用户每次调用生成图像将消耗平台资源积分（例如 GPT-4o image API）。为平衡成本和用户体验，需要引入订阅和单次购买机制，提供可持续的盈利模式。

---

### 🎯 二、核心目标

* 支持 Apple ID 登录后，进行订阅购买或单次购买。
* 后端可识别用户订阅类型、订阅状态与有效期。
* 前端调用 FastAPI 接口进行图片生成时，能识别用户剩余可用次数，超出后引导充值或升级套餐。
* 后端支持 StoreKit Server Notification 或定时校验。

---

### 🧭 三、功能模块

#### 1. 订阅产品结构设计（前端+App Store Connect）

| 产品名称    | 产品ID              | 类型   | 次数限制/月 | 附加功能      | 建议价格（¥） |
| ------- | ----------------- | ---- | ------ | --------- | ------- |
| 单次生成    | gen\_once         | 一次性  | 1次     | 无         | ¥1.00   |
| 月度订阅高级版 | sub\_month\_pro   | 自动续订 | 100次   | 自定义风格     | ¥28.00  |
| 年度订阅基础版 | sub\_year\_basic  | 自动续订 | 360次   | 自定义头像/无水印 | ¥128.00 |

> 使用 StoreKit 2 配置上述订阅商品。

---

### 🗃️ 四、后端设计（FastAPI）

#### （1）表结构设计

##### 用户表（user）

```sql
- id (UUID / INT)
- apple_id (string)
- device_token (string)
- created_at / updated_at
```

##### 订阅状态表（subscription）

```sql
- id
- user_id (foreign key)
- product_id
- status (active, expired, canceled)
- expires_at (datetime)
- transaction_id
- original_transaction_id
- updated_at
```

##### 使用记录表（usage\_log）

```sql
- id
- user_id
- generation_type (standard, pro)
- task_id
- image_url
- created_at
```

---

#### （2）后端接口目录结构（FastAPI）

```
/app
  └── main.py
  └── routes/
        └── auth.py            # 登录 & Apple ID 授权
        └── billing.py         # 验证收据 & 订阅同步
        └── generate.py        # 提交生成任务
        └── usage.py           # 查询/记录用户调用次数
        └── callback.py        # 接收任务完成回调
  └── services/
        └── storekit.py        # 苹果服务器验证封装
        └── subscription.py    # 用户订阅管理
  └── database/
        └── models.py
        └── schema.py
        └── session.py
```

---

### 📲 五、iOS 实现说明

* 使用 **StoreKit 2** 实现订阅购买、收据校验。
* 使用 **DeviceToken** 与用户绑定，用于 APNs 推送。
* 提交生图请求后，后端返回 task\_id，处理完成后通过 APNs 通知对应设备，APP 轮询或收到推送后展示图片结果。

---

### 🔐 六、安全与风控

* 所有调用生成接口需验证用户订阅状态与剩余次数。
* 所有请求记录时间戳与日志，预防滥用。
* 支持苹果 StoreKit Server Notification 或定期主动校验订阅状态。

---

### 📈 七、运营建议

* 免费试用期：提供3次免费生成机会，引导订阅。
* 裂变激励：邀请好友可额外赠送生成次数。
* 限时活动：双11/618 促销时段推出年度订阅打折活动。

---

如需我进一步生成 Word 或 PDF 格式文档，或进行视觉稿、产品脑图、API文档自动化整理，也可继续告诉我。
