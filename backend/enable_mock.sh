#!/bin/bash

# ============================================================================
# 启用图片生成Mock模式脚本
# Enable Image Generation Mock Mode Script
# ============================================================================

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🔧 启用图片生成Mock模式${NC}"
echo "=" * 50

# 检查.env文件是否存在
if [ ! -f "../.env" ]; then
    echo -e "${YELLOW}⚠️  .env文件不存在，创建新文件${NC}"
    touch ../.env
fi

# 检查是否已经有ENABLE_IMAGE_GENERATION_MOCK配置
if grep -q "ENABLE_IMAGE_GENERATION_MOCK" ../.env; then
    echo -e "${YELLOW}📝 更新现有的Mock配置${NC}"
    # 使用sed替换现有配置
    sed -i.bak 's/ENABLE_IMAGE_GENERATION_MOCK=.*/ENABLE_IMAGE_GENERATION_MOCK=True/' ../.env
else
    echo -e "${YELLOW}📝 添加新的Mock配置${NC}"
    # 添加新配置
    echo "" >> ../.env
    echo "# Mock Configuration" >> ../.env
    echo "ENABLE_IMAGE_GENERATION_MOCK=True" >> ../.env
fi

echo -e "${GREEN}✅ Mock模式已启用${NC}"
echo ""
echo "配置详情:"
echo "  文件: ../.env"
echo "  配置: ENABLE_IMAGE_GENERATION_MOCK=True"
echo ""
echo "下一步:"
echo "  1. 重启FastAPI服务器: uv run uvicorn app.main:app --reload"
echo "  2. 运行测试: uv run python test/test_record_info_mock.py"
echo "  3. 测试接口: GET /api/v1/image/record-info/{task_id}"
echo ""
echo -e "${BLUE}💡 提示: 要禁用Mock模式，请将 ENABLE_IMAGE_GENERATION_MOCK 设置为 False${NC}"
