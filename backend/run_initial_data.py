#!/usr/bin/env python3
"""
Convenience script to run initial data setup.

This script can be run from the backend directory to initialize
the database with demo data for the subscription system.
"""

import subprocess
import sys
import os

def main():
    """Run the initial data script."""
    # Change to the backend directory if not already there
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    print("🚀 Running initial data setup...")
    print("📍 Current directory:", os.getcwd())
    
    try:
        # Check if uv is available and use it, otherwise fall back to python
        uv_available = subprocess.run(["uv", "--version"],
                                    capture_output=True, check=False).returncode == 0

        if uv_available:
            print("📦 Using uv to run the script...")
            subprocess.run([
                "uv", "run", "python", "app/initial_data.py"
            ], check=True, capture_output=False)
        else:
            print("🐍 Using system python to run the script...")
            subprocess.run([
                sys.executable, "app/initial_data.py"
            ], check=True, capture_output=False)
        
        print("\n✅ Initial data setup completed successfully!")
        print("\n🎯 Next steps:")
        print("1. Start the development server:")
        print("   fastapi run --reload app/main.py")
        print("\n2. Visit the API documentation:")
        print("   http://localhost:8000/docs")
        print("\n3. Use the demo accounts to test the subscription system:")
        print("   - Subscription user: <EMAIL> / demopassword123")
        print("   - Credits user: <EMAIL> / creditspassword123")
        print("   - Admin user: <EMAIL> / changethis")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running initial data setup: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  Setup interrupted by user")
        sys.exit(1)

if __name__ == "__main__":
    main()
