#!/bin/bash

# 图片生成记录详情API测试脚本
# 测试新添加的获取生成记录详情接口

BASE_URL="http://127.0.0.1:8000"
# 请替换为有效的JWT token
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ0OTM5MzIsInN1YiI6IjQ4OGFhYWYyLTg3NTMtNDJjOS1hYmU0LTU4YTVjNDdkNDJmMiJ9.Blb3eEHBGWm1ZgLdb72hINdOfbh8XGPM69dTchlqySI"

echo "=== 图片生成记录详情API测试 ==="
echo

# 测试1: 测试有效的task_id（使用示例中的task_id）
echo "1. 测试有效的task_id"
TASK_ID="2857db2e20044233c72c35cbbb168008"
echo "Task ID: ${TASK_ID}"
curl -s --location --request GET "${BASE_URL}/api/v1/image/record-info/${TASK_ID}" \
--header "Authorization: Bearer ${TOKEN}" \
--header 'Content-Type: application/json' | jq '.'
echo
echo "---"
echo

# 测试2: 测试不存在的task_id
echo "2. 测试不存在的task_id"
FAKE_TASK_ID="nonexistent_task_id_12345"
echo "Task ID: ${FAKE_TASK_ID}"
curl -s --location --request GET "${BASE_URL}/api/v1/image/record-info/${FAKE_TASK_ID}" \
--header "Authorization: Bearer ${TOKEN}" \
--header 'Content-Type: application/json' | jq '.'
echo
echo "---"
echo

# 测试3: 测试空的task_id
echo "3. 测试空的task_id"
echo "Task ID: (empty)"
curl -s --location --request GET "${BASE_URL}/api/v1/image/record-info/" \
--header "Authorization: Bearer ${TOKEN}" \
--header 'Content-Type: application/json' | jq '.'
echo
echo "---"
echo

# 测试4: 测试无效的JWT token
echo "4. 测试无效的JWT token"
TASK_ID="2857db2e20044233c72c35cbbb168008"
echo "Task ID: ${TASK_ID}"
curl -s --location --request GET "${BASE_URL}/api/v1/image/record-info/${TASK_ID}" \
--header "Authorization: Bearer invalid_token" \
--header 'Content-Type: application/json' | jq '.'
echo
echo "---"
echo

# 测试5: 测试没有Authorization header
echo "5. 测试没有Authorization header"
TASK_ID="2857db2e20044233c72c35cbbb168008"
echo "Task ID: ${TASK_ID}"
curl -s --location --request GET "${BASE_URL}/api/v1/image/record-info/${TASK_ID}" \
--header 'Content-Type: application/json' | jq '.'
echo

echo "=== 测试完成 ==="
