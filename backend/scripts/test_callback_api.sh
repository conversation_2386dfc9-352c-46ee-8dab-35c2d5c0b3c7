#!/bin/bash

# 图片生成回调和设备token API测试脚本

BASE_URL="http://127.0.0.1:8000"
# 请替换为有效的JWT token
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ0OTM5MzIsInN1YiI6IjQ4OGFhYWYyLTg3NTMtNDJjOS1hYmU0LTU4YTVjNDdkNDJmMiJ9.Blb3eEHBGWm1ZgLdb72hINdOfbh8XGPM69dTchlqySI"

echo "=== 图片生成回调和设备token API测试 ==="
echo

# 测试1: 注册设备token
echo "1. 测试注册设备token"
curl -s --location --request POST "${BASE_URL}/api/v1/device-tokens/register" \
--header "Authorization: Bearer ${TOKEN}" \
--header 'Content-Type: application/json' \
--data-raw '{
    "device_token": "test_device_token_1234567890abcdef",
    "platform": "ios"
}' | jq '.'
echo
echo "---"
echo

# 测试2: 获取设备token列表
echo "2. 测试获取设备token列表"
curl -s --location --request GET "${BASE_URL}/api/v1/device-tokens/list" \
--header "Authorization: Bearer ${TOKEN}" \
--header 'Content-Type: application/json' | jq '.'
echo
echo "---"
echo

# 测试3: 检查设备token服务健康状态
echo "3. 测试设备token服务健康状态"
curl -s --location --request GET "${BASE_URL}/api/v1/device-tokens/health" \
--header 'Content-Type: application/json' | jq '.'
echo
echo "---"
echo

# 测试4: 模拟成功的图片生成回调
echo "4. 测试成功的图片生成回调"
curl -s --location --request POST "${BASE_URL}/api/v1/image/callback" \
--header 'Content-Type: application/json' \
--data-raw '{
    "code": 200,
    "msg": "success",
    "data": {
        "taskId": "test12345",
        "info": {
            "result_urls": [
                "https://example.com/result/image1.png"
            ]
        }
    }
}' | jq '.'
echo
echo "---"
echo

# 测试5: 模拟失败的图片生成回调
echo "5. 测试失败的图片生成回调"
curl -s --location --request POST "${BASE_URL}/api/v1/image/callback" \
--header 'Content-Type: application/json' \
--data-raw '{
    "code": 400,
    "msg": "您的内容被 OpenAI 标记为违反内容政策",
    "data": {
        "taskId": "test12345",
        "info": null
    }
}' | jq '.'
echo
echo "---"
echo

# 测试6: 测试无效的回调数据
echo "6. 测试无效的回调数据（缺少taskId）"
curl -s --location --request POST "${BASE_URL}/api/v1/image/callback" \
--header 'Content-Type: application/json' \
--data-raw '{
    "code": 200,
    "msg": "success",
    "data": {
        "info": {
            "result_urls": ["https://example.com/result.png"]
        }
    }
}' | jq '.'
echo
echo "---"
echo

# 测试7: 停用设备token
echo "7. 测试停用设备token"
curl -s --location --request DELETE "${BASE_URL}/api/v1/device-tokens/deactivate?device_token=test_device_token_1234567890abcdef" \
--header "Authorization: Bearer ${TOKEN}" \
--header 'Content-Type: application/json' | jq '.'
echo
echo "---"
echo

# 测试8: 测试图片生成（带callback配置）
echo "8. 测试图片生成（应该使用配置文件中的callback URL）"
curl -s --location --request POST "${BASE_URL}/api/v1/image/generate-image-sync" \
--header "Authorization: Bearer ${TOKEN}" \
--header 'Content-Type: application/json' \
--data-raw '{
    "prompt": "A beautiful sunset over mountains with callback test",
    "size": "1:1",
    "n_variants": 1
}' | jq '.'
echo

echo "=== 测试完成 ==="
echo
echo "注意事项："
echo "1. 确保服务器正在运行"
echo "2. 替换有效的JWT token"
echo "3. 回调测试可能找不到对应的生成记录，这是正常的"
echo "4. APNS推送需要正确配置才能工作"
echo "5. 设备token注册需要有效的用户认证"
