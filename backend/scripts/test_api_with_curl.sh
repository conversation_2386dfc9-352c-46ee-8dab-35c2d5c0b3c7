#!/bin/bash

# 图片生成API测试脚本
# 测试各种情况的API响应

BASE_URL="http://127.0.0.1:8000"
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ0OTM5MzIsInN1YiI6IjQ4OGFhYWYyLTg3NTMtNDJjOS1hYmU0LTU4YTVjNDdkNDJmMiJ9.Blb3eEHBGWm1ZgLdb72hINdOfbh8XGPM69dTchlqySI"

echo "=== 图片生成API测试 ==="
echo

# 测试1: 正常请求
echo "1. 测试正常请求"
curl -s --location --request POST "${BASE_URL}/api/v1/image/generate-image-sync" \
--header "Authorization: Bearer ${TOKEN}" \
--header 'Content-Type: application/json' \
--data-raw '{
    "prompt": "A beautiful sunset over mountains",
    "size": "1:1",
    "n_variants": 1
}' | jq '.'

echo
echo "---"
echo

# 测试2: 无效的尺寸
echo "2. 测试无效尺寸"
curl -s --location --request POST "${BASE_URL}/api/v1/image/generate-image-sync" \
--header "Authorization: Bearer ${TOKEN}" \
--header 'Content-Type: application/json' \
--data-raw '{
    "prompt": "A beautiful sunset over mountains",
    "size": "invalid_size",
    "n_variants": 1
}' | jq '.'

echo
echo "---"
echo

# 测试3: 空提示词
echo "3. 测试空提示词"
curl -s --location --request POST "${BASE_URL}/api/v1/image/generate-image-sync" \
--header "Authorization: Bearer ${TOKEN}" \
--header 'Content-Type: application/json' \
--data-raw '{
    "prompt": "",
    "size": "1:1",
    "n_variants": 1
}' | jq '.'

echo
echo "---"
echo

# 测试4: 无效的变体数量
echo "4. 测试无效变体数量"
curl -s --location --request POST "${BASE_URL}/api/v1/image/generate-image-sync" \
--header "Authorization: Bearer ${TOKEN}" \
--header 'Content-Type: application/json' \
--data-raw '{
    "prompt": "A beautiful sunset over mountains",
    "size": "1:1",
    "n_variants": 15
}' | jq '.'

echo
echo "---"
echo

# 测试5: 带参考图片的请求
echo "5. 测试带参考图片的请求"
curl -s --location --request POST "${BASE_URL}/api/v1/image/generate-image-sync" \
--header "Authorization: Bearer ${TOKEN}" \
--header 'Content-Type: application/json' \
--data-raw '{
    "files_url": [
        "https://image.wenhaofree.com/2025/07/44e676a8392bb570c2d29b03e263dfbe.jpeg"
    ],
    "prompt": "Elegant wedding dress style inspired by this image",
    "size": "16:9",
    "n_variants": 1,
    "is_enhance": true
}' | jq '.'

echo
echo "---"
echo

# 测试6: 健康检查
echo "6. 测试健康检查"
curl -s --location --request GET "${BASE_URL}/api/v1/image/image-generation/health" | jq '.'

echo
echo "---"
echo

# 测试7: 获取生成历史
echo "7. 测试获取生成历史"
curl -s --location --request GET "${BASE_URL}/api/v1/image/history?limit=3" \
--header "Authorization: Bearer ${TOKEN}" | jq '.'

echo
echo "=== 测试完成 ==="
