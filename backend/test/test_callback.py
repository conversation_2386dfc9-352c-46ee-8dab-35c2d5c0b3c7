#!/usr/bin/env python3
"""
测试图片生成回调接口的脚本

使用方法:
python test_callback.py --task-id task123 --success
python test_callback.py --task-id task123 --failed
"""

import requests
import json
import argparse
import sys

def test_callback(base_url: str, task_id: str, success: bool = True):
    """测试回调接口"""
    
    callback_url = f"{base_url}/api/v1/image/callback"
    
    if success:
        # 成功的回调数据
        callback_data = {
            "code": 200,
            "msg": "success",
            "data": {
                "taskId": task_id,
                "info": {
                    "result_urls": [
                        "https://example.com/result1.png",
                        "https://example.com/result2.png"
                    ]
                }
            }
        }
        print(f"🚀 Testing SUCCESS callback for task_id: {task_id}")
    else:
        # 失败的回调数据
        callback_data = {
            "code": 500,
            "msg": "Generation failed due to invalid prompt",
            "data": {
                "taskId": task_id
            }
        }
        print(f"💥 Testing FAILED callback for task_id: {task_id}")
    
    print(f"📤 Sending callback to: {callback_url}")
    print(f"📋 Callback data:")
    print(json.dumps(callback_data, indent=2, ensure_ascii=False))
    print()
    
    try:
        response = requests.post(
            callback_url,
            json=callback_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📥 Response Status: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        
        try:
            response_data = response.json()
            print(f"📊 Response Data:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
        except json.JSONDecodeError:
            print(f"📄 Response Text: {response.text}")
        
        if response.status_code == 200:
            print("✅ Callback test PASSED")
            return True
        else:
            print("❌ Callback test FAILED")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"💥 Request failed: {str(e)}")
        return False


def create_test_record(base_url: str, task_id: str, user_token: str):
    """创建测试记录"""
    
    print(f"🔧 Creating test record for task_id: {task_id}")
    
    # 先调用图片生成接口创建记录
    generate_url = f"{base_url}/api/v1/image/generate-image"
    
    generate_data = {
        "prompt": f"Test image for task {task_id}",
        "size": "1:1",
        "n_variants": 1
    }
    
    try:
        response = requests.post(
            generate_url,
            json=generate_data,
            headers={
                "Authorization": f"Bearer {user_token}",
                "Content-Type": "application/json"
            },
            timeout=30
        )
        
        print(f"📥 Generate Response Status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"✅ Test record created successfully")
            print(f"📊 Response Data:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            return True
        else:
            print(f"❌ Failed to create test record: {response.status_code}")
            try:
                error_data = response.json()
                print(json.dumps(error_data, indent=2, ensure_ascii=False))
            except:
                print(response.text)
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"💥 Request failed: {str(e)}")
        return False


def main():
    parser = argparse.ArgumentParser(description="测试图片生成回调接口")
    parser.add_argument("--base-url", default="http://127.0.0.1:8000", help="API基础URL")
    parser.add_argument("--task-id", required=True, help="任务ID")
    parser.add_argument("--success", action="store_true", help="测试成功回调")
    parser.add_argument("--failed", action="store_true", help="测试失败回调")
    parser.add_argument("--create-record", action="store_true", help="先创建测试记录")
    parser.add_argument("--user-token", help="用户认证令牌（创建记录时需要）")
    
    args = parser.parse_args()
    
    if not args.success and not args.failed:
        print("❌ 请指定 --success 或 --failed")
        sys.exit(1)
    
    if args.create_record:
        if not args.user_token:
            print("❌ 创建记录需要提供 --user-token")
            sys.exit(1)
        
        print("=" * 60)
        print("🔧 STEP 1: Creating test record")
        print("=" * 60)
        
        if not create_test_record(args.base_url, args.task_id, args.user_token):
            print("❌ Failed to create test record")
            sys.exit(1)
        
        print()
    
    print("=" * 60)
    print("📞 STEP 2: Testing callback")
    print("=" * 60)
    
    success = args.success
    if test_callback(args.base_url, args.task_id, success):
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n💥 Tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
