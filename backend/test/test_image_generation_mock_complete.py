#!/usr/bin/env python3
"""
图片生成Mock功能综合测试

测试 /generate-image 和 /record-info/ 两个接口的Mock功能
验证完整的图片生成流程Mock
"""

import sys
import os
import json
import requests
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def get_auth_token():
    """获取认证token"""
    login_data = {
        "username": "<EMAIL>",
        "password": "changethis"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/login/access-token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            return result.get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return None

def test_complete_image_generation_flow():
    """测试完整的图片生成流程Mock"""
    print("🧪 测试完整的图片生成流程Mock...")
    
    # 获取认证token
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token")
        return False
    
    print("✅ 成功获取认证token")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 步骤1: 提交图片生成任务
    print("\n📤 步骤1: 提交图片生成任务...")
    
    generation_request = {
        "prompt": "Beautiful wedding photography, bride and groom, outdoor ceremony",
        "size": "1:1",
        "n_variants": 1,
        "is_enhance": False,
        "files_url": [
            "https://img-bridal.wenhaofree.com/uploads/reference_image.jpeg"
        ],
        "callback_url": "https://myapp.com/webhook/image-complete"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/image/generate-image",
            json=generation_request,
            headers=headers,
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ 图片生成任务提交失败: {response.status_code}")
            return False
        
        generation_result = response.json()
        task_id = generation_result.get("data", {}).get("task_id")
        generation_record_id = generation_result.get("data", {}).get("generation_record_id")
        
        print(f"✅ 图片生成任务提交成功!")
        print(f"  任务ID: {task_id}")
        print(f"  生成记录ID: {generation_record_id}")
        print(f"  状态: {generation_result.get('data', {}).get('status')}")
        
        if not task_id:
            print("❌ 未获取到任务ID")
            return False
        
    except Exception as e:
        print(f"❌ 图片生成任务提交异常: {str(e)}")
        return False
    
    # 步骤2: 查询任务状态
    print(f"\n📋 步骤2: 查询任务状态...")
    print(f"  查询任务ID: {task_id}")
    
    try:
        response = requests.get(
            f"http://localhost:8000/api/v1/image/record-info/{task_id}",
            headers=headers,
            timeout=30
        )
        
        if response.status_code != 200:
            print(f"❌ 任务状态查询失败: {response.status_code}")
            return False
        
        status_result = response.json()
        returned_task_id = status_result.get("data", {}).get("taskId")
        status = status_result.get("data", {}).get("status")
        result_urls = status_result.get("data", {}).get("response", {}).get("resultUrls", [])
        
        print(f"✅ 任务状态查询成功!")
        print(f"  返回任务ID: {returned_task_id}")
        print(f"  任务状态: {status}")
        print(f"  结果URL数量: {len(result_urls)}")
        
        if result_urls:
            print(f"  结果URL: {result_urls[0]}")
        
        # 验证任务ID一致性
        if returned_task_id != task_id:
            print(f"❌ 任务ID不一致: 提交时={task_id}, 查询时={returned_task_id}")
            return False
        
        print(f"✅ 任务ID一致性验证通过!")
        
    except Exception as e:
        print(f"❌ 任务状态查询异常: {str(e)}")
        return False
    
    # 步骤3: 验证Mock数据特征
    print(f"\n🔍 步骤3: 验证Mock数据特征...")
    
    # 验证生成任务响应
    generation_data = generation_result.get("data", {})
    expected_generation_fields = {
        "code": 0,
        "message": "Image generation task submitted successfully",
        "task_id": "92e524b7d976604631efffd0f9ba1fc7",
        "status": "pending",
        "estimated_completion_time": "30-60 seconds"
    }
    
    for field, expected_value in expected_generation_fields.items():
        if field == "code" or field == "message":
            actual_value = generation_result.get(field)
        else:
            actual_value = generation_data.get(field)
        
        if actual_value == expected_value:
            print(f"  ✅ 生成任务 {field}: {actual_value}")
        else:
            print(f"  ❌ 生成任务 {field}: 期望={expected_value}, 实际={actual_value}")
            return False
    
    # 验证状态查询响应
    status_data = status_result.get("data", {})
    expected_status_fields = {
        "code": 200,
        "msg": "success",
        "taskId": "92e524b7d976604631efffd0f9ba1fc7",
        "status": "SUCCESS",
        "successFlag": 1,
        "progress": "1.00"
    }
    
    for field, expected_value in expected_status_fields.items():
        if field == "code" or field == "msg":
            actual_value = status_result.get(field)
        else:
            actual_value = status_data.get(field)
        
        if actual_value == expected_value:
            print(f"  ✅ 状态查询 {field}: {actual_value}")
        else:
            print(f"  ❌ 状态查询 {field}: 期望={expected_value}, 实际={actual_value}")
            return False
    
    print(f"✅ Mock数据特征验证通过!")
    return True

def test_mock_consistency():
    """测试Mock数据的一致性"""
    print("\n🧪 测试Mock数据一致性...")
    
    token = get_auth_token()
    if not token:
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 多次调用生成接口，验证任务ID一致性
    task_ids = []
    for i in range(3):
        request_data = {
            "prompt": f"Test prompt {i+1}",
            "size": "1:1",
            "n_variants": 1,
            "is_enhance": False
        }
        
        try:
            response = requests.post(
                "http://localhost:8000/api/v1/image/generate-image",
                json=request_data,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                task_id = result.get("data", {}).get("task_id")
                task_ids.append(task_id)
                print(f"  第{i+1}次调用任务ID: {task_id}")
            else:
                print(f"  ❌ 第{i+1}次调用失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ 第{i+1}次调用异常: {str(e)}")
            return False
    
    # 验证所有任务ID都相同
    if len(set(task_ids)) == 1:
        print(f"✅ Mock任务ID一致性验证通过: {task_ids[0]}")
        return True
    else:
        print(f"❌ Mock任务ID不一致: {task_ids}")
        return False

def main():
    """主函数"""
    print("🔧 图片生成Mock功能综合测试")
    print("=" * 60)
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code != 200:
            print("❌ FastAPI服务器未正常运行")
            return False
    except:
        print("❌ 无法连接到FastAPI服务器")
        print("请确保:")
        print("1. FastAPI服务器正在运行: uv run uvicorn app.main:app --reload")
        print("2. 环境变量 ENABLE_IMAGE_GENERATION_MOCK=True")
        return False
    
    print("✅ FastAPI服务器运行正常")
    
    # 执行测试
    test_results = []
    
    # 测试1: 完整流程Mock
    test_results.append(test_complete_image_generation_flow())
    
    # 测试2: Mock数据一致性
    test_results.append(test_mock_consistency())
    
    print("\n" + "=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
        print("✅ 图片生成Mock功能完整且一致")
        print("\n📋 测试总结:")
        print("  ✅ /generate-image 接口Mock功能正常")
        print("  ✅ /record-info/ 接口Mock功能正常")
        print("  ✅ 任务ID在两个接口间保持一致")
        print("  ✅ Mock数据格式符合预期")
        print("  ✅ 多次调用返回一致的Mock数据")
        print("\n💡 Mock模式特性:")
        print("  - 固定任务ID: 92e524b7d976604631efffd0f9ba1fc7")
        print("  - 生成记录ID: 随机UUID")
        print("  - 任务状态: pending → SUCCESS")
        print("  - 无需真实API调用，适合开发测试")
    else:
        print(f"💥 部分测试失败! ({passed}/{total})")
        print("❌ 需要检查Mock功能实现")
    
    return passed == total

if __name__ == "__main__":
    main()
