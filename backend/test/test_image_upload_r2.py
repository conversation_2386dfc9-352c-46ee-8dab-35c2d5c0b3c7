#!/usr/bin/env python3
"""
测试Cloudflare R2图片上传服务

使用方法:
1. 配置测试: python test_image_upload_r2.py test-config
2. 测试字节上传: python test_image_upload_r2.py test-bytes
3. 测试URL上传: python test_image_upload_r2.py test-url
4. 测试文件上传: python test_image_upload_r2.py test-file <file_path>
5. 测试删除: python test_image_upload_r2.py test-delete <object_name>
"""

import sys
import logging
import requests
from io import BytesIO

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入我们的服务
from app.services.image_upload_r2 import (
    CloudflareR2Service, 
    ImageUploadRequest,
    get_r2_service,
    upload_image_bytes,
    upload_image_url,
    upload_image_file
)


def test_config():
    """测试R2配置"""
    print("=== 测试R2配置 ===")
    try:
        service = CloudflareR2Service()
        print(f"✅ R2服务初始化成功")
        print(f"   账户ID: {service.account_id}")
        print(f"   存储桶: {service.bucket}")
        print(f"   端点: {service.endpoint}")
        print(f"   公共域名: {service.public_domain or '未设置'}")
        return True
    except Exception as e:
        print(f"❌ R2配置错误: {e}")
        return False


def create_test_image() -> bytes:
    """创建一个测试图片"""
    # 创建一个简单的JPEG文件头（最小可用的JPEG）
    # 这是一个1x1像素的红色JPEG图片
    jpeg_data = bytes([
        0xFF, 0xD8, 0xFF, 0xE0, 0x00, 0x10, 0x4A, 0x46, 0x49, 0x46, 0x00, 0x01,
        0x01, 0x01, 0x00, 0x48, 0x00, 0x48, 0x00, 0x00, 0xFF, 0xDB, 0x00, 0x43,
        0x00, 0x08, 0x06, 0x06, 0x07, 0x06, 0x05, 0x08, 0x07, 0x07, 0x07, 0x09,
        0x09, 0x08, 0x0A, 0x0C, 0x14, 0x0D, 0x0C, 0x0B, 0x0B, 0x0C, 0x19, 0x12,
        0x13, 0x0F, 0x14, 0x1D, 0x1A, 0x1F, 0x1E, 0x1D, 0x1A, 0x1C, 0x1C, 0x20,
        0x24, 0x2E, 0x27, 0x20, 0x22, 0x2C, 0x23, 0x1C, 0x1C, 0x28, 0x37, 0x29,
        0x2C, 0x30, 0x31, 0x34, 0x34, 0x34, 0x1F, 0x27, 0x39, 0x3D, 0x38, 0x32,
        0x3C, 0x2E, 0x33, 0x34, 0x32, 0xFF, 0xC0, 0x00, 0x11, 0x08, 0x00, 0x01,
        0x00, 0x01, 0x01, 0x01, 0x11, 0x00, 0x02, 0x11, 0x01, 0x03, 0x11, 0x01,
        0xFF, 0xC4, 0x00, 0x14, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xFF, 0xC4,
        0x00, 0x14, 0x10, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xDA, 0x00, 0x0C,
        0x03, 0x01, 0x00, 0x02, 0x11, 0x03, 0x11, 0x00, 0x3F, 0x00, 0x80, 0xFF, 0xD9
    ])
    return jpeg_data


def test_bytes_upload():
    """测试字节数据上传"""
    print("\n=== 测试字节数据上传 ===")
    try:
        # 创建测试图片
        image_data = create_test_image()
        print(f"创建测试图片，大小: {len(image_data)} 字节")
        
        # 上传图片
        result = upload_image_bytes(
            image_data=image_data,
            file_name="test_bytes.jpg",
            folder="test"
        )
        
        if result.success:
            print(f"✅ 上传成功!")
            print(f"   URL: {result.url}")
            print(f"   对象名: {result.object_name}")
            print(f"   数据: {result.data}")
            return result.object_name
        else:
            print(f"❌ 上传失败: {result.error}")
            return None
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return None


def test_url_upload():
    """测试URL上传"""
    print("\n=== 测试URL上传 ===")
    try:
        # 使用一个公开的测试图片URL
        test_url = "https://httpbin.org/image/jpeg"
        print(f"从URL下载图片: {test_url}")
        
        # 上传图片
        result = upload_image_url(
            image_url=test_url,
            file_name="test_url.jpg",
            folder="test"
        )
        
        if result.success:
            print(f"✅ 上传成功!")
            print(f"   URL: {result.url}")
            print(f"   对象名: {result.object_name}")
            print(f"   数据: {result.data}")
            return result.object_name
        else:
            print(f"❌ 上传失败: {result.error}")
            return None
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return None


def test_file_upload(file_path: str):
    """测试文件上传"""
    print(f"\n=== 测试文件上传: {file_path} ===")
    try:
        # 上传文件
        result = upload_image_file(
            file_path=file_path,
            folder="test"
        )
        
        if result.success:
            print(f"✅ 上传成功!")
            print(f"   URL: {result.url}")
            print(f"   对象名: {result.object_name}")
            print(f"   数据: {result.data}")
            return result.object_name
        else:
            print(f"❌ 上传失败: {result.error}")
            return None
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return None


def test_delete(object_name: str):
    """测试删除图片"""
    print(f"\n=== 测试删除图片: {object_name} ===")
    try:
        service = get_r2_service()
        result = service.delete_image(object_name)
        
        if result.success:
            print(f"✅ 删除成功!")
            print(f"   数据: {result.data}")
            return True
        else:
            print(f"❌ 删除失败: {result.error}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print(__doc__)
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "test-config":
        success = test_config()
        sys.exit(0 if success else 1)
        
    elif command == "test-bytes":
        if not test_config():
            sys.exit(1)
        object_name = test_bytes_upload()
        if object_name:
            print(f"\n提示: 可以使用以下命令删除测试文件:")
            print(f"python {sys.argv[0]} test-delete {object_name}")
        sys.exit(0 if object_name else 1)
        
    elif command == "test-url":
        if not test_config():
            sys.exit(1)
        object_name = test_url_upload()
        if object_name:
            print(f"\n提示: 可以使用以下命令删除测试文件:")
            print(f"python {sys.argv[0]} test-delete {object_name}")
        sys.exit(0 if object_name else 1)
        
    elif command == "test-file":
        if len(sys.argv) < 3:
            print("错误: 请提供文件路径")
            print("用法: python test_image_upload_r2.py test-file <file_path>")
            sys.exit(1)
        if not test_config():
            sys.exit(1)
        file_path = sys.argv[2]
        object_name = test_file_upload(file_path)
        if object_name:
            print(f"\n提示: 可以使用以下命令删除测试文件:")
            print(f"python {sys.argv[0]} test-delete {object_name}")
        sys.exit(0 if object_name else 1)
        
    elif command == "test-delete":
        if len(sys.argv) < 3:
            print("错误: 请提供对象名称")
            print("用法: python test_image_upload_r2.py test-delete <object_name>")
            sys.exit(1)
        if not test_config():
            sys.exit(1)
        object_name = sys.argv[2]
        success = test_delete(object_name)
        sys.exit(0 if success else 1)
        
    else:
        print(f"未知命令: {command}")
        print(__doc__)
        sys.exit(1)


if __name__ == "__main__":
    main()
