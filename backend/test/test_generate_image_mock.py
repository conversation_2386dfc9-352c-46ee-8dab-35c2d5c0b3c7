#!/usr/bin/env python3
"""
测试 /generate-image 接口的Mock功能

验证当 ENABLE_IMAGE_GENERATION_MOCK=True 时，接口返回mock数据
"""

import sys
import os
import json
import requests

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def get_auth_token():
    """获取认证token"""
    login_data = {
        "username": "<EMAIL>",
        "password": "changethis"
    }
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/login/access-token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            return result.get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return None

def test_generate_image_mock():
    """测试generate-image接口的mock功能"""
    print("🧪 测试 /generate-image 接口Mock功能...")
    
    # 获取认证token
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token")
        return False
    
    print("✅ 成功获取认证token")
    
    # 准备请求头
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 准备图片生成请求数据
    request_data = {
        "prompt": "Beautiful wedding photography, outdoor ceremony, natural lighting",
        "size": "1:1",
        "n_variants": 1,
        "is_enhance": False,
        "files_url": [
            "https://img-bridal.wenhaofree.com/uploads/test_image.jpeg"
        ],
        "callback_url": "https://example.com/callback"
    }
    
    print(f"📝 发送图片生成请求...")
    print(f"  提示词: {request_data['prompt']}")
    print(f"  尺寸: {request_data['size']}")
    print(f"  变体数量: {request_data['n_variants']}")
    
    try:
        # 发送请求
        response = requests.post(
            "http://localhost:8000/api/v1/image/generate-image",
            json=request_data,
            headers=headers,
            timeout=30
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ 请求成功!")
            print(f"📊 响应数据结构验证:")
            
            # 验证响应结构
            required_fields = ["code", "message", "data"]
            data_fields = [
                "task_id", "generation_record_id", "status", 
                "estimated_completion_time", "callback_url",
                "remaining_credits", "remaining_monthly_usage",
                "subscription_status", "trial_message", 
                "is_trial_user", "api_response"
            ]
            
            # 检查顶级字段
            for field in required_fields:
                if field in result:
                    print(f"  ✅ {field}: {result[field]}")
                else:
                    print(f"  ❌ 缺少字段: {field}")
                    return False
            
            # 检查data字段
            data = result.get("data", {})
            for field in data_fields:
                if field in data:
                    value = data[field]
                    if field == "task_id":
                        if value == "92e524b7d976604631efffd0f9ba1fc7":
                            print(f"  ✅ {field}: {value} (正确的mock任务ID)")
                        else:
                            print(f"  ⚠️ {field}: {value} (非预期的任务ID)")
                    elif field == "generation_record_id":
                        if value and len(str(value)) > 10:  # UUID格式检查
                            print(f"  ✅ {field}: {value} (有效的UUID)")
                        else:
                            print(f"  ❌ {field}: {value} (无效的UUID)")
                            return False
                    elif field == "status":
                        if value == "pending":
                            print(f"  ✅ {field}: {value}")
                        else:
                            print(f"  ❌ {field}: {value} (期望: pending)")
                            return False
                    elif field == "estimated_completion_time":
                        if value == "30-60 seconds":
                            print(f"  ✅ {field}: {value}")
                        else:
                            print(f"  ❌ {field}: {value} (期望: 30-60 seconds)")
                            return False
                    else:
                        print(f"  ✅ {field}: {value}")
                else:
                    print(f"  ❌ 缺少data字段: {field}")
                    return False
            
            # 验证mock数据的特征
            if (result.get("code") == 0 and 
                result.get("message") == "Image generation task submitted successfully" and
                data.get("task_id") == "92e524b7d976604631efffd0f9ba1fc7"):
                print(f"✅ Mock数据验证通过!")
                return True
            else:
                print(f"❌ Mock数据验证失败")
                return False
                
        else:
            print(f"❌ 请求失败:")
            try:
                error_detail = response.json()
                print(f"   错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ 连接失败: 请确保 FastAPI 服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

def test_different_request_parameters():
    """测试不同请求参数的mock响应"""
    print("\n🧪 测试不同请求参数的Mock响应...")
    
    # 获取认证token
    token = get_auth_token()
    if not token:
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试不同的请求参数
    test_cases = [
        {
            "name": "基本文本生成",
            "data": {
                "prompt": "Simple wedding photo",
                "size": "16:9",
                "n_variants": 2,
                "is_enhance": True
            }
        },
        {
            "name": "带参考图片",
            "data": {
                "prompt": "Wedding photography with reference",
                "size": "1:1",
                "n_variants": 1,
                "is_enhance": False,
                "files_url": [
                    "https://example.com/image1.jpg",
                    "https://example.com/image2.jpg"
                ]
            }
        },
        {
            "name": "带回调URL",
            "data": {
                "prompt": "Professional wedding shoot",
                "size": "4:3",
                "n_variants": 3,
                "is_enhance": True,
                "callback_url": "https://myapp.com/webhook/image-complete"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"📝 测试用例: {test_case['name']}")
        
        try:
            response = requests.post(
                "http://localhost:8000/api/v1/image/generate-image",
                json=test_case['data'],
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                task_id = result.get("data", {}).get("task_id")
                
                if task_id == "92e524b7d976604631efffd0f9ba1fc7":
                    print(f"  ✅ 返回正确的mock任务ID: {task_id}")
                else:
                    print(f"  ❌ 任务ID不正确: {task_id}")
                    return False
            else:
                print(f"  ❌ 请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ 请求异常: {str(e)}")
            return False
    
    print(f"✅ 所有参数测试通过!")
    return True

def main():
    """主函数"""
    print("🔧 /generate-image 接口Mock功能测试")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code != 200:
            print("❌ FastAPI服务器未正常运行")
            return False
    except:
        print("❌ 无法连接到FastAPI服务器")
        print("请确保:")
        print("1. FastAPI服务器正在运行: uv run uvicorn app.main:app --reload")
        print("2. 环境变量 ENABLE_IMAGE_GENERATION_MOCK=True")
        return False
    
    print("✅ FastAPI服务器运行正常")
    
    # 执行测试
    test_results = []
    
    # 测试1: 基本Mock功能
    test_results.append(test_generate_image_mock())
    
    # 测试2: 不同请求参数
    test_results.append(test_different_request_parameters())
    
    print("\n" + "=" * 50)
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
        print("✅ /generate-image 接口Mock功能正常工作")
        print("\n💡 使用说明:")
        print("  - 设置环境变量 ENABLE_IMAGE_GENERATION_MOCK=True 启用Mock模式")
        print("  - Mock模式下，接口会返回模拟的任务提交成功数据")
        print("  - 任务ID固定为: 92e524b7d976604631efffd0f9ba1fc7")
        print("  - 生成记录ID为随机UUID")
    else:
        print(f"💥 部分测试失败! ({passed}/{total})")
        print("❌ 需要检查Mock功能实现")
    
    return passed == total

if __name__ == "__main__":
    main()
