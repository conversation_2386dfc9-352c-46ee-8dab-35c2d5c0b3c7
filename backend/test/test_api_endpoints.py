#!/usr/bin/env python3
"""
图片生成API端点测试脚本

测试所有图片生成相关的API端点功能
"""

import sys
import os
import requests
import json
from datetime import datetime, timezone, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_EMAIL = "<EMAIL>"  # 使用默认超级用户
TEST_PASSWORD = "changethis"


def get_jwt_token():
    """获取JWT token"""
    print("1. 获取JWT token...")
    
    login_data = {
        "username": TEST_EMAIL,
        "password": TEST_PASSWORD
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/login/access-token",
        data=login_data
    )
    
    if response.status_code == 200:
        token = response.json()["access_token"]
        print(f"   ✓ 成功获取token: {token[:20]}...")
        return token
    else:
        print(f"   ✗ 获取token失败: {response.status_code}")
        print(f"   响应: {response.text}")
        return None


def test_health_check():
    """测试健康检查端点"""
    print("\n2. 测试健康检查端点...")
    
    response = requests.get(f"{BASE_URL}/api/v1/image/image-generation/health")
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✓ 健康检查成功")
        print(f"   状态: {data['status']}")
        print(f"   消息: {data['message']}")
        print(f"   API URL: {data['api_url']}")
        return True
    else:
        print(f"   ✗ 健康检查失败: {response.status_code}")
        return False


def test_user_subscription_status(token):
    """测试用户订阅状态"""
    print("\n3. 测试用户订阅状态...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(
        f"{BASE_URL}/api/v1/subscriptions/status",
        headers=headers
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✓ 获取订阅状态成功")
        print(f"   有效订阅: {'✓' if data['has_active_subscription'] else '✗'}")
        print(f"   月度限制: {data['monthly_limit']}")
        print(f"   已使用: {data['monthly_usage_count']}")
        print(f"   可用积分: {data['total_credits']}")
        print(f"   可使用服务: {'✓' if data['can_use_service'] else '✗'}")
        return data
    else:
        print(f"   ✗ 获取订阅状态失败: {response.status_code}")
        print(f"   响应: {response.text}")
        return None


def test_access_check(token):
    """测试权限检查"""
    print("\n4. 测试权限检查...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(
        f"{BASE_URL}/api/v1/subscriptions/check-access?usage_type=image_generation",
        headers=headers
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✓ 权限检查成功")
        print(f"   可访问: {'✓' if data['can_access'] else '✗'}")
        if not data['can_access']:
            print(f"   原因: {data['reason']}")
        print(f"   剩余积分: {data['remaining_credits']}")
        print(f"   剩余月度使用: {data['remaining_monthly_usage']}")
        print(f"   订阅状态: {data['subscription_status']}")
        return data
    else:
        print(f"   ✗ 权限检查失败: {response.status_code}")
        print(f"   响应: {response.text}")
        return None


def test_image_generation_sync(token):
    """测试同步图片生成"""
    print("\n5. 测试同步图片生成...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "prompt": "A beautiful sunset over the mountains with vibrant colors",
        "size": "1:1",
        "n_variants": 1,
        "is_enhance": False
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/image/generate-image-sync",
        headers=headers,
        json=data
    )
    
    print(f"   响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"   ✓ 图片生成请求成功")
        print(f"   成功: {'✓' if result['success'] else '✗'}")
        print(f"   消息: {result['message']}")
        
        if result.get('data'):
            print("   响应数据:")
            for key, value in result['data'].items():
                if key not in ['code', 'msg']:  # 跳过API错误信息
                    print(f"     {key}: {value}")
        
        if result.get('error'):
            print(f"   错误: {result['error']}")
        
        return result
    else:
        print(f"   ✗ 图片生成请求失败: {response.status_code}")
        try:
            error_data = response.json()
            print(f"   错误详情: {error_data}")
        except:
            print(f"   响应内容: {response.text}")
        return None


def test_image_generation_async(token):
    """测试异步图片生成"""
    print("\n6. 测试异步图片生成...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "prompt": "A futuristic city with flying cars at night",
        "size": "16:9",
        "n_variants": 1,
        "callback_url": "https://example.com/callback"
    }
    
    response = requests.post(
        f"{BASE_URL}/api/v1/image/generate-image",
        headers=headers,
        json=data
    )
    
    print(f"   响应状态码: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"   ✓ 异步图片生成请求成功")
        print(f"   成功: {'✓' if result['success'] else '✗'}")
        print(f"   消息: {result['message']}")
        
        if result.get('data'):
            print("   响应数据:")
            for key, value in result['data'].items():
                if key not in ['code', 'msg']:
                    print(f"     {key}: {value}")
        
        return result
    else:
        print(f"   ✗ 异步图片生成请求失败: {response.status_code}")
        try:
            error_data = response.json()
            print(f"   错误详情: {error_data}")
        except:
            print(f"   响应内容: {response.text}")
        return None


def test_generation_history(token):
    """测试生成历史查询"""
    print("\n7. 测试生成历史查询...")
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 获取历史记录列表
    response = requests.get(
        f"{BASE_URL}/api/v1/image/history?skip=0&limit=5",
        headers=headers
    )
    
    if response.status_code == 200:
        data = response.json()
        print(f"   ✓ 获取历史记录成功")
        print(f"   总记录数: {data['count']}")
        print(f"   返回记录数: {len(data['data'])}")
        
        for i, record in enumerate(data['data'][:3]):  # 只显示前3条
            print(f"   记录 {i+1}:")
            print(f"     ID: {record['id']}")
            print(f"     状态: {record['status']}")
            print(f"     提示词: {record['prompt'][:50]}...")
            print(f"     创建时间: {record['created_at']}")
        
        # 如果有记录，测试获取特定记录详情
        if data['data']:
            record_id = data['data'][0]['id']
            print(f"\n   测试获取特定记录详情: {record_id}")
            
            detail_response = requests.get(
                f"{BASE_URL}/api/v1/image/history/{record_id}",
                headers=headers
            )
            
            if detail_response.status_code == 200:
                detail_data = detail_response.json()
                print(f"   ✓ 获取记录详情成功")
                print(f"   详情: {detail_data['prompt']}")
            else:
                print(f"   ✗ 获取记录详情失败: {detail_response.status_code}")
        
        return data
    else:
        print(f"   ✗ 获取历史记录失败: {response.status_code}")
        try:
            error_data = response.json()
            print(f"   错误详情: {error_data}")
        except:
            print(f"   响应内容: {response.text}")
        return None


def main():
    """主测试函数"""
    print("=== 图片生成API端点测试 ===\n")
    
    # 1. 获取JWT token
    token = get_jwt_token()
    if not token:
        print("无法获取JWT token，测试终止")
        return
    
    # 2. 测试健康检查
    health_ok = test_health_check()
    
    # 3. 测试用户订阅状态
    subscription_status = test_user_subscription_status(token)
    
    # 4. 测试权限检查
    access_check = test_access_check(token)
    
    # 5. 测试同步图片生成
    sync_result = test_image_generation_sync(token)
    
    # 6. 测试异步图片生成
    async_result = test_image_generation_async(token)
    
    # 7. 测试生成历史查询
    history_result = test_generation_history(token)
    
    # 总结
    print("\n=== 测试总结 ===")
    print(f"健康检查: {'✓' if health_ok else '✗'}")
    print(f"订阅状态查询: {'✓' if subscription_status else '✗'}")
    print(f"权限检查: {'✓' if access_check else '✗'}")
    print(f"同步图片生成: {'✓' if sync_result else '✗'}")
    print(f"异步图片生成: {'✓' if async_result else '✗'}")
    print(f"历史记录查询: {'✓' if history_result else '✗'}")
    
    print("\n注意事项:")
    print("1. 图片生成可能因为API token无效而返回401错误")
    print("2. 权限检查和使用记录功能应该正常工作")
    print("3. 确保FastAPI服务正在运行在localhost:8000")
    print("4. 使用的是默认超级用户账号进行测试")


if __name__ == "__main__":
    main()
