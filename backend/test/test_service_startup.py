#!/usr/bin/env python3
"""
服务启动验证脚本
验证FastAPI服务是否正常启动并且所有关键端点都正常工作
"""

import requests
import json
import time
from datetime import datetime

def test_service_startup():
    """测试服务启动和关键功能"""
    
    print("🚀 FastAPI服务启动验证")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 测试用例
    test_cases = [
        {
            "name": "API文档页面",
            "url": f"{base_url}/docs",
            "method": "GET",
            "expected_status": 200,
            "check_content": "swagger"
        },
        {
            "name": "OpenAPI规范",
            "url": f"{base_url}/api/v1/openapi.json",
            "method": "GET",
            "expected_status": 200,
            "check_content": "openapi"
        },
        {
            "name": "图片生成健康检查",
            "url": f"{base_url}/api/v1/image/image-generation/health",
            "method": "GET",
            "expected_status": 200,
            "check_content": "healthy"
        },
        {
            "name": "回调接口 - 有效数据",
            "url": f"{base_url}/api/v1/image/callback",
            "method": "POST",
            "data": {
                "code": 200,
                "msg": "success",
                "data": {
                    "taskId": "test-startup-verification",
                    "info": {
                        "result_urls": ["https://example.com/test.png"]
                    }
                }
            },
            "expected_status": 404,  # 任务不存在是正常的
            "check_content": "CALLBACK_TASK_NOT_FOUND"
        },
        {
            "name": "回调接口 - 无效数据",
            "url": f"{base_url}/api/v1/image/callback",
            "method": "POST",
            "data": {
                "code": 200,
                "msg": "success"
                # 缺少data字段
            },
            "expected_status": 400,
            "check_content": "CALLBACK_INVALID_DATA"
        },
        {
            "name": "回调接口 - JSON格式错误",
            "url": f"{base_url}/api/v1/image/callback",
            "method": "POST",
            "raw_data": "invalid json",
            "expected_status": 400,
            "check_content": "CALLBACK_INVALID_JSON"
        }
    ]
    
    passed_tests = 0
    total_tests = len(test_cases)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试 {i}/{total_tests}: {test_case['name']}")
        
        try:
            # 发送请求
            if test_case['method'] == 'GET':
                response = requests.get(test_case['url'], timeout=10)
            elif test_case['method'] == 'POST':
                if 'raw_data' in test_case:
                    response = requests.post(
                        test_case['url'],
                        headers={"Content-Type": "application/json"},
                        data=test_case['raw_data'],
                        timeout=10
                    )
                else:
                    response = requests.post(
                        test_case['url'],
                        headers={"Content-Type": "application/json"},
                        json=test_case['data'],
                        timeout=10
                    )
            
            # 检查状态码
            expected_status = test_case['expected_status']
            if response.status_code == expected_status:
                print(f"✅ HTTP状态码正确: {response.status_code}")
                status_ok = True
            else:
                print(f"❌ HTTP状态码错误: 期望 {expected_status}, 实际 {response.status_code}")
                status_ok = False
            
            # 检查响应内容
            content_ok = True
            if 'check_content' in test_case:
                response_text = response.text.lower()
                check_content = test_case['check_content'].lower()
                if check_content in response_text:
                    print(f"✅ 响应内容包含期望内容: {test_case['check_content']}")
                else:
                    print(f"❌ 响应内容不包含期望内容: {test_case['check_content']}")
                    content_ok = False
            
            # 显示响应内容（截断）
            if response.headers.get('content-type', '').startswith('application/json'):
                try:
                    response_json = response.json()
                    response_str = json.dumps(response_json, indent=2, ensure_ascii=False)
                    if len(response_str) > 200:
                        response_str = response_str[:200] + "..."
                    print(f"📄 响应内容: {response_str}")
                except json.JSONDecodeError:
                    print(f"📄 响应内容: {response.text[:200]}...")
            else:
                print(f"📄 响应内容: {response.text[:100]}...")
            
            # 判断测试是否通过
            if status_ok and content_ok:
                print(f"✅ 测试通过")
                passed_tests += 1
            else:
                print(f"❌ 测试失败")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ 连接失败 - 服务器可能未启动")
        except requests.exceptions.Timeout:
            print(f"❌ 请求超时")
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    # 总结
    print("\n" + "=" * 50)
    print(f"📊 测试结果总结:")
    print(f"通过测试: {passed_tests}/{total_tests}")
    print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！服务启动成功！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查服务配置")
        return False

def test_service_endpoints():
    """测试主要服务端点"""
    
    print("\n🔍 主要端点验证")
    print("=" * 30)
    
    endpoints = [
        "/docs",
        "/api/v1/openapi.json",
        "/api/v1/image/image-generation/health",
        # 可以添加更多端点
    ]
    
    base_url = "http://localhost:8000"
    
    for endpoint in endpoints:
        url = f"{base_url}{endpoint}"
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint} - 正常")
            else:
                print(f"⚠️ {endpoint} - HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} - 错误: {e}")

def show_service_info():
    """显示服务信息"""
    
    print("\n📋 服务信息")
    print("=" * 20)
    
    print("🌐 服务地址:")
    print("  - API文档: http://localhost:8000/docs")
    print("  - OpenAPI规范: http://localhost:8000/api/v1/openapi.json")
    print("  - 健康检查: http://localhost:8000/api/v1/image/image-generation/health")
    print("  - 回调接口: http://localhost:8000/api/v1/image/callback")
    
    print("\n🔧 启动命令:")
    print("  cd backend")
    print("  source .venv/bin/activate")
    print("  python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    
    print("\n📝 注意事项:")
    print("  - 确保虚拟环境已激活")
    print("  - 确保所有依赖已安装")
    print("  - 确保数据库连接正常")
    print("  - 确保环境变量配置正确")

if __name__ == "__main__":
    try:
        print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 主要测试
        success = test_service_startup()
        
        # 端点验证
        test_service_endpoints()
        
        # 显示服务信息
        show_service_info()
        
        if success:
            print(f"\n🎉 服务启动验证完成 - 所有功能正常！")
        else:
            print(f"\n⚠️ 服务启动验证完成 - 发现问题，请检查日志")
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
