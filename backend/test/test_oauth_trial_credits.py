#!/usr/bin/env python3
"""
OAuth登录试用积分功能测试脚本

测试第三方登录时自动为新用户创建试用积分的功能
"""

import sys
import os
import uuid
from datetime import datetime, timezone

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

try:
    from sqlmodel import Session, create_engine, select
except ImportError:
    print("SQLModel not available in test environment")
    print("This test requires the full application environment to run")
    exit(1)
from app.core.config import settings
from app.models import (
    User, CreditPackage, AuthProviderEnum, PlatformEnum,
    OAuthLoginRequest, OAuthUserInfo
)
from app.services.oauth_service import OAuthService
from app.services.trial_service import TrialService
from app.services.billing import BillingService


def create_test_database_session():
    """创建测试数据库会话"""
    engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
    return Session(engine)


def cleanup_test_user(session: Session, email: str):
    """清理测试用户数据"""
    try:
        # 查找测试用户
        user = session.exec(select(User).where(User.email == email)).first()
        if user:
            # 删除相关的积分包
            credit_packages = session.exec(
                select(CreditPackage).where(CreditPackage.user_id == user.id)
            ).all()
            for package in credit_packages:
                session.delete(package)
            
            # 删除用户
            session.delete(user)
            session.commit()
            print(f"   清理测试用户: {email}")
    except Exception as e:
        print(f"   清理用户失败: {str(e)}")
        session.rollback()


async def test_new_user_oauth_login():
    """测试新用户OAuth登录自动获得试用积分"""
    print("=== 测试新用户OAuth登录试用积分 ===\n")
    
    session = create_test_database_session()
    
    # 生成唯一的测试邮箱
    test_email = f"test_oauth_{uuid.uuid4().hex[:8]}@example.com"
    
    try:
        # 清理可能存在的测试数据
        cleanup_test_user(session, test_email)
        
        # 模拟OAuth用户信息
        oauth_user_info = OAuthUserInfo(
            provider=AuthProviderEnum.google,
            provider_user_id=f"google_test_{uuid.uuid4().hex[:8]}",
            email=test_email,
            full_name="Test OAuth User",
            avatar_url="https://example.com/avatar.jpg"
        )
        
        # 创建OAuth服务
        oauth_service = OAuthService(session)
        
        # 模拟新用户创建过程
        print("1. 模拟新用户OAuth登录...")
        user = await oauth_service._create_user_from_oauth(
            oauth_user_info, 
            PlatformEnum.web
        )
        
        print(f"   ✓ 创建新用户: {user.email}")
        print(f"   用户ID: {user.id}")
        print(f"   认证提供商: {user.auth_provider}")
        
        # 检查试用积分是否自动创建
        print("\n2. 检查试用积分...")
        trial_service = TrialService(session)
        trial_status = trial_service.get_trial_status(user.id)
        
        print(f"   有试用积分: {'✓' if trial_status['has_trial'] else '✗'}")
        print(f"   试用积分总数: {trial_status['trial_credits']}")
        print(f"   剩余积分: {trial_status['remaining_credits']}")
        print(f"   已使用积分: {trial_status['used_credits']}")
        
        # 检查用户订阅状态
        print("\n3. 检查用户订阅状态...")
        billing_service = BillingService(session)
        subscription_status = billing_service.get_user_subscription_status(user.id)
        
        print(f"   有效订阅: {'✓' if subscription_status.has_active_subscription else '✗'}")
        print(f"   总积分: {subscription_status.total_credits}")
        print(f"   可使用服务: {'✓' if subscription_status.can_use_service else '✗'}")
        
        # 测试权限检查
        print("\n4. 测试权限检查...")
        from app.models import UsageTypeEnum
        access_check = billing_service.check_access(user.id, UsageTypeEnum.image_generation)
        
        print(f"   可访问: {'✓' if access_check.can_access else '✗'}")
        print(f"   剩余积分: {access_check.remaining_credits}")
        print(f"   订阅状态: {access_check.subscription_status}")
        
        # 测试消耗试用积分
        print("\n5. 测试消耗试用积分...")
        success, message, details = billing_service.consume_usage(
            user.id, UsageTypeEnum.image_generation, 1
        )
        
        print(f"   消耗成功: {'✓' if success else '✗'}")
        print(f"   消息: {message}")
        if details:
            print(f"   剩余积分: {details.get('remaining_credits', 0)}")
        
        # 再次检查试用状态
        print("\n6. 消耗后的试用状态...")
        trial_status_after = trial_service.get_trial_status(user.id)
        print(f"   剩余积分: {trial_status_after['remaining_credits']}")
        print(f"   已使用积分: {trial_status_after['used_credits']}")
        
        # 测试试用用户检查
        print("\n7. 测试试用用户检查...")
        is_trial_user = trial_service.is_trial_user(user.id)
        print(f"   是试用用户: {'✓' if is_trial_user else '✗'}")
        
        usage_message = trial_service.get_trial_usage_message(user.id)
        print(f"   使用提示: {usage_message}")
        
        return True
        
    except Exception as e:
        print(f"   ✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试数据
        print("\n8. 清理测试数据...")
        cleanup_test_user(session, test_email)
        session.close()


async def test_existing_user_oauth_login():
    """测试现有用户OAuth登录不会重复创建试用积分"""
    print("\n=== 测试现有用户OAuth登录 ===\n")
    
    session = create_test_database_session()
    
    # 生成唯一的测试邮箱
    test_email = f"test_existing_{uuid.uuid4().hex[:8]}@example.com"
    
    try:
        # 清理可能存在的测试数据
        cleanup_test_user(session, test_email)
        
        # 创建OAuth服务
        oauth_service = OAuthService(session)
        
        # 第一次登录 - 创建新用户
        print("1. 第一次OAuth登录（创建新用户）...")
        oauth_user_info = OAuthUserInfo(
            provider=AuthProviderEnum.google,
            provider_user_id=f"google_existing_{uuid.uuid4().hex[:8]}",
            email=test_email,
            full_name="Test Existing User",
            avatar_url="https://example.com/avatar.jpg"
        )
        
        user = await oauth_service._create_user_from_oauth(
            oauth_user_info, 
            PlatformEnum.web
        )
        
        print(f"   ✓ 创建用户: {user.email}")
        
        # 检查试用积分
        trial_service = TrialService(session)
        trial_status_first = trial_service.get_trial_status(user.id)
        print(f"   试用积分: {trial_status_first['trial_credits']}")
        
        # 第二次登录 - 更新现有用户
        print("\n2. 第二次OAuth登录（更新现有用户）...")
        updated_user = await oauth_service._update_user_from_oauth(
            user, oauth_user_info, PlatformEnum.ios
        )
        
        print(f"   ✓ 更新用户: {updated_user.email}")
        print(f"   平台更新: {updated_user.platform}")
        
        # 检查试用积分是否重复创建
        trial_status_second = trial_service.get_trial_status(user.id)
        print(f"   试用积分（应该相同）: {trial_status_second['trial_credits']}")
        
        # 验证积分包数量
        credit_packages = session.exec(
            select(CreditPackage).where(
                CreditPackage.user_id == user.id,
                CreditPackage.product_id == "trial_credits_new_user"
            )
        ).all()
        
        print(f"   试用积分包数量: {len(credit_packages)} (应该为1)")
        
        success = (
            trial_status_first['trial_credits'] == trial_status_second['trial_credits'] and
            len(credit_packages) == 1
        )
        
        print(f"   测试结果: {'✓ 通过' if success else '✗ 失败'}")
        
        return success
        
    except Exception as e:
        print(f"   ✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理测试数据
        print("\n3. 清理测试数据...")
        cleanup_test_user(session, test_email)
        session.close()


async def main():
    """主测试函数"""
    print("开始测试OAuth登录试用积分功能...\n")
    
    # 运行测试
    test1_result = await test_new_user_oauth_login()
    test2_result = await test_existing_user_oauth_login()
    
    # 总结
    print("\n=== 测试总结 ===")
    print(f"新用户试用积分: {'✓ 通过' if test1_result else '✗ 失败'}")
    print(f"现有用户不重复创建: {'✓ 通过' if test2_result else '✗ 失败'}")
    
    overall_success = test1_result and test2_result
    print(f"总体结果: {'✓ 所有测试通过' if overall_success else '✗ 部分测试失败'}")
    
    print("\n功能说明:")
    print("1. 新用户通过第三方登录时自动获得10积分（2次免费生成）")
    print("2. 现有用户登录不会重复创建试用积分")
    print("3. 试用积分优先于购买积分被消耗")
    print("4. 系统可以识别试用用户并提供相应提示")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
