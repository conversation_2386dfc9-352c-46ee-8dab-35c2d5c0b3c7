#!/usr/bin/env python3
"""
测试驼峰格式参数处理

这个脚本用于测试修复后的生图接口是否正确处理驼峰格式的参数。

使用方法:
python test_camel_case_params.py --test-standard-format
python test_camel_case_params.py --test-your-format
python test_camel_case_params.py --test-both
"""

import argparse
import json
import requests
import sys

def get_test_user_token() -> str:
    """获取测试用户的JWT令牌"""
    return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ2MTY4NDIsInN1YiI6ImU2ODIzYjc2LTBhNGEtNDY3Yy1hZWZiLTA5ZjNlNGU1Nzk2OSJ9.zATgYVH6GXSS8mBEOANXT1WkhI-Magts0pUnWGx6ZSs"

def test_standard_format(base_url: str, user_token: str):
    """测试标准API文档格式"""
    print("🧪 Testing Standard API Format")
    print("=" * 60)
    
    # 标准API文档格式
    payload = {
        "filesUrl": ["https://img-bridal.wenhaofree.com/uploads/image_1_5B7C678E-E8BE-49DB-B85F-98B1208E59BF.jpeg"],
        "prompt": "A beautiful sunset over the mountains",
        "size": "1:1",
        "callBackUrl": "http://c89de6f7.natappfree.cc/api/v1/image/callback",
        "isEnhance": False,
        "uploadCn": False,
        "nVariants": 1,
        "enableFallback": False,
        "fallbackModel": "FLUX_MAX"
    }
    
    return send_request(base_url, user_token, payload, "Standard Format")

def test_your_format(base_url: str, user_token: str):
    """测试你提供的实际请求格式"""
    print("\n🧪 Testing Your Actual Request Format")
    print("=" * 60)
    
    # 你的实际请求格式
    payload = {
        "prompt": "Elegant traditional wedding dress style, soft lighting, delicate lace details, romantic bouquet, beautiful background, warm atmosphere, premium texture, flowing white veil, classic posing, refined makeup, dreamy glow, natural posture, tranquil mood, high resolution, professional quality, detailed, beautiful",
        "size": "3:2",
        "isEnhance": False,
        "uploadCn": False,
        "nVariants": 1,
        "enableFallback": False,
        "fallbackModel": "FLUX_MAX",
        "filesUrl": [
            "https://img-bridal.wenhaofree.com/uploads/image_1_5B7C678E-E8BE-49DB-B85F-98B1208E59BF.jpeg"
        ],
        "callBackUrl": "http://c89de6f7.natappfree.cc/api/v1/image/callback"
    }
    
    return send_request(base_url, user_token, payload, "Your Format")

def test_mixed_format(base_url: str, user_token: str):
    """测试混合格式（同时包含驼峰和下划线）"""
    print("\n🧪 Testing Mixed Format (Priority Test)")
    print("=" * 60)
    
    # 混合格式，测试优先级
    payload = {
        "prompt": "Testing mixed parameter format priority",
        "size": "1:1",
        "files_url": ["https://example.com/old_format.jpg"],  # 下划线格式
        "filesUrl": ["https://img-bridal.wenhaofree.com/uploads/image_1_5B7C678E-E8BE-49DB-B85F-98B1208E59BF.jpeg"],  # 驼峰格式（应该优先）
        "callback_url": "http://old-callback.com",  # 下划线格式
        "callBackUrl": "http://c89de6f7.natappfree.cc/api/v1/image/callback",  # 驼峰格式（应该优先）
        "is_enhance": True,  # 下划线格式
        "isEnhance": False,  # 驼峰格式（应该优先）
        "n_variants": 2,  # 下划线格式
        "nVariants": 1,  # 驼峰格式（应该优先）
        "enable_fallback": True,  # 下划线格式
        "enableFallback": False,  # 驼峰格式（应该优先）
        "fallback_model": "OLD_MODEL",  # 下划线格式
        "fallbackModel": "FLUX_MAX"  # 驼峰格式（应该优先）
    }
    
    return send_request(base_url, user_token, payload, "Mixed Format")

def send_request(base_url: str, user_token: str, payload: dict, format_name: str):
    """发送请求并分析结果"""
    
    headers = {
        "Authorization": f"Bearer {user_token}",
        "Content-Type": "application/json"
    }
    
    print(f"📤 Request payload ({format_name}):")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    print()
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/image/generate-image",
            json=payload,
            headers=headers,
            timeout=30
        )
        
        print(f"📥 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"📊 Response Data:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            
            if response_data.get("code") == 0:
                task_id = response_data.get("data", {}).get("task_id")
                record_id = response_data.get("data", {}).get("generation_record_id")
                
                print(f"✅ Request successful!")
                print(f"   Task ID: {task_id}")
                print(f"   Record ID: {record_id}")
                
                return {
                    "success": True,
                    "task_id": task_id,
                    "record_id": record_id,
                    "format": format_name
                }
            else:
                print(f"❌ API returned error code: {response_data.get('code')}")
                return {"success": False, "error": response_data.get("message"), "format": format_name}
        else:
            print(f"❌ HTTP error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {error_data}")
            except:
                print(f"Raw response: {response.text}")
            return {"success": False, "error": f"HTTP {response.status_code}", "format": format_name}
            
    except Exception as e:
        print(f"💥 Request failed: {e}")
        return {"success": False, "error": str(e), "format": format_name}

def verify_database_record(record_id: str, expected_format: str):
    """验证数据库记录中的参数是否正确保存"""
    print(f"\n🔍 Verifying Database Record: {record_id}")
    print("=" * 60)
    
    try:
        from app.core.db import engine
        from sqlalchemy import text
        import json as json_lib
        
        with engine.connect() as conn:
            result = conn.execute(text('''
                SELECT 
                    id,
                    prompt,
                    size,
                    files_url,
                    callback_url,
                    is_enhance,
                    n_variants,
                    status,
                    created_at
                FROM imagegenerationrecord 
                WHERE id = :record_id
            '''), {'record_id': record_id})
            
            record = result.fetchone()
            
            if record:
                print(f"✅ Record found in database")
                print(f"📝 Prompt: {record[1][:50]}...")
                print(f"📏 Size: {record[2]}")
                print(f"🔗 Callback URL: {record[4]}")
                print(f"✨ Is Enhance: {record[5]}")
                print(f"🔢 N Variants: {record[6]}")
                print(f"📊 Status: {record[7]}")
                print(f"📅 Created: {record[8]}")
                
                # 检查 files_url 字段
                files_url_json = record[3]
                if files_url_json:
                    try:
                        saved_urls = json_lib.loads(files_url_json)
                        print(f"📷 Saved image URLs: {len(saved_urls)} URLs")
                        for i, url in enumerate(saved_urls, 1):
                            print(f"   {i}. {url}")
                    except json_lib.JSONDecodeError as e:
                        print(f"❌ Failed to parse files_url JSON: {e}")
                
                return True
            else:
                print(f"❌ Record not found in database")
                return False
                
    except Exception as e:
        print(f"💥 Database verification failed: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="测试驼峰格式参数处理")
    parser.add_argument("--base-url", default="http://127.0.0.1:8000", help="API基础URL")
    parser.add_argument("--test-standard-format", action="store_true", help="测试标准API格式")
    parser.add_argument("--test-your-format", action="store_true", help="测试你的实际请求格式")
    parser.add_argument("--test-mixed-format", action="store_true", help="测试混合格式（优先级测试）")
    parser.add_argument("--test-all", action="store_true", help="测试所有格式")
    
    args = parser.parse_args()
    
    if not any([args.test_standard_format, args.test_your_format, args.test_mixed_format, args.test_all]):
        parser.print_help()
        return
    
    print("🧪 CAMEL CASE PARAMETERS TEST")
    print("=" * 80)
    print("📋 Testing camelCase parameter handling in image generation API")
    print("📋 Comparing standard format vs your actual request format")
    print("=" * 80)
    
    user_token = get_test_user_token()
    test_results = []
    
    # 执行测试
    if args.test_standard_format or args.test_all:
        result = test_standard_format(args.base_url, user_token)
        test_results.append(result)
    
    if args.test_your_format or args.test_all:
        result = test_your_format(args.base_url, user_token)
        test_results.append(result)
    
    if args.test_mixed_format or args.test_all:
        result = test_mixed_format(args.base_url, user_token)
        test_results.append(result)
    
    # 验证数据库记录
    for result in test_results:
        if result.get("success") and result.get("record_id"):
            verify_database_record(result["record_id"], result["format"])
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 TEST SUMMARY")
    print("=" * 80)
    
    successful_tests = [r for r in test_results if r.get("success")]
    failed_tests = [r for r in test_results if not r.get("success")]
    
    if successful_tests:
        print(f"🎉 {len(successful_tests)} test(s) passed!")
        for test in successful_tests:
            print(f"   ✅ {test.get('format', 'unknown')}: {test.get('task_id', 'N/A')}")
    
    if failed_tests:
        print(f"💥 {len(failed_tests)} test(s) failed!")
        for test in failed_tests:
            print(f"   ❌ {test.get('format', 'unknown')}: {test.get('error', 'Unknown error')}")
    
    if successful_tests and not failed_tests:
        print("\n🎊 All tests passed! CamelCase parameter handling is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        sys.exit(1)

if __name__ == "__main__":
    main()
