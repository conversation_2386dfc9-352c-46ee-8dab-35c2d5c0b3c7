#!/usr/bin/env python3
"""
测试统一回调响应格式
验证回调接口使用统一的错误代码和响应格式
"""

import requests
import json
from datetime import datetime

def test_unified_callback_responses():
    """测试统一的回调响应格式"""
    
    print("🔧 测试统一回调响应格式")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000"
    callback_url = f"{base_url}/api/v1/image/callback"
    
    # 测试用例
    test_cases = [
        {
            "name": "有效回调数据 - 任务不存在",
            "data": {
                "code": 200,
                "msg": "success",
                "data": {
                    "taskId": "test-task-not-exist",
                    "info": {
                        "result_urls": ["https://example.com/result.png"]
                    }
                }
            },
            "expected_error_code": "CALLBACK_TASK_NOT_FOUND",
            "expected_status": 404
        },
        {
            "name": "无效回调数据 - 缺少data字段",
            "data": {
                "code": 200,
                "msg": "success"
            },
            "expected_error_code": "CALLBACK_INVALID_DATA",
            "expected_status": 400
        },
        {
            "name": "无效回调数据 - 缺少taskId",
            "data": {
                "code": 200,
                "msg": "success",
                "data": {
                    "info": {
                        "result_urls": ["https://example.com/result.png"]
                    }
                }
            },
            "expected_error_code": "CALLBACK_INVALID_DATA",
            "expected_status": 400
        },
        {
            "name": "无效回调数据 - 缺少code字段",
            "data": {
                "msg": "success",
                "data": {
                    "taskId": "test123",
                    "info": {
                        "result_urls": ["https://example.com/result.png"]
                    }
                }
            },
            "expected_error_code": "CALLBACK_INVALID_DATA",
            "expected_status": 400
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        print(f"请求数据: {json.dumps(test_case['data'], indent=2, ensure_ascii=False)}")
        
        try:
            response = requests.post(
                callback_url,
                headers={"Content-Type": "application/json"},
                json=test_case['data'],
                timeout=10
            )
            
            print(f"HTTP状态码: {response.status_code}")
            
            # 尝试解析响应
            try:
                response_data = response.json()
                print(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                # 验证响应格式
                if response.status_code == test_case.get('expected_status'):
                    print(f"✅ HTTP状态码正确: {response.status_code}")
                else:
                    print(f"⚠️ HTTP状态码不匹配: 期望 {test_case.get('expected_status')}, 实际 {response.status_code}")
                
                # 检查是否有统一的错误格式
                if 'detail' in response_data:
                    detail = response_data['detail']
                    if isinstance(detail, dict):
                        if 'error_code' in detail:
                            actual_error_code = detail['error_code']
                            expected_error_code = test_case.get('expected_error_code')
                            if actual_error_code == expected_error_code:
                                print(f"✅ 错误代码正确: {actual_error_code}")
                            else:
                                print(f"⚠️ 错误代码不匹配: 期望 {expected_error_code}, 实际 {actual_error_code}")
                        
                        if 'success' in detail:
                            print(f"✅ 包含success字段: {detail['success']}")
                        
                        if 'message' in detail:
                            print(f"✅ 包含message字段: {detail['message']}")
                        
                        if 'timestamp' in detail:
                            print(f"✅ 包含timestamp字段: {detail['timestamp']}")
                        
                        if 'request_id' in detail:
                            print(f"✅ 包含request_id字段: {detail['request_id']}")
                    else:
                        print(f"⚠️ detail不是字典格式: {detail}")
                else:
                    print("⚠️ 响应中没有detail字段")
                    
            except json.JSONDecodeError:
                print(f"❌ 响应不是有效的JSON: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
    
    # 测试预期的响应格式
    print(f"\n📋 预期的统一响应格式")
    
    expected_success_format = {
        "success": True,
        "message": "Callback processed successfully",
        "data": {
            "task_id": "task123",
            "record_id": "uuid-string",
            "status": "success",
            "result_urls": ["https://example.com/result.png"]
        },
        "timestamp": "2025-01-30T17:30:00",
        "request_id": "uuid-string"
    }
    
    expected_error_format = {
        "success": False,
        "error_code": "CALLBACK_TASK_NOT_FOUND",
        "message": "Generation record not found for task ID: task123",
        "timestamp": "2025-01-30T17:30:00",
        "request_id": "uuid-string"
    }
    
    print("✅ 预期成功响应格式:")
    print(json.dumps(expected_success_format, indent=2, ensure_ascii=False))
    
    print("\n❌ 预期错误响应格式:")
    print(json.dumps(expected_error_format, indent=2, ensure_ascii=False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 统一响应格式测试总结:")
    print("1. ✅ 测试了多种错误情况")
    print("2. ✅ 验证了HTTP状态码映射")
    print("3. ✅ 检查了统一错误代码")
    print("4. ✅ 验证了响应格式结构")
    
    print("\n🔧 统一响应格式的优势:")
    print("1. ✅ 标准化的错误代码便于客户端处理")
    print("2. ✅ 详细的错误信息便于调试")
    print("3. ✅ 统一的响应结构便于解析")
    print("4. ✅ 包含请求ID便于追踪")
    print("5. ✅ 时间戳便于日志分析")

def test_error_code_definitions():
    """测试错误代码定义"""
    
    print("\n🔍 错误代码定义测试")
    print("=" * 30)
    
    # 模拟导入错误代码（实际使用时会从模块导入）
    error_codes = {
        "CALLBACK_INVALID_DATA": {
            "http_status": 400,
            "message": "Invalid callback data format",
            "description": "回调数据格式不正确"
        },
        "CALLBACK_TASK_NOT_FOUND": {
            "http_status": 404,
            "message": "Generation record not found for task ID",
            "description": "根据任务ID找不到对应的生成记录"
        },
        "CALLBACK_PROCESSING_FAILED": {
            "http_status": 500,
            "message": "Failed to process callback",
            "description": "处理回调时发生内部错误"
        },
        "INTERNAL_ERROR": {
            "http_status": 500,
            "message": "An unexpected error occurred",
            "description": "发生意外的内部错误"
        }
    }
    
    print("📋 回调相关错误代码定义:")
    for code, info in error_codes.items():
        print(f"- {code}: HTTP {info['http_status']} - {info['description']}")
    
    print("\n✅ 错误代码设计原则:")
    print("1. 语义化命名，便于理解")
    print("2. HTTP状态码映射合理")
    print("3. 支持参数化错误消息")
    print("4. 提供用户友好的建议")

if __name__ == "__main__":
    try:
        test_unified_callback_responses()
        test_error_code_definitions()
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
