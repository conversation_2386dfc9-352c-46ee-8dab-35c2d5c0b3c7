#!/usr/bin/env python3
"""
为用户添加试用积分的脚本

使用方法:
python add_trial_credits_script.py --user-id e6823b76-0a4a-467c-aefb-09f3e4e57969
"""

import sys
import os
import argparse
import uuid
from datetime import datetime, timezone

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '.'))

from sqlmodel import Session
from app.core.db import engine
from app.models import User, CreditPackage, SubscriptionPlatformEnum
from app.services.trial_service import TrialService
from app.services.billing import BillingService


def add_trial_credits(user_id_str: str):
    """为指定用户添加试用积分"""
    try:
        # 转换用户ID
        user_id = uuid.UUID(user_id_str)
        print(f"为用户 {user_id} 添加试用积分...")
        
        # 获取数据库会话
        with Session(engine) as session:
            # 检查用户是否存在
            user = session.get(User, user_id)
            if not user:
                print(f"❌ 用户 {user_id} 不存在")
                return False
            
            print(f"✓ 找到用户: {user.email}")
            
            # 创建试用服务
            trial_service = TrialService(session)
            
            # 检查是否已有试用积分
            trial_status = trial_service.get_trial_status(user_id)
            if trial_status["has_trial"]:
                print(f"⚠️ 用户已有试用积分:")
                print(f"   总积分: {trial_status['trial_credits']}")
                print(f"   剩余积分: {trial_status['remaining_credits']}")
                print(f"   已使用: {trial_status['used_credits']}")
                return True
            
            # 添加试用积分
            trial_credits = trial_service.create_trial_credits(user)
            if trial_credits:
                print(f"✓ 成功添加试用积分:")
                print(f"   积分包ID: {trial_credits.id}")
                print(f"   积分数量: {trial_credits.credits}")
                print(f"   剩余积分: {trial_credits.remaining_credits}")
                
                # 验证权限
                billing_service = BillingService(session)
                access_check = billing_service.check_access(user_id, usage_type="image_generation")
                
                print(f"\n权限检查结果:")
                print(f"   可访问: {'✓' if access_check.can_access else '✗'}")
                print(f"   剩余积分: {access_check.remaining_credits}")
                print(f"   订阅状态: {access_check.subscription_status}")
                
                if access_check.can_access:
                    print(f"\n🎉 用户现在可以使用图片生成服务了！")
                    print(f"   预计可生成图片: {access_check.remaining_credits // 5} 张")
                else:
                    print(f"\n❌ 权限检查仍然失败: {access_check.reason}")
                
                return True
            else:
                print("❌ 添加试用积分失败")
                return False
                
    except ValueError:
        print(f"❌ 无效的用户ID格式: {user_id_str}")
        return False
    except Exception as e:
        print(f"❌ 添加试用积分时发生错误: {str(e)}")
        return False


def check_user_status(user_id_str: str):
    """检查用户当前状态"""
    try:
        user_id = uuid.UUID(user_id_str)
        print(f"检查用户 {user_id} 的状态...")
        
        with Session(engine) as session:
            # 检查用户
            user = session.get(User, user_id)
            if not user:
                print(f"❌ 用户 {user_id} 不存在")
                return
            
            print(f"✓ 用户: {user.email}")
            
            # 检查试用状态
            trial_service = TrialService(session)
            trial_status = trial_service.get_trial_status(user_id)
            
            print(f"\n试用状态:")
            print(f"   有试用积分: {'✓' if trial_status['has_trial'] else '✗'}")
            print(f"   试用积分总数: {trial_status['trial_credits']}")
            print(f"   剩余积分: {trial_status['remaining_credits']}")
            print(f"   已使用积分: {trial_status['used_credits']}")
            
            # 检查订阅状态
            billing_service = BillingService(session)
            subscription_status = billing_service.get_user_subscription_status(user_id)
            
            print(f"\n订阅状态:")
            print(f"   有效订阅: {'✓' if subscription_status.has_active_subscription else '✗'}")
            print(f"   总积分: {subscription_status.total_credits}")
            print(f"   月度使用: {subscription_status.monthly_usage_count}/{subscription_status.monthly_limit}")
            print(f"   可使用服务: {'✓' if subscription_status.can_use_service else '✗'}")
            
            # 检查访问权限
            from app.models import UsageTypeEnum
            access_check = billing_service.check_access(user_id, UsageTypeEnum.image_generation)
            
            print(f"\n访问权限:")
            print(f"   可访问: {'✓' if access_check.can_access else '✗'}")
            print(f"   剩余积分: {access_check.remaining_credits}")
            print(f"   剩余月度使用: {access_check.remaining_monthly_usage}")
            print(f"   订阅状态: {access_check.subscription_status}")
            
            if not access_check.can_access:
                print(f"   拒绝原因: {access_check.reason}")
            
    except Exception as e:
        print(f"❌ 检查用户状态时发生错误: {str(e)}")


def add_credits_manually(user_id_str: str, credits: int = 25):
    """手动为用户添加积分包"""
    try:
        user_id = uuid.UUID(user_id_str)
        print(f"为用户 {user_id} 手动添加 {credits} 积分...")

        with Session(engine) as session:
            # 检查用户是否存在
            user = session.get(User, user_id)
            if not user:
                print(f"❌ 用户 {user_id} 不存在")
                return False

            print(f"✓ 找到用户: {user.email}")

            # 创建新的积分包
            credit_package = CreditPackage(
                user_id=user_id,
                credits=credits,
                remaining_credits=credits,
                product_id="manual_credits",
                platform=SubscriptionPlatformEnum.stripe,
                purchased_at=datetime.now(timezone.utc)
            )

            session.add(credit_package)
            session.commit()
            session.refresh(credit_package)

            print(f"✓ 成功添加积分包:")
            print(f"   积分包ID: {credit_package.id}")
            print(f"   积分数量: {credit_package.credits}")
            print(f"   剩余积分: {credit_package.remaining_credits}")

            # 验证权限
            billing_service = BillingService(session)
            access_check = billing_service.check_access(user_id, usage_type="image_generation")

            print(f"\n权限检查结果:")
            print(f"   可访问: {'✓' if access_check.can_access else '✗'}")
            print(f"   剩余积分: {access_check.remaining_credits}")
            print(f"   订阅状态: {access_check.subscription_status}")

            if access_check.can_access:
                print(f"\n🎉 用户现在可以使用图片生成服务了！")
                print(f"   预计可生成图片: {access_check.remaining_credits // 5} 张")
            else:
                print(f"\n❌ 权限检查仍然失败: {access_check.reason}")

            return True

    except Exception as e:
        print(f"❌ 添加积分时发生错误: {str(e)}")
        return False


def main():
    parser = argparse.ArgumentParser(description="用户积分管理工具")
    parser.add_argument("--user-id", required=True, help="用户ID (UUID格式)")
    parser.add_argument("--action", choices=["add-trial", "add-credits", "check-status"], default="add-trial",
                       help="操作类型: add-trial(添加试用积分), add-credits(手动添加积分) 或 check-status(检查状态)")
    parser.add_argument("--credits", type=int, default=25, help="要添加的积分数量 (默认: 25)")

    args = parser.parse_args()

    if args.action == "add-trial":
        success = add_trial_credits(args.user_id)
        sys.exit(0 if success else 1)
    elif args.action == "add-credits":
        success = add_credits_manually(args.user_id, args.credits)
        sys.exit(0 if success else 1)
    elif args.action == "check-status":
        check_user_status(args.user_id)


if __name__ == "__main__":
    main()
