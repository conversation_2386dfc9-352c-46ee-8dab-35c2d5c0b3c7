#!/usr/bin/env python3
"""
图片生成服务使用示例

这个文件展示了如何在实际应用中使用图片生成服务
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.generation_image_service import (
    ImageGenerationRequest,
    ImageGenerationService,
    image_generation_service
)


def example_basic_generation():
    """基础图片生成示例"""
    print("=== 基础图片生成示例 ===")
    
    # 创建简单的图片生成请求
    request = ImageGenerationRequest(
        prompt="A cute cat sitting on a windowsill, looking outside at a garden",
        size="1:1",
        n_variants=1
    )
    
    # 调用服务
    result = image_generation_service.generate_image_sync(request)
    
    # 处理结果
    if result.success:
        print("✓ 图片生成请求成功提交")
        print(f"响应数据: {result.data}")
    else:
        print("✗ 图片生成失败")
        print(f"错误: {result.error}")
    
    print()


def example_enhanced_generation():
    """增强图片生成示例"""
    print("=== 增强图片生成示例 ===")
    
    # 创建增强的图片生成请求
    request = ImageGenerationRequest(
        prompt="A futuristic cityscape at night with neon lights and flying cars",
        size="16:9",
        is_enhance=True,
        n_variants=2,
        enable_fallback=True,
        fallback_model="FLUX_MAX"
    )
    
    # 调用服务
    result = image_generation_service.generate_image_sync(request)
    
    # 处理结果
    if result.success:
        print("✓ 增强图片生成请求成功提交")
        print(f"响应数据: {result.data}")
    else:
        print("✗ 增强图片生成失败")
        print(f"错误: {result.error}")
    
    print()


def example_with_reference_images():
    """带参考图片的生成示例"""
    print("=== 带参考图片的生成示例 ===")
    
    # 创建带参考图片的请求
    request = ImageGenerationRequest(
        files_url=[
            "https://example.com/reference1.jpg",
            "https://example.com/reference2.jpg"
        ],
        prompt="Create a artistic composition inspired by these reference images",
        size="4:3",
        is_enhance=True,
        n_variants=1
    )
    
    # 调用服务
    result = image_generation_service.generate_image_sync(request)
    
    # 处理结果
    if result.success:
        print("✓ 参考图片生成请求成功提交")
        print(f"响应数据: {result.data}")
    else:
        print("✗ 参考图片生成失败")
        print(f"错误: {result.error}")
    
    print()


async def example_async_generation():
    """异步图片生成示例"""
    print("=== 异步图片生成示例 ===")
    
    # 创建异步请求
    request = ImageGenerationRequest(
        prompt="A serene mountain landscape with a crystal clear lake",
        size="16:9",
        callback_url="https://your-app.com/api/image-callback",
        n_variants=1
    )
    
    # 异步调用服务
    result = await image_generation_service.generate_image(request)
    
    # 处理结果
    if result.success:
        print("✓ 异步图片生成请求成功提交")
        print(f"响应数据: {result.data}")
        print("注意: 这是异步请求，实际图片将通过回调URL返回")
    else:
        print("✗ 异步图片生成失败")
        print(f"错误: {result.error}")
    
    print()


def example_batch_generation():
    """批量图片生成示例"""
    print("=== 批量图片生成示例 ===")
    
    prompts = [
        "A red rose in full bloom",
        "A blue ocean wave crashing on the shore",
        "A green forest with sunlight filtering through trees",
        "A purple sunset over mountains"
    ]
    
    results = []
    
    for i, prompt in enumerate(prompts, 1):
        print(f"生成图片 {i}/{len(prompts)}: {prompt[:30]}...")
        
        request = ImageGenerationRequest(
            prompt=prompt,
            size="1:1",
            n_variants=1
        )
        
        result = image_generation_service.generate_image_sync(request)
        results.append(result)
        
        if result.success:
            print(f"  ✓ 成功")
        else:
            print(f"  ✗ 失败: {result.error}")
    
    # 统计结果
    successful = sum(1 for r in results if r.success)
    print(f"\n批量生成完成: {successful}/{len(results)} 成功")
    print()


def example_error_handling():
    """错误处理示例"""
    print("=== 错误处理示例 ===")
    
    # 测试空提示词（应该会失败）
    try:
        request = ImageGenerationRequest(
            prompt="",  # 空提示词
            size="1:1"
        )
        print("创建了空提示词请求（这可能会失败）")
    except Exception as e:
        print(f"创建请求时出错: {e}")
    
    # 测试无效尺寸
    try:
        request = ImageGenerationRequest(
            prompt="A beautiful landscape",
            size="invalid_size"  # 无效尺寸
        )
        result = image_generation_service.generate_image_sync(request)
        if not result.success:
            print(f"无效尺寸请求失败: {result.error}")
    except Exception as e:
        print(f"无效尺寸请求出错: {e}")
    
    print()


def example_get_record_info():
    """获取生成记录详情示例"""
    print("=== 获取生成记录详情示例 ===")

    # 创建服务实例
    service = ImageGenerationService()

    # 示例task_id（来自API响应示例）
    task_id = "2857db2e20044233c72c35cbbb168008"

    print(f"获取任务详情: {task_id}")

    # 调用服务获取记录详情
    result = service.get_generation_record_info(task_id)

    # 处理结果
    if result.success:
        print("✓ 成功获取记录详情")

        # 解析响应数据
        data = result.data.get('data', {})
        if data:
            print(f"  任务ID: {data.get('taskId')}")
            print(f"  状态: {data.get('status')}")
            print(f"  进度: {data.get('progress')}")
            print(f"  成功标志: {data.get('successFlag')}")
            print(f"  创建时间: {data.get('createTime')}")
            print(f"  完成时间: {data.get('completeTime')}")

            # 显示生成结果
            response = data.get('response', {})
            if response and response.get('resultUrls'):
                print(f"  结果图片URL:")
                for i, url in enumerate(response['resultUrls'], 1):
                    print(f"    {i}. {url}")

            # 显示原始参数
            param_json = data.get('paramJson')
            if param_json:
                import json
                try:
                    params = json.loads(param_json)
                    print(f"  原始提示词: {params.get('prompt', 'N/A')}")
                    print(f"  图片尺寸: {params.get('size', 'N/A')}")
                    print(f"  变体数量: {params.get('nVariants', 'N/A')}")
                    print(f"  是否增强: {params.get('isEnhance', 'N/A')}")
                except json.JSONDecodeError:
                    print(f"  原始参数: {param_json}")
        else:
            print("  响应数据为空")
    else:
        print("✗ 获取记录详情失败")
        print(f"  错误代码: {result.error_code}")
        print(f"  错误信息: {result.message}")
        print(f"  HTTP状态码: {result.http_status_code}")

    print()


def example_record_info_error_cases():
    """记录详情错误情况示例"""
    print("=== 记录详情错误情况示例 ===")

    service = ImageGenerationService()

    # 测试1: 空task_id
    print("1. 测试空task_id:")
    result = service.get_generation_record_info("")
    print(f"   结果: success={result.success}, error_code={result.error_code}")
    print(f"   错误信息: {result.message}")
    print()

    # 测试2: 不存在的task_id
    print("2. 测试不存在的task_id:")
    fake_task_id = "nonexistent_task_id_12345"
    result = service.get_generation_record_info(fake_task_id)
    print(f"   结果: success={result.success}, error_code={result.error_code}")
    print(f"   错误信息: {result.message}")
    print(f"   HTTP状态码: {result.http_status_code}")
    print()


if __name__ == "__main__":
    print("图片生成服务使用示例\n")
    
    # 运行各种示例
    example_basic_generation()
    example_enhanced_generation()
    example_with_reference_images()
    
    # 运行异步示例
    asyncio.run(example_async_generation())
    
    # 运行批量生成示例
    example_batch_generation()
    
    # 运行错误处理示例
    example_error_handling()

    # 运行获取记录详情示例
    example_get_record_info()

    # 运行记录详情错误情况示例
    example_record_info_error_cases()

    print("所有示例运行完成!")
    print("\n注意事项:")
    print("1. 确保在.env文件中配置了有效的API token")
    print("2. 401错误表示API token无效或权限不足")
    print("3. 在生产环境中，建议添加请求频率限制")
    print("4. 异步请求需要配置有效的回调URL")
    print("5. 获取记录详情需要有效的task_id，通常从生成API响应中获取")
