#!/usr/bin/env python3
"""
测试 /record-info/ 接口的Mock功能

验证当 ENABLE_IMAGE_GENERATION_MOCK=True 时，接口返回mock数据
"""

import sys
import os
import json
import requests
import uuid

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def get_auth_token():
    """获取认证token"""
    login_data = {
        "username": "<EMAIL>",
        "password": "changethis"
    }

    try:
        response = requests.post(
            "http://localhost:8000/api/v1/login/access-token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            return result.get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {response.text}")
            return None

    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return None

def test_record_info_mock():
    """测试record-info接口的mock功能"""
    print("🧪 测试 /record-info/ 接口Mock功能...")
    
    # 获取认证token
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token")
        return False
    
    print("✅ 成功获取认证token")
    
    # 生成测试任务ID
    test_task_id = f"test_task_{uuid.uuid4().hex[:16]}"
    print(f"📝 测试任务ID: {test_task_id}")
    
    # 准备请求头
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        # 发送请求
        response = requests.get(
            f"http://localhost:8000/api/v1/image/record-info/{test_task_id}",
            headers=headers,
            timeout=30
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ 请求成功!")
            print(f"📊 响应数据结构验证:")
            
            # 验证响应结构
            required_fields = ["code", "msg", "data"]
            data_fields = ["taskId", "paramJson", "completeTime", "response", "successFlag", "status", "createTime", "progress"]
            
            # 检查顶级字段
            for field in required_fields:
                if field in result:
                    print(f"  ✅ {field}: {result[field]}")
                else:
                    print(f"  ❌ 缺少字段: {field}")
                    return False
            
            # 检查data字段
            data = result.get("data", {})
            for field in data_fields:
                if field in data:
                    if field == "taskId":
                        if data[field] == test_task_id:
                            print(f"  ✅ {field}: {data[field]} (正确匹配)")
                        else:
                            print(f"  ❌ {field}: {data[field]} (不匹配，期望: {test_task_id})")
                            return False
                    elif field == "paramJson":
                        try:
                            param_data = json.loads(data[field])
                            print(f"  ✅ {field}: 有效的JSON字符串")
                        except:
                            print(f"  ❌ {field}: 无效的JSON字符串")
                            return False
                    elif field == "response":
                        response_data = data[field]
                        if "resultUrls" in response_data and isinstance(response_data["resultUrls"], list):
                            print(f"  ✅ {field}.resultUrls: {len(response_data['resultUrls'])} 个URL")
                        else:
                            print(f"  ❌ {field}: 缺少resultUrls或格式错误")
                            return False
                    else:
                        print(f"  ✅ {field}: {data[field]}")
                else:
                    print(f"  ❌ 缺少data字段: {field}")
                    return False
            
            # 验证mock数据的特征
            if (result.get("code") == 200 and 
                result.get("msg") == "success" and
                data.get("status") == "SUCCESS" and
                data.get("successFlag") == 1):
                print(f"✅ Mock数据验证通过!")
                return True
            else:
                print(f"❌ Mock数据验证失败")
                return False
                
        else:
            print(f"❌ 请求失败:")
            try:
                error_detail = response.json()
                print(f"   错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ 连接失败: 请确保 FastAPI 服务器正在运行")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

def test_different_task_ids():
    """测试不同任务ID的mock响应"""
    print("\n🧪 测试不同任务ID的Mock响应...")
    
    # 获取认证token
    token = get_auth_token()
    if not token:
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试多个不同的任务ID
    test_task_ids = [
        "92e524b7d976604631efffd0f9ba1fc7",  # 原始示例ID
        f"custom_task_{uuid.uuid4().hex[:12]}",
        f"test_{int(time.time())}"
    ]
    
    for task_id in test_task_ids:
        print(f"📝 测试任务ID: {task_id}")
        
        try:
            response = requests.get(
                f"http://localhost:8000/api/v1/image/record-info/{task_id}",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                returned_task_id = result.get("data", {}).get("taskId")
                
                if returned_task_id == task_id:
                    print(f"  ✅ 任务ID正确匹配: {returned_task_id}")
                else:
                    print(f"  ❌ 任务ID不匹配: 期望 {task_id}, 得到 {returned_task_id}")
                    return False
            else:
                print(f"  ❌ 请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ 请求异常: {str(e)}")
            return False
    
    print(f"✅ 所有任务ID测试通过!")
    return True

def main():
    """主函数"""
    print("🔧 /record-info/ 接口Mock功能测试")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code != 200:
            print("❌ FastAPI服务器未正常运行")
            return False
    except:
        print("❌ 无法连接到FastAPI服务器")
        print("请确保:")
        print("1. FastAPI服务器正在运行: uv run uvicorn app.main:app --reload")
        print("2. 环境变量 ENABLE_IMAGE_GENERATION_MOCK=True")
        return False
    
    print("✅ FastAPI服务器运行正常")
    
    # 执行测试
    test_results = []
    
    # 测试1: 基本Mock功能
    test_results.append(test_record_info_mock())
    
    # 测试2: 不同任务ID
    test_results.append(test_different_task_ids())
    
    print("\n" + "=" * 50)
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
        print("✅ /record-info/ 接口Mock功能正常工作")
        print("\n💡 使用说明:")
        print("  - 设置环境变量 ENABLE_IMAGE_GENERATION_MOCK=True 启用Mock模式")
        print("  - Mock模式下，接口会返回模拟的成功数据")
        print("  - 任务ID会正确替换为请求中的ID")
    else:
        print(f"💥 部分测试失败! ({passed}/{total})")
        print("❌ 需要检查Mock功能实现")
    
    return passed == total

if __name__ == "__main__":
    import time
    main()
