#!/usr/bin/env python3
"""
测试图片URL列表保存功能

这个脚本用于测试生图接口是否正确保存参数中的图片列表到数据库。

使用方法:
python test_files_url_saving.py --test-snake-case    # 测试下划线格式 files_url
python test_files_url_saving.py --test-camel-case    # 测试驼峰格式 filesUrl
python test_files_url_saving.py --test-both          # 测试两种格式
python test_files_url_saving.py --verify-database    # 验证数据库中的数据
"""

import argparse
import json
import requests
import sys
import uuid
from typing import List, Dict, Any

def get_test_user_token() -> str:
    """获取测试用户的JWT令牌"""
    # 这里使用之前测试中使用的用户令牌
    return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE3NTQ2MTY4NDIsInN1YiI6ImU2ODIzYjc2LTBhNGEtNDY3Yy1hZWZiLTA5ZjNlNGU1Nzk2OSJ9.zATgYVH6GXSS8mBEOANXT1WkhI-Magts0pUnWGx6ZSs"

def get_test_image_urls() -> List[str]:
    """获取测试用的图片URL列表"""
    return [
        "https://img-bridal.wenhaofree.com/uploads/image_1_5B7C678E-E8BE-49DB-B85F-98B1208E59BF.jpeg",
        "https://img-bridal.wenhaofree.com/uploads/image_2_A1B2C3D4-E5F6-7890-ABCD-EF1234567890.jpeg",
        "https://img-bridal.wenhaofree.com/uploads/image_3_12345678-90AB-CDEF-1234-567890ABCDEF.jpeg"
    ]

def test_snake_case_format(base_url: str, user_token: str) -> Dict[str, Any]:
    """测试下划线格式 files_url"""
    print("🧪 Testing Snake Case Format (files_url)")
    print("=" * 60)
    
    test_images = get_test_image_urls()
    
    payload = {
        "files_url": test_images,  # 下划线格式
        "prompt": "Test image generation with snake_case files_url format",
        "size": "1:1",
        "n_variants": 1,
        "is_enhance": False
    }
    
    headers = {
        "Authorization": f"Bearer {user_token}",
        "Content-Type": "application/json"
    }
    
    print(f"📤 Request payload:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    print()
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/image/generate-image",
            json=payload,
            headers=headers,
            timeout=30
        )
        
        print(f"📥 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"📊 Response Data:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            
            if response_data.get("code") == 0:
                task_id = response_data.get("data", {}).get("task_id")
                record_id = response_data.get("data", {}).get("generation_record_id")
                
                print(f"✅ Request successful!")
                print(f"   Task ID: {task_id}")
                print(f"   Record ID: {record_id}")
                
                return {
                    "success": True,
                    "task_id": task_id,
                    "record_id": record_id,
                    "format": "snake_case",
                    "input_images": test_images
                }
            else:
                print(f"❌ API returned error code: {response_data.get('code')}")
                return {"success": False, "error": response_data.get("message")}
        else:
            print(f"❌ HTTP error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {error_data}")
            except:
                print(f"Raw response: {response.text}")
            return {"success": False, "error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        print(f"💥 Request failed: {e}")
        return {"success": False, "error": str(e)}

def test_camel_case_format(base_url: str, user_token: str) -> Dict[str, Any]:
    """测试驼峰格式 filesUrl"""
    print("\n🧪 Testing Camel Case Format (filesUrl)")
    print("=" * 60)
    
    test_images = get_test_image_urls()
    
    payload = {
        "filesUrl": test_images,  # 驼峰格式
        "prompt": "Test image generation with camelCase filesUrl format",
        "size": "1:1",
        "n_variants": 1,
        "is_enhance": False
    }
    
    headers = {
        "Authorization": f"Bearer {user_token}",
        "Content-Type": "application/json"
    }
    
    print(f"📤 Request payload:")
    print(json.dumps(payload, indent=2, ensure_ascii=False))
    print()
    
    try:
        response = requests.post(
            f"{base_url}/api/v1/image/generate-image",
            json=payload,
            headers=headers,
            timeout=30
        )
        
        print(f"📥 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"📊 Response Data:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            
            if response_data.get("code") == 0:
                task_id = response_data.get("data", {}).get("task_id")
                record_id = response_data.get("data", {}).get("generation_record_id")
                
                print(f"✅ Request successful!")
                print(f"   Task ID: {task_id}")
                print(f"   Record ID: {record_id}")
                
                return {
                    "success": True,
                    "task_id": task_id,
                    "record_id": record_id,
                    "format": "camel_case",
                    "input_images": test_images
                }
            else:
                print(f"❌ API returned error code: {response_data.get('code')}")
                return {"success": False, "error": response_data.get("message")}
        else:
            print(f"❌ HTTP error: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {error_data}")
            except:
                print(f"Raw response: {response.text}")
            return {"success": False, "error": f"HTTP {response.status_code}"}
            
    except Exception as e:
        print(f"💥 Request failed: {e}")
        return {"success": False, "error": str(e)}

def verify_database_records(test_results: List[Dict[str, Any]]):
    """验证数据库中的记录"""
    print("\n🔍 Verifying Database Records")
    print("=" * 60)
    
    try:
        from app.core.db import engine
        from sqlalchemy import text
        
        successful_tests = [r for r in test_results if r.get("success")]
        
        if not successful_tests:
            print("❌ No successful tests to verify")
            return
        
        with engine.connect() as conn:
            for test_result in successful_tests:
                record_id = test_result.get("record_id")
                if not record_id:
                    continue
                
                print(f"\n📋 Checking record: {record_id}")
                print(f"   Format tested: {test_result.get('format')}")
                print(f"   Expected images: {len(test_result.get('input_images', []))}")
                
                # 查询数据库记录
                result = conn.execute(text('''
                    SELECT 
                        id,
                        prompt,
                        files_url,
                        status,
                        created_at
                    FROM imagegenerationrecord 
                    WHERE id = :record_id
                '''), {'record_id': record_id})
                
                record = result.fetchone()
                
                if record:
                    print(f"   ✅ Record found in database")
                    print(f"   📝 Prompt: {record[1][:50]}...")
                    print(f"   📊 Status: {record[3]}")
                    print(f"   📅 Created: {record[4]}")
                    
                    # 检查 files_url 字段
                    files_url_json = record[2]
                    if files_url_json:
                        try:
                            saved_urls = json.loads(files_url_json)
                            print(f"   📷 Saved image URLs: {len(saved_urls)} URLs")
                            
                            expected_urls = test_result.get('input_images', [])
                            if saved_urls == expected_urls:
                                print(f"   ✅ Image URLs match expected values")
                                for i, url in enumerate(saved_urls, 1):
                                    print(f"      {i}. {url}")
                            else:
                                print(f"   ❌ Image URLs don't match expected values")
                                print(f"      Expected: {expected_urls}")
                                print(f"      Saved: {saved_urls}")
                        except json.JSONDecodeError as e:
                            print(f"   ❌ Failed to parse files_url JSON: {e}")
                            print(f"      Raw value: {files_url_json}")
                    else:
                        print(f"   ❌ No files_url data saved")
                else:
                    print(f"   ❌ Record not found in database")
                    
    except Exception as e:
        print(f"💥 Database verification failed: {e}")

def main():
    parser = argparse.ArgumentParser(description="测试图片URL列表保存功能")
    parser.add_argument("--base-url", default="http://127.0.0.1:8000", help="API基础URL")
    parser.add_argument("--test-snake-case", action="store_true", help="测试下划线格式 files_url")
    parser.add_argument("--test-camel-case", action="store_true", help="测试驼峰格式 filesUrl")
    parser.add_argument("--test-both", action="store_true", help="测试两种格式")
    parser.add_argument("--verify-database", action="store_true", help="验证数据库中的数据")
    
    args = parser.parse_args()
    
    if not any([args.test_snake_case, args.test_camel_case, args.test_both, args.verify_database]):
        parser.print_help()
        return
    
    print("🧪 FILES URL SAVING TEST")
    print("=" * 80)
    print("📋 Testing image URL list saving functionality")
    print("📋 Verifying both snake_case and camelCase formats")
    print("=" * 80)
    
    user_token = get_test_user_token()
    test_results = []
    
    # 执行测试
    if args.test_snake_case or args.test_both:
        result = test_snake_case_format(args.base_url, user_token)
        test_results.append(result)
    
    if args.test_camel_case or args.test_both:
        result = test_camel_case_format(args.base_url, user_token)
        test_results.append(result)
    
    # 验证数据库（如果有成功的测试或者明确要求验证）
    if args.verify_database or any(r.get("success") for r in test_results):
        verify_database_records(test_results)
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 TEST SUMMARY")
    print("=" * 80)
    
    successful_tests = [r for r in test_results if r.get("success")]
    failed_tests = [r for r in test_results if not r.get("success")]
    
    if successful_tests:
        print(f"🎉 {len(successful_tests)} test(s) passed!")
        for test in successful_tests:
            print(f"   ✅ {test.get('format', 'unknown')} format: {test.get('task_id', 'N/A')}")
    
    if failed_tests:
        print(f"💥 {len(failed_tests)} test(s) failed!")
        for test in failed_tests:
            print(f"   ❌ Error: {test.get('error', 'Unknown error')}")
    
    if successful_tests and not failed_tests:
        print("\n🎊 All tests passed! Image URL saving is working correctly.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        sys.exit(1)

if __name__ == "__main__":
    main()
