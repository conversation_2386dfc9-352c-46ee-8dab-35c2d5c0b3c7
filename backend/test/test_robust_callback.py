#!/usr/bin/env python3
"""
测试强壮的回调接口
验证修复后的回调接口的强壮性和错误处理
"""

import requests
import json
from datetime import datetime

def test_robust_callback():
    """测试强壮的回调接口"""
    
    print("🔧 测试强壮的回调接口")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:8000"
    callback_url = f"{base_url}/api/v1/image/callback"
    
    # 测试用例
    test_cases = [
        {
            "name": "有效回调数据 - 任务不存在",
            "data": {
                "code": 200,
                "msg": "success",
                "data": {
                    "taskId": "test-task-not-exist",
                    "info": {
                        "result_urls": ["https://example.com/result.png"]
                    }
                }
            },
            "expected_error_code": "CALLBACK_TASK_NOT_FOUND",
            "expected_status": 404
        },
        {
            "name": "无效JSON格式",
            "data": "invalid json",
            "expected_error_code": "CALLBACK_INVALID_JSON",
            "expected_status": 400,
            "raw_data": True
        },
        {
            "name": "缺少code字段",
            "data": {
                "msg": "success",
                "data": {
                    "taskId": "test123"
                }
            },
            "expected_error_code": "CALLBACK_INVALID_DATA",
            "expected_status": 400
        },
        {
            "name": "缺少data字段",
            "data": {
                "code": 200,
                "msg": "success"
            },
            "expected_error_code": "CALLBACK_INVALID_DATA",
            "expected_status": 400
        },
        {
            "name": "缺少taskId字段",
            "data": {
                "code": 200,
                "msg": "success",
                "data": {
                    "info": {
                        "result_urls": ["https://example.com/result.png"]
                    }
                }
            },
            "expected_error_code": "CALLBACK_INVALID_DATA",
            "expected_status": 400
        },
        {
            "name": "data不是对象",
            "data": {
                "code": 200,
                "msg": "success",
                "data": "not an object"
            },
            "expected_error_code": "CALLBACK_INVALID_DATA",
            "expected_status": 400
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {test_case['name']}")
        
        try:
            if test_case.get("raw_data"):
                # 发送原始数据（非JSON）
                response = requests.post(
                    callback_url,
                    headers={"Content-Type": "application/json"},
                    data=test_case['data'],
                    timeout=5
                )
            else:
                # 发送JSON数据
                print(f"请求数据: {json.dumps(test_case['data'], indent=2, ensure_ascii=False)}")
                response = requests.post(
                    callback_url,
                    headers={"Content-Type": "application/json"},
                    json=test_case['data'],
                    timeout=5
                )
            
            print(f"HTTP状态码: {response.status_code}")
            
            # 解析响应
            try:
                response_data = response.json()
                print(f"响应数据: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                
                # 验证响应格式
                expected_status = test_case.get('expected_status')
                if response.status_code == expected_status:
                    print(f"✅ HTTP状态码正确: {response.status_code}")
                else:
                    print(f"⚠️ HTTP状态码不匹配: 期望 {expected_status}, 实际 {response.status_code}")
                
                # 检查统一响应格式
                if 'detail' in response_data:
                    detail = response_data['detail']
                    if isinstance(detail, dict):
                        # 验证必需字段
                        required_fields = ['success', 'error_code', 'message', 'timestamp', 'request_id']
                        for field in required_fields:
                            if field in detail:
                                print(f"✅ 包含{field}字段: {detail[field]}")
                            else:
                                print(f"❌ 缺少{field}字段")
                        
                        # 验证错误代码
                        actual_error_code = detail.get('error_code')
                        expected_error_code = test_case.get('expected_error_code')
                        if actual_error_code == expected_error_code:
                            print(f"✅ 错误代码正确: {actual_error_code}")
                        else:
                            print(f"⚠️ 错误代码不匹配: 期望 {expected_error_code}, 实际 {actual_error_code}")
                    else:
                        print(f"⚠️ detail不是字典格式: {detail}")
                elif response.status_code == 200:
                    # 成功响应
                    required_fields = ['success', 'message', 'data', 'timestamp', 'request_id']
                    for field in required_fields:
                        if field in response_data:
                            print(f"✅ 包含{field}字段: {response_data[field]}")
                        else:
                            print(f"❌ 缺少{field}字段")
                else:
                    print("⚠️ 响应格式不符合预期")
                    
            except json.JSONDecodeError:
                print(f"❌ 响应不是有效的JSON: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ 请求超时 - 可能服务器未运行")
        except requests.exceptions.ConnectionError:
            print("❌ 连接错误 - 请确保服务器正在运行")
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
    
    # 显示预期的响应格式
    print(f"\n📋 统一响应格式规范")
    
    success_format = {
        "success": True,
        "message": "Callback processed successfully",
        "data": {
            "task_id": "task123",
            "record_id": "uuid-string",
            "status": "success",
            "result_urls": ["https://example.com/result.png"]
        },
        "timestamp": "2025-01-30T17:30:00.000Z",
        "request_id": "uuid-string"
    }
    
    error_format = {
        "success": False,
        "error_code": "CALLBACK_TASK_NOT_FOUND",
        "message": "Generation record not found for task ID: task123",
        "timestamp": "2025-01-30T17:30:00.000Z",
        "request_id": "uuid-string"
    }
    
    print("✅ 成功响应格式:")
    print(json.dumps(success_format, indent=2, ensure_ascii=False))
    
    print("\n❌ 错误响应格式:")
    print(json.dumps(error_format, indent=2, ensure_ascii=False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 强壮性测试总结:")
    print("1. ✅ JSON解析错误处理")
    print("2. ✅ 必需字段验证")
    print("3. ✅ 数据类型验证")
    print("4. ✅ 统一错误代码")
    print("5. ✅ 标准响应格式")
    print("6. ✅ 请求追踪支持")
    
    print("\n🔧 强壮性改进:")
    print("1. ✅ 移除了复杂的导入依赖")
    print("2. ✅ 简化了错误处理逻辑")
    print("3. ✅ 添加了详细的数据验证")
    print("4. ✅ 统一了响应格式创建")
    print("5. ✅ 增强了异常处理")
    print("6. ✅ 添加了请求追踪")

def test_error_codes():
    """测试错误代码定义"""
    
    print("\n🔍 错误代码定义")
    print("=" * 30)
    
    error_codes = {
        "CALLBACK_INVALID_JSON": {
            "status": 400,
            "description": "JSON格式错误"
        },
        "CALLBACK_INVALID_DATA": {
            "status": 400,
            "description": "回调数据格式错误或缺少必需字段"
        },
        "CALLBACK_TASK_NOT_FOUND": {
            "status": 404,
            "description": "根据任务ID找不到对应的生成记录"
        },
        "CALLBACK_PROCESSING_FAILED": {
            "status": 500,
            "description": "处理回调时发生错误"
        },
        "INTERNAL_ERROR": {
            "status": 500,
            "description": "发生意外的内部错误"
        }
    }
    
    print("📋 回调接口错误代码:")
    for code, info in error_codes.items():
        print(f"- {code} (HTTP {info['status']}): {info['description']}")
    
    print("\n✅ 错误处理特点:")
    print("1. 明确的错误代码便于客户端处理")
    print("2. 合理的HTTP状态码映射")
    print("3. 详细的错误描述信息")
    print("4. 统一的响应格式")

if __name__ == "__main__":
    try:
        test_robust_callback()
        test_error_codes()
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
