#!/usr/bin/env python3
"""
回调URL路径验证测试脚本
验证图片生成回调接口的URL路径是否正确配置
"""

import requests
import json
import sys
from datetime import datetime

def test_callback_url():
    """测试回调URL的正确性"""
    
    base_url = "http://localhost:8000"
    
    print("🔍 回调URL路径验证测试")
    print("=" * 50)
    
    # 测试1: 健康检查端点
    print("\n📋 测试1: 健康检查端点")
    health_url = f"{base_url}/api/v1/image/image-generation/health"
    print(f"URL: {health_url}")
    
    try:
        response = requests.get(health_url, timeout=10)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✅ 健康检查端点正常")
            print(f"响应: {response.json()}")
        else:
            print(f"❌ 健康检查失败: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    # 测试2: 回调接口路径验证
    print("\n📋 测试2: 回调接口路径验证")
    callback_url = f"{base_url}/api/v1/image/callback"
    print(f"URL: {callback_url}")
    
    # 测试数据 - 正确格式
    test_data = {
        "code": 200,
        "msg": "success",
        "data": {
            "taskId": "test-task-" + datetime.now().strftime("%Y%m%d-%H%M%S"),
            "info": {
                "result_urls": [
                    "https://example.com/test-image-1.png",
                    "https://example.com/test-image-2.png"
                ]
            }
        }
    }
    
    try:
        response = requests.post(
            callback_url,
            headers={"Content-Type": "application/json"},
            json=test_data,
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 回调接口路径正确")
            print(f"响应: {response.json()}")
        elif response.status_code == 404:
            print("❌ 回调接口路径错误 - 404 Not Found")
            print("请检查URL路径配置")
        elif response.status_code == 422:
            print("⚠️ 回调接口路径正确，但数据格式有问题")
            print(f"响应: {response.json()}")
        else:
            print(f"⚠️ 回调接口响应异常: {response.status_code}")
            print(f"响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    # 测试3: 错误格式数据测试
    print("\n📋 测试3: 错误格式数据测试")
    wrong_data = {
        "code": 200,
        "message": "success",  # 错误字段名，应该是 "msg"
        "data": {
            "task_id": "test-task-wrong",  # 错误字段名，应该是 "taskId"
            "images": ["https://example.com/test.png"]  # 错误结构
        }
    }
    
    try:
        response = requests.post(
            callback_url,
            headers={"Content-Type": "application/json"},
            json=wrong_data,
            timeout=10
        )
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 422:
            print("✅ 数据验证正常 - 正确拒绝了错误格式")
            print(f"验证错误: {response.json()}")
        else:
            print(f"⚠️ 数据验证异常: {response.status_code}")
            print(f"响应: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求失败: {e}")
    
    # 测试4: URL路径组件验证
    print("\n📋 测试4: URL路径组件验证")
    print("URL路径构成分析:")
    print(f"  基础URL: {base_url}")
    print(f"  API前缀: /api/v1")
    print(f"  图片路由前缀: /image")
    print(f"  回调端点: /callback")
    print(f"  完整回调URL: {callback_url}")
    
    # 测试错误的URL路径
    wrong_urls = [
        f"{base_url}/api/v1/callback",  # 缺少 /image 前缀
        f"{base_url}/api/v1/image-generation/callback",  # 错误的前缀
        f"{base_url}/image/callback",  # 缺少 /api/v1 前缀
        f"{base_url}/api/v2/image/callback",  # 错误的版本号
    ]
    
    print("\n🔍 测试错误URL路径（应该返回404）:")
    for wrong_url in wrong_urls:
        try:
            response = requests.post(
                wrong_url,
                headers={"Content-Type": "application/json"},
                json=test_data,
                timeout=5
            )
            if response.status_code == 404:
                print(f"✅ {wrong_url} - 正确返回404")
            else:
                print(f"⚠️ {wrong_url} - 意外响应: {response.status_code}")
        except requests.exceptions.RequestException:
            print(f"✅ {wrong_url} - 连接失败（预期）")
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    print("1. ✅ 健康检查端点验证")
    print("2. ✅ 回调接口路径验证") 
    print("3. ✅ 数据格式验证")
    print("4. ✅ 错误路径验证")
    print("\n🎯 回调URL配置正确: http://localhost:8000/api/v1/image/callback")

def test_production_url():
    """测试生产环境URL格式"""
    print("\n🌐 生产环境URL格式验证")
    print("=" * 30)
    
    production_domains = [
        "https://api.example.com",
        "https://your-domain.com", 
        "https://bridal-api.com"
    ]
    
    for domain in production_domains:
        callback_url = f"{domain}/api/v1/image/callback"
        print(f"生产环境回调URL: {callback_url}")
    
    print("\n📝 配置示例:")
    print("开发环境: IMAGE_GENERATION_CALLBACK_URL=http://localhost:8000/api/v1/image/callback")
    print("生产环境: IMAGE_GENERATION_CALLBACK_URL=https://your-domain.com/api/v1/image/callback")

if __name__ == "__main__":
    try:
        test_callback_url()
        test_production_url()
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        sys.exit(1)
