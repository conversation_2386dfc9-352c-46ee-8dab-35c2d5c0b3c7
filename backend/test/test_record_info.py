#!/usr/bin/env python3
"""
测试图片生成记录详情获取功能

这个脚本用于测试新添加的获取图片生成记录详情的接口
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.generation_image_service import ImageGenerationService


def test_get_record_info():
    """测试获取记录详情功能"""
    print("=== 图片生成记录详情获取测试 ===\n")

    # 创建服务实例
    service = ImageGenerationService()

    # 检查服务配置
    print("1. 检查服务配置:")
    print(f"   API URL: {service.api_url}")
    print(f"   Record Info API URL: {service.record_info_api_url}")
    print(f"   API Token配置: {'✓' if service.api_token else '✗'}")
    print()

    if not service.api_token:
        print("❌ 错误: API Token未配置")
        print("请在.env文件中设置 IMAGE_GENERATION_API_TOKEN")
        return

    # 测试用例1: 测试无效的task_id
    print("2. 测试无效的task_id:")
    result = service.get_generation_record_info("")
    print(f"   空task_id结果: success={result.success}, error_code={result.error_code}")
    print(f"   错误信息: {result.message}")
    print()

    # 测试用例2: 测试有效的task_id（使用示例中的task_id）
    print("3. 测试有效的task_id:")
    test_task_id = "2857db2e20044233c72c35cbbb168008"  # 从示例响应中获取的task_id
    result = service.get_generation_record_info(test_task_id)
    print(f"   结果: success={result.success}")
    if result.success:
        print(f"   响应数据: {result.data}")
    else:
        print(f"   错误代码: {result.error_code}")
        print(f"   错误信息: {result.message}")
        print(f"   HTTP状态码: {result.http_status_code}")
    print()

    # 测试用例3: 测试不存在的task_id
    print("4. 测试不存在的task_id:")
    fake_task_id = "nonexistent_task_id_12345"
    result = service.get_generation_record_info(fake_task_id)
    print(f"   结果: success={result.success}")
    if not result.success:
        print(f"   错误代码: {result.error_code}")
        print(f"   错误信息: {result.message}")
        print(f"   HTTP状态码: {result.http_status_code}")
    print()


if __name__ == "__main__":
    test_get_record_info()
