#!/usr/bin/env python3
"""
测试Apple登录API修复

验证现有用户再次登录时不会被默认生成的姓名覆盖
"""

import sys
import os
import json
import requests
import jwt
import uuid
from datetime import datetime, timezone, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from sqlmodel import Session
from app.core.db import engine
from app.models import User, AuthProviderEnum, PlatformEnum

def create_test_apple_token(user_id: str = "001031.test123456789abcdef.0506"):
    """创建测试用的 Apple Identity Token"""
    payload = {
        "iss": "https://appleid.apple.com",
        "aud": "com.wenhaofree.bridal-swift",
        "exp": int((datetime.now(timezone.utc) + timedelta(hours=1)).timestamp()),
        "iat": int(datetime.now(timezone.utc).timestamp()),
        "sub": user_id,
        "nonce": "TEST-NONCE-12345",
        "c_hash": "TestHashValue123",
        "email": "<EMAIL>",
        "email_verified": True,
        "is_private_email": True,
        "auth_time": int(datetime.now(timezone.utc).timestamp()),
        "nonce_supported": True
    }
    
    return jwt.encode(payload, "test-secret", algorithm="HS256")

def create_test_user():
    """创建测试用户"""
    with Session(engine) as session:
        # 创建一个有真实姓名的用户
        test_user = User(
            id=uuid.uuid4(),
            email="<EMAIL>",
            is_active=True,
            is_superuser=False,
            full_name="张三",  # 真实的中文姓名
            platform=PlatformEnum.ios,
            auth_provider=AuthProviderEnum.apple,
            provider_user_id="001031.test123456789abcdef.0506",
            created_at=datetime.now(timezone.utc),
            hashed_password=None
        )
        
        session.add(test_user)
        session.commit()
        session.refresh(test_user)
        
        print(f"✓ 创建测试用户: {test_user.email}")
        print(f"  原始姓名: '{test_user.full_name}'")
        print(f"  用户ID: {test_user.id}")
        print(f"  Provider User ID: {test_user.provider_user_id}")
        
        return test_user

def test_apple_login_name_preservation():
    """测试Apple登录姓名保留"""
    print("🧪 测试Apple登录姓名保留...")
    
    # 1. 创建测试用户
    test_user = create_test_user()
    original_name = test_user.full_name
    
    # 2. 创建Apple Identity Token
    identity_token = create_test_apple_token(test_user.provider_user_id)
    
    # 3. 准备登录请求
    request_data = {
        "identity_token": identity_token,
        "platform": "ios"
        # 注意：不提供 user_info，模拟非首次登录
    }
    
    print(f"\n📱 发送Apple登录请求...")
    print(f"  不提供用户信息（模拟非首次登录）")
    
    try:
        # 4. 发送登录请求
        response = requests.post(
            "http://localhost:8000/api/v1/oauth/apple/login",
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ 登录成功!")
            print(f"  用户ID: {result.get('user', {}).get('id')}")
            print(f"  邮箱: {result.get('user', {}).get('email')}")
            print(f"  登录后姓名: '{result.get('user', {}).get('full_name')}'")
            print(f"  是否新用户: {result.get('is_new_user')}")
            
            # 5. 验证姓名是否被保留
            current_name = result.get('user', {}).get('full_name')
            
            if current_name == original_name:
                print(f"✅ 姓名保留测试通过!")
                print(f"  原始姓名: '{original_name}'")
                print(f"  保留姓名: '{current_name}'")
                return True
            else:
                print(f"❌ 姓名保留测试失败!")
                print(f"  原始姓名: '{original_name}'")
                print(f"  被覆盖为: '{current_name}'")
                return False
                
        else:
            print(f"❌ 登录失败:")
            try:
                error_detail = response.json()
                print(f"   错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ 连接失败: 请确保 FastAPI 服务器正在运行 (http://localhost:8000)")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False
    finally:
        # 清理测试数据
        cleanup_test_user(test_user.id)

def test_apple_login_with_user_info():
    """测试Apple登录时提供用户信息"""
    print("\n🧪 测试Apple登录提供用户信息...")
    
    # 1. 创建测试用户
    test_user = create_test_user()
    original_name = test_user.full_name
    
    # 2. 创建Apple Identity Token
    identity_token = create_test_apple_token(test_user.provider_user_id)
    
    # 3. 准备登录请求（提供真实姓名）
    request_data = {
        "identity_token": identity_token,
        "platform": "ios",
        "user_info": {
            "firstName": "李",
            "lastName": "四"
        }
    }
    
    print(f"📱 发送Apple登录请求...")
    print(f"  提供真实姓名: 李 四")
    
    try:
        # 4. 发送登录请求
        response = requests.post(
            "http://localhost:8000/api/v1/oauth/apple/login",
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ 登录成功!")
            print(f"  登录后姓名: '{result.get('user', {}).get('full_name')}'")
            
            # 5. 验证姓名是否被更新为真实姓名
            current_name = result.get('user', {}).get('full_name')
            
            if current_name == "李 四":
                print(f"✅ 真实姓名更新测试通过!")
                print(f"  从 '{original_name}' 更新为 '{current_name}'")
                return True
            else:
                print(f"❌ 真实姓名更新测试失败!")
                print(f"  期望: '李 四', 实际: '{current_name}'")
                return False
                
        else:
            print(f"❌ 登录失败:")
            try:
                error_detail = response.json()
                print(f"   错误详情: {json.dumps(error_detail, indent=2, ensure_ascii=False)}")
            except:
                print(f"   响应内容: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ 连接失败: 请确保 FastAPI 服务器正在运行 (http://localhost:8000)")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False
    finally:
        # 清理测试数据
        cleanup_test_user(test_user.id)

def cleanup_test_user(user_id):
    """清理测试用户"""
    try:
        with Session(engine) as session:
            user = session.get(User, user_id)
            if user:
                session.delete(user)
                session.commit()
                print(f"🗑️ 清理测试用户: {user.email}")
    except Exception as e:
        print(f"⚠️ 清理测试用户失败: {str(e)}")

def main():
    """主函数"""
    print("🔧 Apple登录姓名保留API测试")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code != 200:
            print("❌ FastAPI服务器未正常运行，请先启动服务器")
            print("   运行: uv run uvicorn app.main:app --reload")
            return False
    except:
        print("❌ 无法连接到FastAPI服务器，请先启动服务器")
        print("   运行: uv run uvicorn app.main:app --reload")
        return False
    
    print("✅ FastAPI服务器运行正常")
    
    # 执行测试
    test_results = []
    
    # 测试1: 姓名保留
    test_results.append(test_apple_login_name_preservation())
    
    # 测试2: 真实姓名更新
    test_results.append(test_apple_login_with_user_info())
    
    print("\n" + "=" * 50)
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
        print("✅ Apple登录姓名保留功能修复成功")
    else:
        print(f"💥 部分测试失败! ({passed}/{total})")
        print("❌ 需要进一步检查和修复")
    
    return passed == total

if __name__ == "__main__":
    main()
