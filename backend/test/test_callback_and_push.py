#!/usr/bin/env python3
"""
测试图片生成回调和推送通知功能

这个脚本用于测试新添加的回调处理和推送通知功能
"""

import sys
import os
import asyncio
import json

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.apns_service import APNSService
from app.services.image_callback_service import ImageCallbackService


def test_apns_configuration():
    """测试APNS配置"""
    print("=== APNS配置测试 ===\n")
    
    service = APNSService()
    
    print("1. 检查APNS配置:")
    print(f"   Key ID: {'✓' if service.key_id else '✗'}")
    print(f"   Team ID: {'✓' if service.team_id else '✗'}")
    print(f"   Bundle ID: {'✓' if service.bundle_id else '✗'}")
    print(f"   Key Path: {'✓' if service.key_path else '✗'}")
    print(f"   Use Sandbox: {service.use_sandbox}")
    print(f"   APNS URL: {service.apns_url}")
    print(f"   Is Configured: {'✓' if service.is_configured() else '✗'}")
    print()
    
    if not service.is_configured():
        print("❌ APNS未完全配置")
        print("请在.env文件中设置以下环境变量:")
        print("- APNS_KEY_ID")
        print("- APNS_TEAM_ID")
        print("- APNS_BUNDLE_ID")
        print("- APNS_KEY_PATH")
        return False
    
    return True


async def test_apns_notification():
    """测试APNS推送通知"""
    print("=== APNS推送通知测试 ===\n")
    
    service = APNSService()
    
    if not service.is_configured():
        print("❌ APNS未配置，跳过推送测试")
        return
    
    # 测试设备token（这是一个示例token，实际使用时需要真实的设备token）
    test_device_token = "test_device_token_1234567890abcdef"
    
    print("2. 测试发送推送通知:")
    print(f"   设备Token: {test_device_token}")
    
    try:
        success = await service.send_notification(
            device_token=test_device_token,
            title="图片生成完成",
            body="您的图片已成功生成，点击查看结果",
            data={
                "type": "image_generation_success",
                "task_id": "test12345",
                "record_id": "test-record-id",
                "result_urls": ["https://example.com/result.png"]
            }
        )
        
        if success:
            print("   ✓ 推送通知发送成功")
        else:
            print("   ✗ 推送通知发送失败")
            
    except Exception as e:
        print(f"   ✗ 推送通知发送异常: {str(e)}")
    
    print()


def test_callback_data_validation():
    """测试回调数据验证"""
    print("=== 回调数据验证测试 ===\n")
    
    # 模拟回调服务（不需要数据库连接）
    class MockCallbackService:
        def validate_callback_data(self, callback_data):
            from app.services.image_callback_service import ImageCallbackService
            service = ImageCallbackService(None)  # 传入None作为session
            return service.validate_callback_data(callback_data)
    
    service = MockCallbackService()
    
    print("3. 测试回调数据验证:")
    
    # 测试用例1: 有效的成功回调
    success_callback = {
        "code": 200,
        "msg": "success",
        "data": {
            "taskId": "test12345",
            "info": {
                "result_urls": [
                    "https://example.com/result/image1.png"
                ]
            }
        }
    }
    
    is_valid, error_msg = service.validate_callback_data(success_callback)
    print(f"   成功回调数据: {'✓' if is_valid else '✗'} {error_msg}")
    
    # 测试用例2: 有效的失败回调
    failure_callback = {
        "code": 400,
        "msg": "您的内容被 OpenAI 标记为违反内容政策",
        "data": {
            "taskId": "test12345",
            "info": None
        }
    }
    
    is_valid, error_msg = service.validate_callback_data(failure_callback)
    print(f"   失败回调数据: {'✓' if is_valid else '✗'} {error_msg}")
    
    # 测试用例3: 无效的回调数据（缺少code）
    invalid_callback = {
        "msg": "success",
        "data": {
            "taskId": "test12345"
        }
    }
    
    is_valid, error_msg = service.validate_callback_data(invalid_callback)
    print(f"   无效回调数据: {'✗' if not is_valid else '✓'} {error_msg}")
    
    # 测试用例4: 无效的回调数据（缺少taskId）
    invalid_callback2 = {
        "code": 200,
        "msg": "success",
        "data": {
            "info": {}
        }
    }
    
    is_valid, error_msg = service.validate_callback_data(invalid_callback2)
    print(f"   缺少taskId: {'✗' if not is_valid else '✓'} {error_msg}")
    
    print()


def test_configuration_summary():
    """配置总结"""
    print("=== 配置总结 ===\n")
    
    from app.core.config import settings
    
    print("4. 当前配置状态:")
    print(f"   图片生成API URL: {settings.IMAGE_GENERATION_API_URL}")
    print(f"   图片生成API Token: {'✓' if settings.IMAGE_GENERATION_API_TOKEN else '✗'}")
    print(f"   回调URL: {settings.IMAGE_GENERATION_CALLBACK_URL}")
    print(f"   APNS Key ID: {'✓' if settings.APNS_KEY_ID else '✗'}")
    print(f"   APNS Team ID: {'✓' if settings.APNS_TEAM_ID else '✗'}")
    print(f"   APNS Bundle ID: {'✓' if settings.APNS_BUNDLE_ID else '✗'}")
    print(f"   APNS Key Path: {'✓' if settings.APNS_KEY_PATH else '✗'}")
    print(f"   APNS Use Sandbox: {settings.APNS_USE_SANDBOX}")
    print()


async def main():
    """主测试函数"""
    print("=== 图片生成回调和推送通知功能测试 ===\n")
    
    # 测试APNS配置
    apns_configured = test_apns_configuration()
    
    # 测试APNS推送通知
    if apns_configured:
        await test_apns_notification()
    
    # 测试回调数据验证
    test_callback_data_validation()
    
    # 配置总结
    test_configuration_summary()
    
    print("=== 测试完成 ===")
    print("\n注意事项:")
    print("1. 确保在.env文件中配置了所有必需的环境变量")
    print("2. APNS需要有效的.p8密钥文件和正确的配置")
    print("3. 推送通知需要真实的设备token才能成功发送")
    print("4. 回调URL需要在图片生成API中正确配置")


if __name__ == "__main__":
    asyncio.run(main())
