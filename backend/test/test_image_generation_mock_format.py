#!/usr/bin/env python3
"""
测试图片生成Mock功能的响应格式
Test Image Generation Mock Response Format
"""

import requests
import json
import re
import time
from typing import Dict, Any


def get_auth_token() -> str:
    """获取认证token"""
    login_data = {
        "username": "<EMAIL>",
        "password": "changethis"
    }

    try:
        response = requests.post(
            "http://localhost:8000/api/v1/login/access-token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            return result.get("access_token")
        else:
            print(f"❌ 登录失败: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"   错误详情: {error_detail}")
            except:
                print(f"   响应内容: {response.text}")
            return None

    except Exception as e:
        print(f"❌ 登录异常: {str(e)}")
        return None


def test_mock_response_format():
    """测试Mock响应格式是否符合要求"""
    print("🧪 测试Mock响应格式...")
    
    # 获取认证token
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token")
        return False
    
    print("✅ 成功获取认证token")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 准备测试请求
    request_data = {
        "prompt": "Beautiful wedding photography test",
        "size": "1:1",
        "n_variants": 1,
        "is_enhance": False
    }
    
    try:
        # 发送请求
        response = requests.post(
            "http://localhost:8000/api/v1/image/generate-image",
            json=request_data,
            headers=headers,
            timeout=30
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
        
        # 解析响应数据
        result = response.json()
        print(f"📋 响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        # 验证响应格式
        success = True
        
        # 检查顶级字段
        if "code" not in result:
            print("❌ 缺少 'code' 字段")
            success = False
        elif result["code"] != 200:
            print(f"❌ 'code' 字段值错误，期望: 200, 实际: {result['code']}")
            success = False
        else:
            print("✅ 'code' 字段正确: 200")
        
        if "msg" not in result:
            print("❌ 缺少 'msg' 字段")
            success = False
        elif result["msg"] != "success":
            print(f"❌ 'msg' 字段值错误，期望: 'success', 实际: '{result['msg']}'")
            success = False
        else:
            print("✅ 'msg' 字段正确: 'success'")
        
        if "data" not in result:
            print("❌ 缺少 'data' 字段")
            success = False
        else:
            data = result["data"]
            if not isinstance(data, dict):
                print("❌ 'data' 字段不是对象类型")
                success = False
            else:
                print("✅ 'data' 字段存在且为对象类型")
                
                # 检查taskId字段
                if "taskId" not in data:
                    print("❌ 'data' 中缺少 'taskId' 字段")
                    success = False
                else:
                    task_id = data["taskId"]
                    if not isinstance(task_id, str):
                        print("❌ 'taskId' 不是字符串类型")
                        success = False
                    elif not task_id.startswith("test-"):
                        print(f"❌ 'taskId' 前缀错误，期望以 'test-' 开头, 实际: '{task_id}'")
                        success = False
                    elif len(task_id) != 17:  # "test-" (5) + 12位随机字符
                        print(f"❌ 'taskId' 长度错误，期望: 17, 实际: {len(task_id)}")
                        success = False
                    elif not re.match(r'^test-[a-z0-9]{12}$', task_id):
                        print(f"❌ 'taskId' 格式错误，期望: test-[a-z0-9]{{12}}, 实际: '{task_id}'")
                        success = False
                    else:
                        print(f"✅ 'taskId' 格式正确: '{task_id}'")
        
        return success
        
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False


def test_multiple_requests_different_task_ids():
    """测试多次请求生成不同的任务ID"""
    print("\n🧪 测试多次请求生成不同任务ID...")
    
    # 获取认证token
    token = get_auth_token()
    if not token:
        print("❌ 无法获取认证token")
        return False
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    request_data = {
        "prompt": "Test prompt for multiple requests",
        "size": "1:1",
        "n_variants": 1,
        "is_enhance": False
    }
    
    task_ids = []
    
    # 发送5次请求
    for i in range(5):
        try:
            response = requests.post(
                "http://localhost:8000/api/v1/image/generate-image",
                json=request_data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                task_id = result.get("data", {}).get("taskId")
                task_ids.append(task_id)
                print(f"  第{i+1}次请求任务ID: {task_id}")
            else:
                print(f"❌ 第{i+1}次请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 第{i+1}次请求异常: {str(e)}")
            return False
    
    # 验证所有任务ID都不同
    unique_task_ids = set(task_ids)
    if len(unique_task_ids) == len(task_ids):
        print(f"✅ 所有任务ID都不同 ({len(task_ids)}个)")
        return True
    else:
        print(f"❌ 发现重复的任务ID，唯一ID数量: {len(unique_task_ids)}, 总数: {len(task_ids)}")
        return False


def check_server_status():
    """检查服务器状态"""
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        return response.status_code == 200
    except:
        return False


def main():
    """主测试函数"""
    print("🚀 图片生成Mock响应格式测试")
    print("=" * 50)
    
    # 检查服务器状态
    if not check_server_status():
        print("❌ FastAPI服务器未运行或无法访问")
        print("   请确保服务器在 http://localhost:8000 运行")
        print("   启动命令: uv run uvicorn app.main:app --reload")
        return False
    
    print("✅ FastAPI服务器运行正常")
    
    # 执行测试
    test_results = []
    
    # 测试1: Mock响应格式
    test_results.append(test_mock_response_format())
    
    # 测试2: 多次请求不同任务ID
    test_results.append(test_multiple_requests_different_task_ids())
    
    print("\n" + "=" * 50)
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
        print("✅ Mock响应格式符合要求")
        print("\n📋 测试总结:")
        print("  ✅ 响应格式: {\"code\": 200, \"msg\": \"success\", \"data\": {\"taskId\": \"test-xxx\"}}")
        print("  ✅ 任务ID前缀: test-")
        print("  ✅ 任务ID格式: test-[a-z0-9]{12}")
        print("  ✅ 多次请求生成不同任务ID")
        print("\n💡 使用说明:")
        print("  - 设置环境变量 ENABLE_IMAGE_GENERATION_MOCK=True 启用Mock模式")
        print("  - Mock模式下，每次请求都会生成新的随机任务ID")
    else:
        print(f"💥 部分测试失败! ({passed}/{total})")
        print("❌ 需要检查Mock功能实现")
    
    return passed == total


if __name__ == "__main__":
    main()
