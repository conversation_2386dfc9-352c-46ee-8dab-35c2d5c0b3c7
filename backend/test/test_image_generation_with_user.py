#!/usr/bin/env python3
"""
带用户权限检查的图片生成服务测试脚本

这个脚本测试完整的用户权限检查和使用记录功能
"""

import sys
import os
import asyncio
import uuid
from datetime import datetime, timezone, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from sqlmodel import Session, create_engine, select
from app.core.config import settings
from app.models import (
    User, Subscription, CreditPackage, UsageRecord, ImageGenerationRecord,
    SubscriptionPlatformEnum, UsageTypeEnum
)
from app.services.generation_image_service import (
    ImageGenerationRequest,
    ImageGenerationService
)
from app.services.billing import BillingService


def create_test_database_session():
    """创建测试数据库会话"""
    engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
    return Session(engine)


def create_test_user(session: Session) -> User:
    """创建测试用户"""
    test_user = User(
        email=f"test_user_{uuid.uuid4().hex[:8]}@example.com",
        full_name="Test User",
        hashed_password="test_password_hash",
        is_active=True,
        is_superuser=False
    )
    session.add(test_user)
    session.commit()
    session.refresh(test_user)
    return test_user


def create_test_subscription(session: Session, user: User) -> Subscription:
    """为用户创建测试订阅"""
    now = datetime.now(timezone.utc)
    subscription = Subscription(
        user_id=user.id,
        product_id="sub_monthly_40",
        platform=SubscriptionPlatformEnum.stripe,
        start_date=now,
        end_date=now + timedelta(days=30),
        is_active=True,
        original_transaction_id=f"test_txn_{uuid.uuid4().hex[:8]}"
    )
    session.add(subscription)
    session.commit()
    session.refresh(subscription)
    return subscription


def create_test_credits(session: Session, user: User, credits: int = 50) -> CreditPackage:
    """为用户创建测试积分包"""
    credit_package = CreditPackage(
        user_id=user.id,
        credits=credits,
        remaining_credits=credits,
        product_id="credits_pack_50",
        platform=SubscriptionPlatformEnum.stripe
    )
    session.add(credit_package)
    session.commit()
    session.refresh(credit_package)
    return credit_package


async def test_user_with_subscription():
    """测试有订阅的用户"""
    print("=== 测试有订阅的用户 ===\n")
    
    session = create_test_database_session()
    
    try:
        # 创建测试用户和订阅
        user = create_test_user(session)
        subscription = create_test_subscription(session, user)
        
        print(f"创建测试用户: {user.email}")
        print(f"创建订阅: {subscription.product_id} (限制40次/月)")
        
        # 创建服务实例
        service = ImageGenerationService(session=session)
        
        # 检查用户状态
        billing_service = BillingService(session)
        status = billing_service.get_user_subscription_status(user.id)
        
        print(f"\n用户状态:")
        print(f"  有效订阅: {'✓' if status.has_active_subscription else '✗'}")
        print(f"  月度限制: {status.monthly_limit}")
        print(f"  已使用: {status.monthly_usage_count}")
        print(f"  可用积分: {status.total_credits}")
        print(f"  可使用服务: {'✓' if status.can_use_service else '✗'}")
        
        # 测试图片生成
        request = ImageGenerationRequest(
            prompt="A beautiful landscape with mountains and lakes",
            size="1:1",
            n_variants=1
        )
        
        print(f"\n测试图片生成:")
        result = service.generate_image_sync_with_user_check(user.id, request)
        
        print(f"  成功: {'✓' if result.success else '✗'}")
        print(f"  消息: {result.message}")
        
        if result.success and result.data:
            print("  响应数据:")
            for key, value in result.data.items():
                if key not in ['code', 'msg']:  # 跳过API错误信息
                    print(f"    {key}: {value}")
        
        if result.error:
            print(f"  错误: {result.error}")
        
        # 检查生成记录
        records = session.exec(
            select(ImageGenerationRecord).where(ImageGenerationRecord.user_id == user.id)
        ).all()
        
        print(f"\n生成记录: {len(records)} 条")
        for record in records:
            print(f"  记录ID: {record.id}")
            print(f"  状态: {record.status}")
            print(f"  提示词: {record.prompt[:50]}...")
            print(f"  创建时间: {record.created_at}")
        
    finally:
        # 清理测试数据
        session.exec(select(ImageGenerationRecord).where(ImageGenerationRecord.user_id == user.id)).all()
        for record in session.exec(select(ImageGenerationRecord).where(ImageGenerationRecord.user_id == user.id)):
            session.delete(record)
        session.delete(subscription)
        session.delete(user)
        session.commit()
        session.close()


async def test_user_with_credits_only():
    """测试只有积分的用户"""
    print("\n=== 测试只有积分的用户 ===\n")
    
    session = create_test_database_session()
    
    try:
        # 创建测试用户和积分
        user = create_test_user(session)
        credits = create_test_credits(session, user, 25)
        
        print(f"创建测试用户: {user.email}")
        print(f"创建积分包: {credits.credits} 积分")
        
        # 创建服务实例
        service = ImageGenerationService(session=session)
        
        # 检查用户状态
        billing_service = BillingService(session)
        status = billing_service.get_user_subscription_status(user.id)
        
        print(f"\n用户状态:")
        print(f"  有效订阅: {'✓' if status.has_active_subscription else '✗'}")
        print(f"  月度限制: {status.monthly_limit}")
        print(f"  已使用: {status.monthly_usage_count}")
        print(f"  可用积分: {status.total_credits}")
        print(f"  可使用服务: {'✓' if status.can_use_service else '✗'}")
        
        # 测试图片生成
        request = ImageGenerationRequest(
            prompt="A futuristic city with flying cars",
            size="16:9",
            n_variants=1
        )
        
        print(f"\n测试图片生成:")
        result = service.generate_image_sync_with_user_check(user.id, request)
        
        print(f"  成功: {'✓' if result.success else '✗'}")
        print(f"  消息: {result.message}")
        
        if result.success and result.data:
            print("  响应数据:")
            for key, value in result.data.items():
                if key not in ['code', 'msg']:  # 跳过API错误信息
                    print(f"    {key}: {value}")
        
        if result.error:
            print(f"  错误: {result.error}")
        
    finally:
        # 清理测试数据
        for record in session.exec(select(ImageGenerationRecord).where(ImageGenerationRecord.user_id == user.id)):
            session.delete(record)
        session.delete(credits)
        session.delete(user)
        session.commit()
        session.close()


async def test_user_without_access():
    """测试没有权限的用户"""
    print("\n=== 测试没有权限的用户 ===\n")
    
    session = create_test_database_session()
    
    try:
        # 创建测试用户（无订阅无积分）
        user = create_test_user(session)
        
        print(f"创建测试用户: {user.email}")
        print("无订阅，无积分")
        
        # 创建服务实例
        service = ImageGenerationService(session=session)
        
        # 检查用户状态
        billing_service = BillingService(session)
        status = billing_service.get_user_subscription_status(user.id)
        
        print(f"\n用户状态:")
        print(f"  有效订阅: {'✓' if status.has_active_subscription else '✗'}")
        print(f"  月度限制: {status.monthly_limit}")
        print(f"  已使用: {status.monthly_usage_count}")
        print(f"  可用积分: {status.total_credits}")
        print(f"  可使用服务: {'✓' if status.can_use_service else '✗'}")
        
        # 测试图片生成（应该失败）
        request = ImageGenerationRequest(
            prompt="This should fail due to no access",
            size="1:1",
            n_variants=1
        )
        
        print(f"\n测试图片生成:")
        result = service.generate_image_sync_with_user_check(user.id, request)
        
        print(f"  成功: {'✓' if result.success else '✗'}")
        print(f"  消息: {result.message}")
        
        if result.error:
            print(f"  错误: {result.error}")
        
        # 检查是否创建了失败记录
        records = session.exec(
            select(ImageGenerationRecord).where(ImageGenerationRecord.user_id == user.id)
        ).all()
        
        print(f"\n生成记录: {len(records)} 条")
        for record in records:
            print(f"  记录ID: {record.id}")
            print(f"  状态: {record.status}")
            print(f"  错误信息: {record.error_message}")
        
    finally:
        # 清理测试数据
        for record in session.exec(select(ImageGenerationRecord).where(ImageGenerationRecord.user_id == user.id)):
            session.delete(record)
        session.delete(user)
        session.commit()
        session.close()


if __name__ == "__main__":
    print("开始测试带用户权限检查的图片生成服务...\n")
    
    # 运行各种测试场景
    asyncio.run(test_user_with_subscription())
    asyncio.run(test_user_with_credits_only())
    asyncio.run(test_user_without_access())
    
    print("\n所有测试完成!")
    print("\n注意事项:")
    print("1. 这些测试会在数据库中创建和删除测试数据")
    print("2. 确保数据库连接正常")
    print("3. API调用可能会因为token无效而返回401错误，这是正常的")
    print("4. 重点关注权限检查和记录创建功能是否正常工作")
