#!/usr/bin/env python3
"""
测试Apple登录邮箱保护逻辑

验证现有用户的真实邮箱不会被Apple隐私邮箱覆盖
"""

import sys
import os
import json
import requests
import jwt
import uuid
from datetime import datetime, timezone, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from sqlmodel import Session
from app.core.db import engine
from app.models import User, AuthProviderEnum, PlatformEnum

def create_test_apple_token(user_id: str = "001031.test123456789abcdef.0506", email: str = "<EMAIL>"):
    """创建测试用的 Apple Identity Token"""
    payload = {
        "iss": "https://appleid.apple.com",
        "aud": "com.wenhaofree.bridal-swift",
        "exp": int((datetime.now(timezone.utc) + timedelta(hours=1)).timestamp()),
        "iat": int(datetime.now(timezone.utc).timestamp()),
        "sub": user_id,
        "nonce": "TEST-NONCE-12345",
        "c_hash": "TestHashValue123",
        "email": email,
        "email_verified": True,
        "is_private_email": True,
        "auth_time": int(datetime.now(timezone.utc).timestamp()),
        "nonce_supported": True
    }
    
    return jwt.encode(payload, "test-secret", algorithm="HS256")

def create_test_user_with_real_email():
    """创建有真实邮箱的测试用户"""
    with Session(engine) as session:
        unique_id = uuid.uuid4().hex[:8]
        test_user = User(
            id=uuid.uuid4(),
            email=f"zhangsan.{unique_id}@gmail.com",  # 真实邮箱（唯一）
            is_active=True,
            is_superuser=False,
            full_name="张三",
            platform=PlatformEnum.ios,
            auth_provider=AuthProviderEnum.apple,
            provider_user_id=f"001031.test{unique_id}.0506",
            created_at=datetime.now(timezone.utc),
            hashed_password=None
        )
        
        session.add(test_user)
        session.commit()
        session.refresh(test_user)
        
        print(f"✓ 创建有真实邮箱的用户: {test_user.email}")
        print(f"  姓名: '{test_user.full_name}'")
        print(f"  用户ID: {test_user.id}")
        
        return test_user

def create_test_user_with_private_email():
    """创建有Apple隐私邮箱的测试用户"""
    with Session(engine) as session:
        unique_id = uuid.uuid4().hex[:8]
        test_user = User(
            id=uuid.uuid4(),
            email=f"{unique_id}@privaterelay.appleid.com",  # Apple隐私邮箱（唯一）
            is_active=True,
            is_superuser=False,
            full_name="李四",
            platform=PlatformEnum.ios,
            auth_provider=AuthProviderEnum.apple,
            provider_user_id=f"001031.test{unique_id}.0506",
            created_at=datetime.now(timezone.utc),
            hashed_password=None
        )
        
        session.add(test_user)
        session.commit()
        session.refresh(test_user)
        
        print(f"✓ 创建有Apple隐私邮箱的用户: {test_user.email}")
        print(f"  姓名: '{test_user.full_name}'")
        print(f"  用户ID: {test_user.id}")
        
        return test_user

def test_real_email_preservation():
    """测试真实邮箱保护"""
    print("🧪 测试真实邮箱保护...")
    
    # 1. 创建有真实邮箱的用户
    test_user = create_test_user_with_real_email()
    original_email = test_user.email
    
    # 2. 创建Apple Identity Token（包含隐私邮箱）
    identity_token = create_test_apple_token(
        test_user.provider_user_id, 
        "<EMAIL>"  # 不同的Apple隐私邮箱
    )
    
    # 3. 准备登录请求
    request_data = {
        "identity_token": identity_token,
        "platform": "ios"
    }
    
    print(f"\n📱 发送Apple登录请求...")
    print(f"  原始邮箱: {original_email}")
    print(f"  Token中的邮箱: <EMAIL>")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/oauth/apple/login",
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ 登录成功!")
            print(f"  登录后邮箱: '{result.get('user', {}).get('email')}'")
            
            # 验证真实邮箱是否被保留
            current_email = result.get('user', {}).get('email')
            
            if current_email == original_email:
                print(f"✅ 真实邮箱保护测试通过!")
                print(f"  真实邮箱 '{original_email}' 被正确保留")
                return True
            else:
                print(f"❌ 真实邮箱保护测试失败!")
                print(f"  真实邮箱 '{original_email}' 被覆盖为 '{current_email}'")
                return False
                
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False
    finally:
        cleanup_test_user(test_user.id)

def test_private_email_update_to_real():
    """测试Apple隐私邮箱可以被真实邮箱替换"""
    print("\n🧪 测试隐私邮箱更新为真实邮箱...")
    
    # 1. 创建有Apple隐私邮箱的用户
    test_user = create_test_user_with_private_email()
    original_email = test_user.email
    
    # 2. 创建Apple Identity Token（包含真实邮箱）
    unique_suffix = uuid.uuid4().hex[:8]
    real_email = f"lisi.{unique_suffix}@gmail.com"
    identity_token = create_test_apple_token(
        test_user.provider_user_id, 
        real_email
    )
    
    # 3. 准备登录请求（提供真实邮箱）
    request_data = {
        "identity_token": identity_token,
        "platform": "ios",
        "real_email": real_email
    }
    
    print(f"📱 发送Apple登录请求...")
    print(f"  原始邮箱: {original_email}")
    print(f"  提供真实邮箱: {real_email}")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/v1/oauth/apple/login",
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"📥 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            
            print(f"✅ 登录成功!")
            print(f"  登录后邮箱: '{result.get('user', {}).get('email')}'")
            
            # 验证邮箱是否被更新为真实邮箱
            current_email = result.get('user', {}).get('email')
            
            if current_email == real_email:
                print(f"✅ 隐私邮箱更新测试通过!")
                print(f"  从隐私邮箱 '{original_email}' 更新为真实邮箱 '{current_email}'")
                return True
            else:
                print(f"❌ 隐私邮箱更新测试失败!")
                print(f"  期望 '{real_email}', 实际 '{current_email}'")
                return False
                
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False
    finally:
        cleanup_test_user(test_user.id)

def cleanup_test_user(user_id):
    """清理测试用户"""
    try:
        with Session(engine) as session:
            user = session.get(User, user_id)
            if user:
                session.delete(user)
                session.commit()
                print(f"🗑️ 清理测试用户: {user.email}")
    except Exception as e:
        print(f"⚠️ 清理测试用户失败: {str(e)}")

def main():
    """主函数"""
    print("🔧 Apple登录邮箱保护测试")
    print("=" * 50)
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code != 200:
            print("❌ FastAPI服务器未正常运行")
            return False
    except:
        print("❌ 无法连接到FastAPI服务器")
        return False
    
    print("✅ FastAPI服务器运行正常")
    
    # 执行测试
    test_results = []
    
    # 测试1: 真实邮箱保护
    test_results.append(test_real_email_preservation())
    
    # 测试2: 隐私邮箱更新为真实邮箱
    test_results.append(test_private_email_update_to_real())
    
    print("\n" + "=" * 50)
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
        print("✅ Apple登录邮箱保护功能修复成功")
    else:
        print(f"💥 部分测试失败! ({passed}/{total})")
        print("❌ 需要进一步检查和修复")
    
    return passed == total

if __name__ == "__main__":
    main()
