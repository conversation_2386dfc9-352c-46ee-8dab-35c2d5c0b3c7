#!/usr/bin/env python3
"""
测试修改后的 get_generation_record_info 接口

这个脚本用于测试修改后的接口，验证它是否直接返回第三方API的响应数据，
而不进行额外的包装。

使用方法:
python test_record_info_direct.py --task-id YOUR_TASK_ID --user-token YOUR_JWT_TOKEN
python test_record_info_direct.py --task-id test_task_123 --user-token "eyJ..."
"""

import argparse
import json
import requests
import sys

def test_record_info_direct(base_url: str, task_id: str, user_token: str):
    """测试直接返回API数据的记录信息接口"""
    
    print("🧪 Testing Direct Record Info API")
    print("=" * 60)
    print(f"📋 Base URL: {base_url}")
    print(f"📋 Task ID: {task_id}")
    print(f"📋 User Token: {user_token[:20]}...")
    print()
    
    # 构建请求URL
    url = f"{base_url}/api/v1/image/record-info/{task_id}"
    
    # 构建请求头
    headers = {
        "Authorization": f"Bearer {user_token}",
        "Content-Type": "application/json"
    }
    
    print(f"📤 Sending GET request to: {url}")
    print(f"📋 Headers: {json.dumps(headers, indent=2, ensure_ascii=False)}")
    print()
    
    try:
        response = requests.get(
            url,
            headers=headers,
            timeout=30
        )
        
        print(f"📥 Response Status: {response.status_code}")
        print(f"📄 Response Headers: {dict(response.headers)}")
        print()
        
        # 尝试解析JSON响应
        try:
            response_data = response.json()
            print(f"📊 Response Data:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            print()
            
            # 分析响应结构
            print("🔍 Response Analysis:")
            print("=" * 40)
            
            if response.status_code == 200:
                print("✅ Request successful!")
                
                # 检查是否是直接的API响应（不包装）
                if "success" in response_data and "message" in response_data and "data" in response_data:
                    print("❌ Response appears to be WRAPPED (old format)")
                    print("   Contains: success, message, data fields")
                    print("   This suggests the old wrapping logic is still active")
                    return False
                else:
                    print("✅ Response appears to be DIRECT API data (new format)")
                    print("   Does not contain wrapper fields (success, message, data)")
                    print("   This suggests the modification is working correctly")
                    
                    # 分析响应字段
                    print(f"📋 Response contains {len(response_data)} top-level fields:")
                    for key, value in response_data.items():
                        value_type = type(value).__name__
                        if isinstance(value, (dict, list)):
                            value_preview = f"{value_type} with {len(value)} items"
                        else:
                            value_preview = f"{value_type}: {str(value)[:50]}..."
                        print(f"   - {key}: {value_preview}")
                    
                    return True
            else:
                print(f"❌ Request failed with status {response.status_code}")
                
                # 检查错误响应格式
                if "detail" in response_data:
                    print("✅ Error response uses FastAPI standard format (HTTPException)")
                    print(f"   Error detail: {response_data['detail']}")
                else:
                    print("❌ Error response uses custom format")
                
                return False
                
        except json.JSONDecodeError:
            print("❌ Response is not valid JSON")
            print(f"📄 Raw response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"💥 Request failed: {str(e)}")
        return False

def test_with_invalid_task_id(base_url: str, user_token: str):
    """测试无效task_id的错误处理"""
    
    print("\n🧪 Testing Invalid Task ID Error Handling")
    print("=" * 60)
    
    invalid_task_id = "invalid_task_id_12345"
    
    url = f"{base_url}/api/v1/image/record-info/{invalid_task_id}"
    headers = {
        "Authorization": f"Bearer {user_token}",
        "Content-Type": "application/json"
    }
    
    print(f"📤 Testing with invalid task ID: {invalid_task_id}")
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        
        print(f"📥 Response Status: {response.status_code}")
        
        if response.status_code != 200:
            try:
                error_data = response.json()
                print(f"📊 Error Response:")
                print(json.dumps(error_data, indent=2, ensure_ascii=False))
                
                if "detail" in error_data:
                    print("✅ Error uses FastAPI standard format")
                    return True
                else:
                    print("❌ Error uses non-standard format")
                    return False
                    
            except json.JSONDecodeError:
                print("❌ Error response is not valid JSON")
                return False
        else:
            print("⚠️  Unexpected success with invalid task ID")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"💥 Request failed: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description="测试直接返回API数据的记录信息接口")
    parser.add_argument("--base-url", default="http://127.0.0.1:8000", help="API基础URL")
    parser.add_argument("--task-id", required=True, help="要查询的任务ID")
    parser.add_argument("--user-token", required=True, help="用户JWT令牌")
    parser.add_argument("--test-invalid", action="store_true", help="同时测试无效task_id的处理")
    
    args = parser.parse_args()
    
    print("🧪 RECORD INFO DIRECT API TEST")
    print("=" * 80)
    print("📋 Testing the modified get_generation_record_info interface")
    print("📋 Expecting direct API response data without wrapper")
    print("=" * 80)
    
    # 测试正常情况
    success = test_record_info_direct(args.base_url, args.task_id, args.user_token)
    
    # 测试错误情况（可选）
    error_handling_success = True
    if args.test_invalid:
        error_handling_success = test_with_invalid_task_id(args.base_url, args.user_token)
    
    # 总结
    print("\n" + "=" * 80)
    print("📋 TEST SUMMARY")
    print("=" * 80)
    
    if success:
        print("🎉 SUCCESS: Interface correctly returns direct API data!")
        print("✅ The modification is working as expected")
        print("✅ No wrapper fields (success, message, data) detected")
    else:
        print("💥 FAILURE: Interface still returns wrapped data")
        print("❌ The modification may not be working correctly")
        print("❌ Old wrapper format detected")
    
    if args.test_invalid:
        if error_handling_success:
            print("✅ Error handling works correctly (FastAPI standard format)")
        else:
            print("❌ Error handling needs improvement")
    
    if success and error_handling_success:
        print("\n🎊 All tests passed! The interface modification is successful.")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the implementation.")
        sys.exit(1)

if __name__ == "__main__":
    main()
