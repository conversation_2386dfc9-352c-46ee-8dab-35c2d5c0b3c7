#!/usr/bin/env python3
"""
简单测试Apple登录用户名保留逻辑
"""

import sys
import os
import uuid
from datetime import datetime, timezone

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from sqlmodel import Session
from app.core.db import engine
from app.models import User, AuthProviderEnum, PlatformEnum, OAuthUserInfo

def test_update_user_logic():
    """测试用户更新逻辑"""
    print("🧪 测试用户更新逻辑...")
    
    # 导入OAuth服务
    from app.services.oauth_service import OAuthService
    
    with Session(engine) as session:
        # 创建OAuth服务实例
        oauth_service = OAuthService(session)
        
        # 1. 创建一个有真实姓名的现有用户
        existing_user = User(
            id=uuid.uuid4(),
            email="<EMAIL>",
            is_active=True,
            is_superuser=False,
            full_name="张三",  # 真实姓名
            platform=PlatformEnum.ios,
            auth_provider=AuthProviderEnum.apple,
            provider_user_id="test.existing.123",
            created_at=datetime.now(timezone.utc),
            hashed_password=None
        )
        
        session.add(existing_user)
        session.commit()
        session.refresh(existing_user)
        
        print(f"✓ 创建测试用户: {existing_user.email}")
        print(f"  原始姓名: '{existing_user.full_name}'")
        
        # 2. 测试用Apple默认姓名更新用户
        apple_default_user_info = OAuthUserInfo(
            provider=AuthProviderEnum.apple,
            provider_user_id="test.existing.123",
            email="<EMAIL>",
            full_name="Apple User 0123",  # Apple默认生成的姓名
            avatar_url=None
        )
        
        print(f"\n📱 尝试用Apple默认姓名更新用户...")
        print(f"  尝试更新为: '{apple_default_user_info.full_name}'")
        
        # 调用更新方法
        import asyncio
        updated_user = asyncio.run(oauth_service._update_user_from_oauth(
            existing_user, apple_default_user_info, PlatformEnum.ios
        ))
        
        session.commit()
        session.refresh(updated_user)
        
        print(f"  更新后姓名: '{updated_user.full_name}'")
        
        # 验证姓名是否被保留
        if updated_user.full_name == "张三":
            print(f"✅ 测试1通过: 真实姓名被正确保留")
            test1_passed = True
        else:
            print(f"❌ 测试1失败: 真实姓名被覆盖")
            test1_passed = False
        
        # 3. 测试用真实姓名更新用户
        real_name_user_info = OAuthUserInfo(
            provider=AuthProviderEnum.apple,
            provider_user_id="test.existing.123",
            email="<EMAIL>",
            full_name="李四",  # 真实提供的姓名
            avatar_url=None
        )
        
        print(f"\n📱 尝试用真实姓名更新用户...")
        print(f"  尝试更新为: '{real_name_user_info.full_name}'")
        
        updated_user2 = asyncio.run(oauth_service._update_user_from_oauth(
            updated_user, real_name_user_info, PlatformEnum.ios
        ))
        
        session.commit()
        session.refresh(updated_user2)
        
        print(f"  更新后姓名: '{updated_user2.full_name}'")
        
        # 验证真实姓名是否被更新
        if updated_user2.full_name == "李四":
            print(f"✅ 测试2通过: 真实姓名被正确更新")
            test2_passed = True
        else:
            print(f"❌ 测试2失败: 真实姓名未被更新")
            test2_passed = False
        
        # 4. 测试空姓名用户接受默认姓名
        empty_name_user = User(
            id=uuid.uuid4(),
            email="<EMAIL>",
            is_active=True,
            is_superuser=False,
            full_name=None,  # 没有姓名
            platform=PlatformEnum.ios,
            auth_provider=AuthProviderEnum.apple,
            provider_user_id="test.empty.123",
            created_at=datetime.now(timezone.utc),
            hashed_password=None
        )
        
        session.add(empty_name_user)
        session.commit()
        session.refresh(empty_name_user)
        
        print(f"\n📱 测试空姓名用户接受默认姓名...")
        print(f"  原始姓名: {empty_name_user.full_name}")
        
        updated_user3 = asyncio.run(oauth_service._update_user_from_oauth(
            empty_name_user, apple_default_user_info, PlatformEnum.ios
        ))
        
        session.commit()
        session.refresh(updated_user3)
        
        print(f"  更新后姓名: '{updated_user3.full_name}'")
        
        # 验证空姓名用户是否接受了默认姓名
        if updated_user3.full_name == "Apple User 0123":
            print(f"✅ 测试3通过: 空姓名用户接受了默认姓名")
            test3_passed = True
        else:
            print(f"❌ 测试3失败: 空姓名用户未接受默认姓名")
            test3_passed = False
        
        # 清理测试数据
        session.delete(existing_user)
        session.delete(empty_name_user)
        session.commit()
        
        return test1_passed and test2_passed and test3_passed

def main():
    """主函数"""
    print("🔧 Apple登录用户名保留逻辑测试")
    print("=" * 50)
    
    try:
        success = test_update_user_logic()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 所有测试通过!")
            print("✅ Apple登录用户名保留逻辑修复成功")
        else:
            print("💥 部分测试失败!")
            print("❌ 需要进一步检查和修复")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试执行失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
