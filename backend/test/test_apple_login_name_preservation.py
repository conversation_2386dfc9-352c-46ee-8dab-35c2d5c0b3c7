#!/usr/bin/env python3
"""
测试Apple登录时用户名保留逻辑

验证现有用户再次登录时不会被默认生成的姓名覆盖
"""

import sys
import os
import uuid
import jwt
from datetime import datetime, timezone, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from sqlmodel import Session
from app.core.db import engine
from app.models import User, AuthProviderEnum, PlatformEnum
from app.services.oauth_service import OAuthService, OAuthUserInfo, OAuthLoginRequest

def create_test_apple_token(user_id: str = "001031.test123456789abcdef.0506"):
    """创建测试用的 Apple Identity Token"""
    payload = {
        "iss": "https://appleid.apple.com",
        "aud": "com.wenhaofree.bridal-swift",
        "exp": int((datetime.now(timezone.utc) + timedelta(hours=1)).timestamp()),
        "iat": int(datetime.now(timezone.utc).timestamp()),
        "sub": user_id,
        "nonce": "TEST-NONCE-12345",
        "c_hash": "TestHashValue123",
        "email": "<EMAIL>",
        "email_verified": True,
        "is_private_email": True,
        "auth_time": int(datetime.now(timezone.utc).timestamp()),
        "nonce_supported": True
    }
    
    return jwt.encode(payload, "test-secret", algorithm="HS256")

async def test_apple_login_name_preservation():
    """测试Apple登录时用户名保留逻辑"""
    print("🧪 测试Apple登录用户名保留逻辑...")

    with Session(engine) as session:
        # 1. 创建一个已有真实姓名的用户
        existing_user = User(
            id=uuid.uuid4(),
            email="<EMAIL>",
            is_active=True,
            is_superuser=False,
            full_name="张三",  # 真实的中文姓名
            platform=PlatformEnum.ios,
            auth_provider=AuthProviderEnum.apple,
            provider_user_id="001031.existing123456789.0506",
            created_at=datetime.now(timezone.utc),
            hashed_password=None
        )
        
        session.add(existing_user)
        session.commit()
        session.refresh(existing_user)
        
        print(f"✓ 创建测试用户: {existing_user.email}")
        print(f"  原始姓名: '{existing_user.full_name}'")
        print(f"  用户ID: {existing_user.id}")
        
        # 2. 模拟该用户再次Apple登录（没有提供用户信息）
        oauth_service = OAuthService(session)
        
        # 创建OAuth请求
        oauth_request = OAuthLoginRequest(
            provider=AuthProviderEnum.apple,
            access_token=create_test_apple_token(existing_user.provider_user_id),
            platform=PlatformEnum.ios
        )
        
        print(f"\n📱 模拟用户再次Apple登录...")
        
        # 执行登录
        try:
            result = await oauth_service.oauth_login(oauth_request, None, None)
            
            print(f"✅ 登录成功!")
            print(f"  用户ID: {result.user.id}")
            print(f"  邮箱: {result.user.email}")
            print(f"  登录后姓名: '{result.user.full_name}'")
            print(f"  是否新用户: {result.is_new_user}")
            
            # 验证姓名是否被保留
            if result.user.full_name == "张三":
                print(f"✅ 姓名保留测试通过: 原始姓名 '{existing_user.full_name}' 被正确保留")
                return True
            else:
                print(f"❌ 姓名保留测试失败: 期望 '张三', 实际 '{result.user.full_name}'")
                return False
                
        except Exception as e:
            print(f"❌ 登录失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

async def test_apple_login_new_user_with_default_name():
    """测试新用户Apple登录时使用默认姓名"""
    print("\n🧪 测试新用户Apple登录默认姓名...")

    with Session(engine) as session:
        oauth_service = OAuthService(session)
        
        # 创建新用户的OAuth请求
        new_user_id = "001031.newuser123456789.0506"
        oauth_request = OAuthLoginRequest(
            provider=AuthProviderEnum.apple,
            access_token=create_test_apple_token(new_user_id),
            platform=PlatformEnum.ios
        )
        
        print(f"📱 模拟新用户Apple登录...")
        
        try:
            result = await oauth_service.oauth_login(oauth_request, None, None)
            
            print(f"✅ 新用户登录成功!")
            print(f"  用户ID: {result.user.id}")
            print(f"  邮箱: {result.user.email}")
            print(f"  生成的姓名: '{result.user.full_name}'")
            print(f"  是否新用户: {result.is_new_user}")
            
            # 验证新用户是否获得了默认姓名
            if result.user.full_name and result.user.full_name.startswith("Apple User"):
                print(f"✅ 新用户默认姓名测试通过: '{result.user.full_name}'")
                return True
            else:
                print(f"❌ 新用户默认姓名测试失败: '{result.user.full_name}'")
                return False
                
        except Exception as e:
            print(f"❌ 新用户登录失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

async def test_apple_login_with_real_name():
    """测试Apple登录时提供真实姓名"""
    print("\n🧪 测试Apple登录提供真实姓名...")

    with Session(engine) as session:
        # 创建一个有默认姓名的用户
        user_with_default_name = User(
            id=uuid.uuid4(),
            email="<EMAIL>",
            is_active=True,
            is_superuser=False,
            full_name="Apple User 1234",  # 默认生成的姓名
            platform=PlatformEnum.ios,
            auth_provider=AuthProviderEnum.apple,
            provider_user_id="001031.defaultname123456.0506",
            created_at=datetime.now(timezone.utc),
            hashed_password=None
        )
        
        session.add(user_with_default_name)
        session.commit()
        session.refresh(user_with_default_name)
        
        print(f"✓ 创建有默认姓名的用户: {user_with_default_name.email}")
        print(f"  原始姓名: '{user_with_default_name.full_name}'")
        
        oauth_service = OAuthService(session)
        
        # 模拟用户提供真实姓名信息
        user_info = {
            "firstName": "李",
            "lastName": "四"
        }
        
        oauth_request = OAuthLoginRequest(
            provider=AuthProviderEnum.apple,
            access_token=create_test_apple_token(user_with_default_name.provider_user_id),
            platform=PlatformEnum.ios
        )
        
        print(f"📱 模拟用户提供真实姓名登录...")
        
        try:
            result = await oauth_service.oauth_login(oauth_request, user_info, None)
            
            print(f"✅ 提供真实姓名登录成功!")
            print(f"  登录后姓名: '{result.user.full_name}'")
            
            # 验证真实姓名是否被更新
            if result.user.full_name == "李 四":
                print(f"✅ 真实姓名更新测试通过: 从 '{user_with_default_name.full_name}' 更新为 '{result.user.full_name}'")
                return True
            else:
                print(f"❌ 真实姓名更新测试失败: 期望 '李 四', 实际 '{result.user.full_name}'")
                return False
                
        except Exception as e:
            print(f"❌ 提供真实姓名登录失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

async def main():
    """主测试函数"""
    print("🔧 Apple登录用户名保留逻辑测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 现有用户姓名保留
    test_results.append(await test_apple_login_name_preservation())
    
    # 测试2: 新用户默认姓名
    test_results.append(await test_apple_login_new_user_with_default_name())
    
    # 测试3: 提供真实姓名更新
    test_results.append(await test_apple_login_with_real_name())
    
    print("\n" + "=" * 60)
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"🎉 所有测试通过! ({passed}/{total})")
        print("✅ Apple登录用户名保留逻辑修复成功")
    else:
        print(f"💥 部分测试失败! ({passed}/{total})")
        print("❌ 需要进一步检查和修复")
    
    return passed == total

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
