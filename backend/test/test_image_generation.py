#!/usr/bin/env python3
"""
图片生成服务测试脚本

使用方法:
1. 确保在.env文件中配置了正确的API token
2. 运行: python test_image_generation.py
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from backend.app.services.generation_image_service import (
    ImageGenerationRequest,
    ImageGenerationService
)


async def test_image_generation():
    """测试图片生成功能"""
    print("=== 图片生成服务测试 ===\n")

    # 创建服务实例
    service = ImageGenerationService()

    # 检查服务配置
    print("1. 检查服务配置:")
    print(f"   API URL: {service.api_url}")
    print(f"   API Token配置: {'✓' if service.api_token else '✗'}")
    print()

    if not service.api_token:
        print("❌ 错误: API Token未配置")
        print("请在.env文件中设置 IMAGE_GENERATION_API_TOKEN")
        return
    
    # 创建测试请求
    print("2. 创建测试请求:")
    request = ImageGenerationRequest(
        prompt="A beautiful sunset over the mountains with vibrant colors",
        size="1:1",
        is_enhance=False,
        n_variants=1,
        enable_fallback=True,
        fallback_model="FLUX_MAX"
    )
    print(f"   提示词: {request.prompt}")
    print(f"   尺寸: {request.size}")
    print(f"   变体数量: {request.n_variants}")
    print()
    
    # 测试异步调用
    print("3. 测试异步图片生成:")
    try:
        result = await service.generate_image(request)

        print(f"   成功: {'✓' if result.success else '✗'}")
        print(f"   消息: {result.message}")

        if result.success and result.data:
            print("   响应数据:")
            for key, value in result.data.items():
                print(f"     {key}: {value}")

        if result.error:
            print(f"   错误: {result.error}")

    except Exception as e:
        print(f"   异常: {str(e)}")

    print()

    # 测试同步调用
    print("4. 测试同步图片生成:")
    try:
        result = service.generate_image_sync(request)

        print(f"   成功: {'✓' if result.success else '✗'}")
        print(f"   消息: {result.message}")

        if result.success and result.data:
            print("   响应数据:")
            for key, value in result.data.items():
                print(f"     {key}: {value}")

        if result.error:
            print(f"   错误: {result.error}")

    except Exception as e:
        print(f"   异常: {str(e)}")


def test_with_files():
    """测试带文件URL的图片生成"""
    print("\n=== 带文件URL的图片生成测试 ===\n")
    
    request = ImageGenerationRequest(
        files_url=[
            "https://example.com/image1.png",
            "https://example.com/image2.png"
        ],
        prompt="Combine these images into a beautiful collage",
        size="16:9",
        callback_url="https://your-callback-url.com/callback",
        is_enhance=True,
        n_variants=2
    )
    
    print("测试请求配置:")
    print(f"   文件URLs: {request.files_url}")
    print(f"   提示词: {request.prompt}")
    print(f"   尺寸: {request.size}")
    print(f"   回调URL: {request.callback_url}")
    print(f"   增强: {request.is_enhance}")
    print(f"   变体数量: {request.n_variants}")
    print()
    
    try:
        service = ImageGenerationService()
        result = service.generate_image_sync(request)

        print(f"成功: {'✓' if result.success else '✗'}")
        print(f"消息: {result.message}")

        if result.success and result.data:
            print("响应数据:")
            for key, value in result.data.items():
                print(f"  {key}: {value}")

        if result.error:
            print(f"错误: {result.error}")

    except Exception as e:
        print(f"异常: {str(e)}")


if __name__ == "__main__":
    print("开始测试图片生成服务...\n")
    
    # 运行异步测试
    asyncio.run(test_image_generation())
    
    # 运行文件URL测试
    test_with_files()
    
    print("\n测试完成!")
