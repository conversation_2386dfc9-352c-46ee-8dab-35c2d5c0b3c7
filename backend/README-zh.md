# FastAPI 后端项目 - 中文文档

## 📋 项目概述

这是一个基于 FastAPI 的现代化后端项目，采用异步架构设计，提供完整的用户管理、身份认证和 CRUD 操作功能。项目遵循 FastAPI 最佳实践和现代 Python 开发标准。

**🆕 新增功能：订阅型生图 APP 后端系统**
- 完整的订阅管理系统（Apple/Google/Stripe 支付集成）
- 积分系统和使用配额管理
- 多平台支持（iOS/Android/Web）
- 实时使用统计和分析
- 管理员后台和用户行为追踪

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd bridal-fastapi/backend

# 安装依赖
uv sync

# 激活虚拟环境
source .venv/bin/activate
```

### 2. 数据库初始化
```bash
# 初始化数据库和演示数据
python -c "from app.initial_data import main; main()"
```

### 3. 启动服务
```bash
# 启动开发服务器
fastapi run --reload app/main.py
```

### 4. 测试 API
访问 `http://localhost:8000/docs` 查看 API 文档，或使用演示账户：

- **订阅用户**: <EMAIL> / demopassword123
- **积分用户**: <EMAIL> / creditspassword123
- **管理员**: <EMAIL> / changethis

## 🏗️ 项目架构

### 目录结构
```
backend/
├── app/
│   ├── api/                     # API 路由层
│   │   ├── main.py              # API 路由配置
│   │   ├── deps.py              # 依赖注入（认证、数据库）
│   │   └── routes/              # 具体路由实现
│   │       ├── login.py         # 身份认证端点
│   │       ├── users.py         # 用户管理端点
│   │       ├── items.py         # 项目 CRUD 端点
│   │       ├── utils.py         # 工具端点
│   │       ├── subscriptions.py # 🆕 订阅管理端点
│   │       ├── usage.py         # 🆕 使用记录和配额管理端点
│   │       ├── credits.py       # 🆕 积分系统端点
│   │       ├── admin.py         # 🆕 管理员后台端点
│   │       └── private.py       # 开发环境专用端点
│   ├── services/                # 🆕 业务逻辑层
│   │   ├── billing.py           # 计费和订阅验证逻辑
│   │   ├── apple_validator.py   # Apple Store 订阅验证
│   │   ├── google_validator.py  # Google Play 订阅验证
│   │   └── usage_tracker.py     # 使用次数跟踪服务
│   ├── core/                    # 核心功能模块
│   │   ├── config.py            # 应用配置
│   │   ├── db.py                # 数据库设置和初始化
│   │   └── security.py          # JWT 和密码安全
│   ├── alembic/                 # 数据库迁移
│   ├── email-templates/         # 邮件模板系统
│   ├── tests/                   # 测试套件
│   ├── main.py                  # FastAPI 应用入口点
│   ├── models.py                # SQLModel 数据模型
│   ├── crud.py                  # 数据库操作
│   └── utils.py                 # 邮件和令牌工具
├── scripts/                     # 开发脚本
├── pyproject.toml              # Python 依赖和配置
└── Dockerfile                  # 容器配置
```

## 🔧 技术栈与依赖

### 核心框架栈
- **FastAPI** `>=0.114.2` - 现代异步 Web 框架
- **SQLModel** `>=0.0.21` - 类型安全的 ORM（SQLAlchemy + Pydantic）
- **PostgreSQL** + **psycopg** `>=3.1.13` - 数据库
- **Alembic** `>=1.12.1` - 数据库迁移
- **Pydantic** `>2.0` - 数据验证和设置

### 身份认证与安全
- **PyJWT** `>=2.8.0` - JWT 令牌处理
- **Passlib[bcrypt]** `>=1.7.4` - 密码哈希
- **OAuth2PasswordBearer** - 基于令牌的身份认证

### 附加功能
- **Sentry SDK** `>=1.40.6` - 错误监控
- **Emails** `>=0.6` + **Jinja2** `>=3.1.4` - 邮件系统
- **Tenacity** `>=8.2.3` - 重试机制
- **UV** - 快速 Python 包管理器

## 🛡️ API 端点结构

### 身份认证路由 (`/login`)
- `POST /login/access-token` - OAuth2 令牌登录
- `POST /login/test-token` - 验证访问令牌
- `POST /password-recovery/{email}` - 密码恢复
- `POST /reset-password/` - 使用令牌重置密码

### 用户管理路由 (`/users`)
- `GET /users/` - 列出用户（仅超级用户）
- `POST /users/` - 创建用户（仅超级用户）
- `GET /users/me` - 获取当前用户资料
- `PUT /users/me` - 更新当前用户
- `PUT /users/me/password` - 更改密码
- `DELETE /users/me` - 删除自己的账户
- `POST /users/signup` - 公开用户注册
- `GET /users/{user_id}` - 根据 ID 获取用户（仅超级用户）
- `PUT /users/{user_id}` - 更新用户（仅超级用户）
- `DELETE /users/{user_id}` - 删除用户（仅超级用户）

### 项目管理路由 (`/items`)
- `GET /items/` - 列出项目（按所有权过滤）
- `GET /items/{id}` - 根据 ID 获取项目
- `POST /items/` - 创建新项目
- `PUT /items/{id}` - 更新项目
- `DELETE /items/{id}` - 删除项目

### 工具路由 (`/utils`)
- `POST /utils/test-email/` - 发送测试邮件（仅超级用户）
- `GET /utils/health-check/` - 健康检查端点

### 私有路由 (`/private`) - 仅本地开发
- `POST /private/users/` - 无验证创建用户

## 🆕 订阅系统 API 端点

### 订阅管理路由 (`/subscriptions`)
- `GET /subscriptions/status` - 获取用户订阅状态
- `GET /subscriptions/check-access` - 检查服务访问权限
- `GET /subscriptions/` - 获取用户订阅历史
- `POST /subscriptions/apple/validate` - 验证 Apple App Store 收据
- `POST /subscriptions/google/validate` - 验证 Google Play 订阅
- `POST /subscriptions/sync` - 同步订阅状态
- `GET /subscriptions/{subscription_id}` - 获取特定订阅详情
- `PUT /subscriptions/{subscription_id}` - 更新订阅信息（管理员）
- **🆕 `POST /subscriptions/create`** - 创建新订阅
- **🆕 `POST /subscriptions/upgrade`** - 升级用户订阅状态
- **🆕 `POST /subscriptions/upgrade-to-monthly`** - 快速升级到月度订阅
- **🆕 `POST /subscriptions/upgrade-to-yearly`** - 快速升级到年度订阅

### 使用记录路由 (`/usage`)
- `POST /usage/consume` - 消耗使用配额或积分
- `GET /usage/history` - 获取使用历史记录
- `GET /usage/stats` - 获取使用统计信息
- `GET /usage/current-month` - 获取当月使用情况
- `GET /usage/limits` - 获取使用限制和剩余配额
- `POST /usage/batch-consume` - 批量消耗使用配额
- `GET /usage/{usage_id}` - 获取特定使用记录
- `DELETE /usage/{usage_id}` - 删除使用记录（管理员）

### 积分系统路由 (`/credits`)
- `GET /credits/balance` - 获取积分余额
- `GET /credits/packages` - 获取积分包历史
- `POST /credits/purchase` - 记录积分包购买
- `POST /credits/apple/validate` - 验证 Apple 积分购买
- `POST /credits/google/validate` - 验证 Google Play 积分购买
- `GET /credits/pricing` - 获取积分包定价信息
- `GET /credits/{package_id}` - 获取特定积分包详情
- `DELETE /credits/{package_id}` - 删除积分包（管理员）

### 管理员后台路由 (`/admin`) - 仅超级用户
- `GET /admin/dashboard` - 管理员仪表板统计
- `GET /admin/users/{user_id}/status` - 用户详细状态（管理员视图）
- `GET /admin/analytics/usage` - 系统使用分析
- `GET /admin/analytics/revenue` - 收入分析
- `POST /admin/users/{user_id}/credits` - 为用户授予积分
- `PUT /admin/users/{user_id}/subscription/{subscription_id}` - 更新用户订阅

### 🆕 第三方登录路由 (`/oauth`)
- `POST /oauth/login` - 通用第三方登录端点
- `GET /oauth/providers` - 获取支持的登录平台列表
- `POST /oauth/apple/login` - Apple Sign In 专用端点
- `POST /oauth/google/login` - Google OAuth 专用端点
- `POST /oauth/wechat/login` - 微信登录专用端点
- `POST /oauth/unlink/{provider}` - 解除第三方平台绑定

## 🗄️ 数据模型

### 用户模型 (User) - 已扩展
- UUID 主键
- 邮箱（唯一，索引）
- 哈希密码（第三方登录用户可为空）
- 全名（可选）
- 活跃状态
- 超级用户标志
- **🆕 平台信息**（iOS/Android/Web）
- **🆕 创建时间和最后登录时间**
- **🆕 第三方登录支持**：
  - 认证提供商（email/apple/google/wechat/facebook/github）
  - 第三方平台用户ID
  - 用户头像URL
- 与项目的一对多关系
- **🆕 与订阅、使用记录、积分包的关系**

### 项目模型 (Item)
- UUID 主键
- 标题和描述
- 所有者关系（用户外键）
- 与用户级联删除

## 🆕 订阅系统数据模型

### 订阅模型 (Subscription)
- UUID 主键
- 产品 ID（如 sub_monthly_40）
- 平台（Apple/Google/Stripe）
- 订阅起始和结束时间
- 活跃状态
- 原始交易 ID（平台返回）
- 最后验证时间
- 用户关联（外键）

### 使用记录模型 (UsageRecord)
- UUID 主键
- 使用时间
- 使用类型（图片生成/HD导出/高级滤镜）
- 使用次数
- 用户关联（外键）

### 积分包模型 (CreditPackage)
- UUID 主键
- 购买积分数量
- 剩余积分数量
- 购买时间
- 产品 ID（如 credits_pack_50）
- 购买平台
- 用户关联（外键）

## 🔐 安全特性

- **基于 JWT 的身份认证**，可配置过期时间（默认 8 天）
- **使用 bcrypt 的密码哈希**
- **基于角色的访问控制**（普通用户 vs 超级用户）
- **前端集成的 CORS 配置**
- **使用 Pydantic 模型的输入验证**
- **通过 SQLModel/SQLAlchemy 防止 SQL 注入**

## 📧 邮件系统

- **使用 Jinja2 的基于模板的邮件**
- **响应式邮件设计的 MJML 支持**
- **支持 TLS/SSL 的 SMTP 配置**
- **邮件类型：**
  - 测试邮件
  - 密码重置
  - 新账户通知

## 🧪 测试基础设施

- **Pytest** 框架与 FastAPI TestClient
- **自动清理的数据库固件**
- **测试受保护端点的身份认证助手**
- **HTML 输出的覆盖率报告**
- **VS Code 集成**用于调试和测试运行

## 🚀 开发特性

- **使用 `fastapi run --reload` 的热重载**
- **带卷挂载的 Docker Compose 设置**
- **使用 Alembic 的数据库迁移**
- **代码质量工具：**
  - **Ruff** 用于代码检查和格式化
  - **MyPy** 用于类型检查
  - **Pre-commit 钩子**
- **VS Code 调试器**配置

## ⚙️ 配置管理

- **基于环境的设置**（本地/测试/生产）
- **类型安全配置的 Pydantic Settings**
- **带验证的数据库 URL 构建**
- **CORS 源管理**
- **邮件服务**配置
- **生产监控的 Sentry 集成**

### 🆕 订阅系统环境变量

在 `.env` 文件中添加以下配置：

```bash
# Apple App Store 配置
APPLE_SHARED_SECRET=your_apple_shared_secret

# Google Play 配置
GOOGLE_PACKAGE_NAME=com.yourapp.package
GOOGLE_SERVICE_ACCOUNT_KEY=path/to/service-account-key.json

# Stripe 配置（可选）
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# 订阅系统配置
SUBSCRIPTION_WEBHOOK_SECRET=your_webhook_secret_for_validation

# 🆕 第三方登录配置
# Apple Sign In
APPLE_CLIENT_ID=com.yourapp.service
APPLE_TEAM_ID=your_apple_team_id
APPLE_KEY_ID=your_apple_key_id
APPLE_PRIVATE_KEY_PATH=path/to/apple-private-key.p8

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret

# 微信登录
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# Facebook Login
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# GitHub OAuth
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
```

### 配置说明

#### 订阅系统配置
- **APPLE_SHARED_SECRET**: Apple App Store 的共享密钥，用于验证收据
- **GOOGLE_PACKAGE_NAME**: Google Play 应用包名
- **GOOGLE_SERVICE_ACCOUNT_KEY**: Google Play 服务账户密钥文件路径
- **STRIPE_SECRET_KEY**: Stripe 支付密钥（如果使用 Stripe）
- **SUBSCRIPTION_WEBHOOK_SECRET**: Webhook 验证密钥

#### 🆕 第三方登录配置
- **Apple Sign In**:
  - `APPLE_CLIENT_ID`: Apple 开发者账户中的 Service ID
  - `APPLE_TEAM_ID`: Apple 开发者团队 ID
  - `APPLE_KEY_ID`: Apple 私钥 ID
  - `APPLE_PRIVATE_KEY_PATH`: Apple 私钥文件路径

- **Google OAuth**:
  - `GOOGLE_CLIENT_ID`: Google Cloud Console 中的客户端 ID
  - `GOOGLE_CLIENT_SECRET`: Google OAuth 客户端密钥

- **微信登录**:
  - `WECHAT_APP_ID`: 微信开放平台应用 ID
  - `WECHAT_APP_SECRET`: 微信开放平台应用密钥

- **Facebook Login**:
  - `FACEBOOK_APP_ID`: Facebook 开发者平台应用 ID
  - `FACEBOOK_APP_SECRET`: Facebook 应用密钥

- **GitHub OAuth**:
  - `GITHUB_CLIENT_ID`: GitHub OAuth App 客户端 ID
  - `GITHUB_CLIENT_SECRET`: GitHub OAuth App 客户端密钥

## 🔄 数据库管理

- **类型安全数据库操作的 SQLModel**
- **模式版本控制的 Alembic 迁移**
- **使用 SQLAlchemy 引擎的连接池**
- **初始化时自动创建超级用户**
- **带重试逻辑的数据库健康检查**

## 📦 依赖版本详情

### 生产依赖
```toml
fastapi[standard] = ">=0.114.2,<1.0.0"
python-multipart = ">=0.0.7,<1.0.0"
email-validator = ">=2.1.0.post1,<*******"
passlib[bcrypt] = ">=1.7.4,<2.0.0"
tenacity = ">=8.2.3,<9.0.0"
pydantic = ">2.0"
emails = ">=0.6,<1.0"
jinja2 = ">=3.1.4,<4.0.0"
alembic = ">=1.12.1,<2.0.0"
httpx = ">=0.25.1,<1.0.0"
psycopg[binary] = ">=3.1.13,<4.0.0"
sqlmodel = ">=0.0.21,<1.0.0"
bcrypt = "==4.3.0"
pydantic-settings = ">=2.2.1,<3.0.0"
sentry-sdk[fastapi] = ">=1.40.6,<2.0.0"
pyjwt = ">=2.8.0,<3.0.0"
```

### 开发依赖
```toml
pytest = ">=7.4.3,<8.0.0"
mypy = ">=1.8.0,<2.0.0"
ruff = ">=0.2.2,<1.0.0"
pre-commit = ">=3.6.2,<4.0.0"
types-passlib = ">=1.7.7.20240106,<*******"
coverage = ">=7.4.3,<8.0.0"
```

## 🆕 订阅系统使用指南

### 订阅产品配置

#### 订阅方案
```python
SUBSCRIPTION_LIMITS = {
    "sub_monthly_40": {"monthly_limit": 40, "credits_per_use": 1},    # 月度40次
    "sub_monthly_60": {"monthly_limit": 60, "credits_per_use": 1},    # 月度60次
    "sub_yearly_480": {"monthly_limit": 40, "credits_per_use": 1},    # 年度480次
    "sub_yearly_720": {"monthly_limit": 60, "credits_per_use": 1},    # 年度720次
}
```

#### 积分包配置
```python
CREDIT_PACKAGES = {
    "credits_pack_10": {"credits": 10, "cost_per_use": 5},   # 10积分包
    "credits_pack_50": {"credits": 50, "cost_per_use": 5},   # 50积分包
    "credits_pack_100": {"credits": 100, "cost_per_use": 5}, # 100积分包
}
```

### API 使用流程

#### 1. 检查用户权限
```bash
# 检查用户是否可以使用服务
curl -X GET "http://localhost:8000/api/v1/subscriptions/check-access" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 响应示例
{
  "can_access": true,
  "remaining_credits": 25,
  "remaining_monthly_usage": 35,
  "subscription_status": "active"
}
```

#### 2. 消耗使用配额
```bash
# 生成图片时消耗配额
curl -X POST "http://localhost:8000/api/v1/usage/consume" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "usage_type": "image_generation",
    "count": 1
  }'

# 响应示例
{
  "success": true,
  "message": "Usage recorded against subscription",
  "remaining_credits": 25,
  "remaining_monthly_usage": 34
}
```

#### 3. 获取用户状态
```bash
# 获取完整的用户订阅状态
curl -X GET "http://localhost:8000/api/v1/subscriptions/status" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 响应示例
{
  "user_id": "123e4567-e89b-12d3-a456-426614174000",
  "has_active_subscription": true,
  "subscription_end_date": "2024-02-15T10:30:00Z",
  "subscription_product_id": "sub_monthly_40",
  "total_credits": 25,
  "monthly_usage_count": 6,
  "monthly_limit": 40,
  "can_use_service": true
}
```

### 🆕 用户订阅升级

#### 1. 通用订阅升级接口
```bash
# 升级用户到指定订阅计划
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "product_id": "sub_monthly_40",
    "duration_days": 30,
    "platform": "stripe"
  }'

# 响应示例
{
  "subscription": {
    "id": "sub_123e4567-e89b-12d3-a456-426614174000",
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "product_id": "sub_monthly_40",
    "platform": "stripe",
    "start_date": "2025-01-29T10:00:00Z",
    "end_date": "2025-02-28T10:00:00Z",
    "is_active": true
  },
  "message": "New subscription created successfully",
  "is_new_subscription": true
}
```

#### 2. 快速升级到月度订阅
```bash
# 升级到月度订阅（40张图片/月）
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-monthly?user_id=123e4567-e89b-12d3-a456-426614174000&images_per_month=40" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 升级到月度订阅（60张图片/月）
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-monthly?user_id=123e4567-e89b-12d3-a456-426614174000&images_per_month=60" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

#### 3. 快速升级到年度订阅
```bash
# 升级到年度订阅（40张图片/月）
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-yearly?user_id=123e4567-e89b-12d3-a456-426614174000&images_per_month=40" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"

# 升级到年度订阅（60张图片/月）
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-yearly?user_id=123e4567-e89b-12d3-a456-426614174000&images_per_month=60" \
  -H "Authorization: Bearer YOUR_ADMIN_TOKEN"
```

#### 4. 订阅产品ID说明

| 产品ID | 描述 | 时长 | 月度限制 | 适用场景 |
|--------|------|------|----------|----------|
| `sub_monthly_40` | 月度基础版 | 30天 | 40张图片 | 轻度用户 |
| `sub_monthly_60` | 月度高级版 | 30天 | 60张图片 | 中度用户 |
| `sub_yearly_480` | 年度基础版 | 365天 | 40张图片/月 | 长期轻度用户 |
| `sub_yearly_720` | 年度高级版 | 365天 | 60张图片/月 | 长期重度用户 |

#### 5. 权限说明
- **管理员用户**：可以升级任何用户的订阅状态
- **普通用户**：只能升级自己的订阅状态
- **升级逻辑**：
  - 如果用户没有订阅：创建新订阅
  - 如果用户有活跃订阅：延长现有订阅时间
  - 如果用户有过期订阅：重新激活并延长时间

### 平台集成

#### Apple App Store 验证
```bash
curl -X POST "http://localhost:8000/api/v1/subscriptions/apple/validate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "receipt_data": "base64_encoded_receipt_data"
  }'
```

#### Google Play 验证
```bash
curl -X POST "http://localhost:8000/api/v1/subscriptions/google/validate" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "subscription_id": "your_product_id",
    "purchase_token": "purchase_token_from_google"
  }'
```

### 🆕 第三方登录集成

#### 支持的登录平台

| 平台 | 状态 | 支持设备 | 说明 |
|------|------|----------|------|
| Apple Sign In | 🔄 待实现 | iOS, Web | 使用 Apple ID 登录 |
| Google OAuth | ✅ 已实现 | iOS, Android, Web | 使用 Google 账户登录 |
| 微信登录 | 🔄 待实现 | iOS, Android, Web | 使用微信账户登录 |
| Facebook Login | 🔄 待实现 | iOS, Android, Web | 使用 Facebook 账户登录 |
| GitHub OAuth | 🔄 待实现 | Web | 使用 GitHub 账户登录 |

#### 第三方登录流程

```bash
# 1. 获取支持的登录平台
curl -X GET "http://localhost:8000/api/v1/oauth/providers"

# 2. 🍎 Apple Sign In 登录（推荐 - 已完全实现）
curl -X POST "http://localhost:8000/api/v1/oauth/apple/login" \
  -H "Content-Type: application/json" \
  -d '{
    "identity_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.G7e6kK8N2bXvTDYfV7UDFlLN0HE1ekLvUrthLbxZpKk",
    "platform": "ios"
  }'

# 3. 🔍 Google OAuth 登录（需要真实令牌）
curl -X POST "http://localhost:8000/api/v1/oauth/google/login" \
  -H "Content-Type: application/json" \
  -d '{
    "access_token": "ya29.real_google_access_token_here",
    "platform": "web"
  }'

# 4. 通用 OAuth 登录端点
curl -X POST "http://localhost:8000/api/v1/oauth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "provider": "apple",
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.G7e6kK8N2bXvTDYfV7UDFlLN0HE1ekLvUrthLbxZpKk",
    "platform": "ios"
  }'

# ✅ 成功响应示例
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************.abc123...",
  "token_type": "bearer",
  "user": {
    "id": "51a8a41b-070e-45c7-8188-008c768261a7",
    "email": "<EMAIL>",
    "full_name": "Test User",
    "is_active": true,
    "is_superuser": false,
    "platform": "ios",
    "auth_provider": "apple",
    "provider_user_id": "001234.567890abcdef12345678901234567890abcdef",
    "avatar_url": null,
    "created_at": "2025-01-29T16:47:07.123456Z",
    "last_login": "2025-01-29T16:47:07.123456Z"
  },
  "is_new_user": true
}
```

#### 第三方登录用户特点

1. **无密码存储**：第三方登录用户的 `hashed_password` 字段为 `null`
2. **平台标识**：通过 `auth_provider` 和 `provider_user_id` 识别用户
3. **自动注册**：首次登录自动创建账户
4. **信息同步**：从第三方平台获取用户基本信息和头像

#### 密码策略建议

对于第三方登录用户，系统采用以下策略：

- **无密码存储**：不存储用户密码，使用平台唯一标识符
- **JWT 令牌**：登录成功后返回系统自己的 JWT 令牌
- **账户绑定**：相同邮箱的账户会自动绑定第三方登录方式
- **安全验证**：通过验证第三方平台的 access_token 确保安全性

### 演示账户

系统提供了以下演示账户用于测试：

1. **订阅用户**
   - 邮箱：`<EMAIL>`
   - 密码：`demopassword123`
   - 状态：活跃月度订阅（40次/月）

2. **积分用户**
   - 邮箱：`<EMAIL>`
   - 密码：`creditspassword123`
   - 状态：35积分剩余

3. **过期用户**
   - 邮箱：`<EMAIL>`
   - 密码：`expiredpassword123`
   - 状态：订阅已过期

4. **管理员**
   - 邮箱：`<EMAIL>`
   - 密码：`changethis`
   - 权限：超级用户，可访问所有管理功能

## 🆕 用户订阅升级完整示例

### 步骤1：获取管理员令牌
```bash
# 获取管理员访问令牌
curl -X POST "http://localhost:8000/api/v1/login/access-token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=changethis"

# 保存返回的 access_token
# 响应示例：{"access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...", "token_type": "bearer"}
```

### 步骤2：查看用户当前状态
```bash
# 获取用户令牌（以 demo 用户为例）
curl -X POST "http://localhost:8000/api/v1/login/access-token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=demopassword123"

# 查看用户订阅状态
curl -X GET "http://localhost:8000/api/v1/subscriptions/status" \
  -H "Authorization: Bearer USER_TOKEN"
```

### 步骤3：升级用户订阅

#### 方法1：快速升级到月度订阅（推荐）
```bash
# 升级到月度基础版（40张图片/月）
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-monthly?user_id=16967c94-42fe-4036-8a1a-16936bdc2687&images_per_month=40" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# 升级到月度高级版（60张图片/月）
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-monthly?user_id=16967c94-42fe-4036-8a1a-16936bdc2687&images_per_month=60" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

#### 方法2：快速升级到年度订阅
```bash
# 升级到年度基础版（40张图片/月，12个月）
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-yearly?user_id=16967c94-42fe-4036-8a1a-16936bdc2687&images_per_month=40" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# 升级到年度高级版（60张图片/月，12个月）
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade-to-yearly?user_id=16967c94-42fe-4036-8a1a-16936bdc2687&images_per_month=60" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

#### 方法3：自定义订阅升级
```bash
# 自定义订阅时长和产品
curl -X POST "http://localhost:8000/api/v1/subscriptions/upgrade" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": "16967c94-42fe-4036-8a1a-16936bdc2687",
    "product_id": "sub_monthly_40",
    "duration_days": 90,
    "platform": "stripe"
  }'
```

### 步骤4：验证升级结果
```bash
# 检查用户订阅状态
curl -X GET "http://localhost:8000/api/v1/subscriptions/status" \
  -H "Authorization: Bearer USER_TOKEN"

# 预期响应（升级后）
{
  "user_id": "16967c94-42fe-4036-8a1a-16936bdc2687",
  "has_active_subscription": true,
  "subscription_end_date": "2025-02-28T10:00:00Z",
  "subscription_product_id": "sub_monthly_40",
  "total_credits": 0,
  "monthly_usage_count": 0,
  "monthly_limit": 40,
  "can_use_service": true
}

# 测试服务访问权限
curl -X GET "http://localhost:8000/api/v1/subscriptions/check-access" \
  -H "Authorization: Bearer USER_TOKEN"

# 预期响应
{
  "can_access": true,
  "remaining_credits": 0,
  "remaining_monthly_usage": 40,
  "subscription_status": "active"
}
```

### 步骤5：测试图片生成功能
```bash
# 消耗一次使用配额
curl -X POST "http://localhost:8000/api/v1/usage/consume" \
  -H "Authorization: Bearer USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "usage_type": "image_generation",
    "count": 1
  }'

# 成功响应
{
  "success": true,
  "message": "Usage recorded against subscription",
  "remaining_credits": 0,
  "remaining_monthly_usage": 39
}
```

## 🛠️ 开发工作流

### 环境设置
```bash
# 安装依赖
uv sync

# 激活虚拟环境
source .venv/bin/activate

# 初始化数据库和演示数据
python -c "from app.initial_data import main; main()"

# 运行开发服务器
fastapi run --reload app/main.py
```

### 数据库迁移
```bash
# 创建迁移
alembic revision --autogenerate -m "描述更改"

# 应用迁移
alembic upgrade head
```

### 测试
```bash
# 运行测试
bash ./scripts/test.sh

# 在 Docker 中运行测试
docker compose exec backend bash scripts/tests-start.sh
```

### 代码质量
```bash
# 格式化代码
bash ./scripts/format.sh

# 代码检查
bash ./scripts/lint.sh
```

## 🔧 核心功能模块详解

### 身份认证系统 (app/core/security.py)
- **JWT 令牌生成**：使用 HS256 算法，支持自定义过期时间
- **密码安全**：使用 bcrypt 进行密码哈希和验证
- **令牌验证**：自动验证令牌有效性和用户状态

### 配置管理 (app/core/config.py)
- **环境变量支持**：从 `.env` 文件读取配置
- **类型安全**：使用 Pydantic 进行配置验证
- **多环境支持**：本地、测试、生产环境配置
- **数据库连接**：自动构建 PostgreSQL 连接字符串

### 数据库层 (app/core/db.py)
- **连接管理**：使用 SQLAlchemy 引擎管理数据库连接
- **初始化脚本**：自动创建超级用户账户
- **会话管理**：提供数据库会话的依赖注入

### API 依赖 (app/api/deps.py)
- **身份认证依赖**：OAuth2 令牌验证
- **数据库会话依赖**：自动管理数据库会话生命周期
- **用户权限检查**：区分普通用户和超级用户权限

## 📊 数据模型详解

### 用户相关模型
```python
# 基础用户模型
class UserBase(SQLModel):
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_active: bool = True
    is_superuser: bool = False
    full_name: str | None = Field(default=None, max_length=255)

# 数据库用户模型
class User(UserBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    hashed_password: str
    items: list["Item"] = Relationship(back_populates="owner", cascade_delete=True)
```

### 项目相关模型
```python
# 基础项目模型
class ItemBase(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=255)

# 数据库项目模型
class Item(ItemBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    owner_id: uuid.UUID = Field(foreign_key="user.id", nullable=False, ondelete="CASCADE")
    owner: User | None = Relationship(back_populates="items")
```

## 🧪 测试架构

### 测试配置 (app/tests/conftest.py)
- **数据库固件**：为每个测试会话创建独立的数据库环境
- **认证助手**：提供测试用的令牌生成功能
- **自动清理**：测试完成后自动清理测试数据

### 测试覆盖范围
- **CRUD 操作测试**：用户和项目的增删改查
- **API 端点测试**：所有路由的功能测试
- **身份认证测试**：登录、注册、权限验证
- **工具函数测试**：邮件发送、令牌生成等

## 📧 邮件模板系统

### 模板结构
```
email-templates/
├── src/                    # MJML 源文件
│   ├── new_account.mjml    # 新账户通知
│   ├── reset_password.mjml # 密码重置
│   └── test_email.mjml     # 测试邮件
└── build/                  # 编译后的 HTML 文件
    ├── new_account.html
    ├── reset_password.html
    └── test_email.html
```

### 邮件功能
- **模板渲染**：使用 Jinja2 进行动态内容渲染
- **SMTP 支持**：支持 TLS/SSL 加密的 SMTP 服务器
- **邮件类型**：测试邮件、密码重置、新账户通知

## 🐳 Docker 部署

### Dockerfile 特性
- **多阶段构建**：优化镜像大小
- **UV 包管理器**：快速依赖安装
- **非 root 用户**：提高安全性
- **健康检查**：内置应用健康检查

### Docker Compose 集成
- **开发环境**：支持热重载和卷挂载
- **数据库服务**：集成 PostgreSQL 容器
- **网络配置**：自动配置服务间通信

## 🔍 监控与日志

### Sentry 集成
- **错误追踪**：自动捕获和报告应用错误
- **性能监控**：追踪 API 响应时间和性能指标
- **环境隔离**：区分开发、测试、生产环境的错误报告

### 日志系统
- **结构化日志**：使用 Python logging 模块
- **日志级别**：支持不同级别的日志输出
- **请求追踪**：记录 API 请求和响应信息

## 🆕 管理员功能

### 系统仪表板
```bash
# 获取系统概览统计
curl -X GET "http://localhost:8000/api/v1/admin/dashboard" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# 响应包含：
# - 用户统计（总数、活跃用户、平台分布）
# - 订阅统计（活跃订阅数、预估月收入）
# - 使用统计（30天总使用量、平均使用量）
```

### 用户管理
```bash
# 查看用户详细状态
curl -X GET "http://localhost:8000/api/v1/admin/users/{user_id}/status" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# 为用户授予积分
curl -X POST "http://localhost:8000/api/v1/admin/users/{user_id}/credits" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "credits": 50,
    "reason": "Customer support compensation"
  }'

# 更新用户订阅
curl -X PUT "http://localhost:8000/api/v1/admin/users/{user_id}/subscription/{sub_id}" \
  -H "Authorization: Bearer ADMIN_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "end_date": "2024-03-15T10:30:00Z",
    "is_active": true
  }'
```

### 分析报告
```bash
# 使用分析
curl -X GET "http://localhost:8000/api/v1/admin/analytics/usage?days=30" \
  -H "Authorization: Bearer ADMIN_TOKEN"

# 收入分析
curl -X GET "http://localhost:8000/api/v1/admin/analytics/revenue?days=30" \
  -H "Authorization: Bearer ADMIN_TOKEN"
```

## 🔧 故障排除

### 常见问题

1. **订阅验证失败**
   - 检查 Apple/Google 配置是否正确
   - 确认网络连接和 API 密钥有效性
   - 查看日志中的详细错误信息

2. **积分扣除异常**
   - 检查用户积分余额是否充足
   - 确认积分包配置正确
   - 验证数据库事务完整性

3. **使用配额计算错误**
   - 检查月度使用统计查询逻辑
   - 确认时区设置正确
   - 验证订阅产品配置

### 调试技巧

```bash
# 查看用户完整状态
python -c "
from app.core.db import engine
from app.services.billing import BillingService
from sqlmodel import Session
with Session(engine) as session:
    billing = BillingService(session)
    status = billing.get_user_subscription_status('user_uuid_here')
    print(status)
"

# 手动触发订阅同步
curl -X POST "http://localhost:8000/api/v1/subscriptions/sync" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📈 性能优化建议

1. **数据库索引**：已为关键字段创建索引
2. **缓存策略**：考虑使用 Redis 缓存订阅状态
3. **批量操作**：使用批量 API 减少请求次数
4. **异步处理**：订阅验证可以异步处理
5. **监控告警**：设置关键指标的监控告警

## 🆕 第三方登录实现总结

### 核心设计理念

1. **无密码存储**：第三方登录用户不存储密码，使用平台唯一标识符
2. **统一令牌系统**：所有登录方式最终都返回系统自己的 JWT 令牌
3. **自动账户绑定**：相同邮箱的账户会自动绑定第三方登录方式
4. **平台兼容性**：支持多平台（iOS/Android/Web）的不同登录方式

### 数据库设计

```sql
-- 用户表扩展字段
ALTER TABLE "user" ADD COLUMN auth_provider authproviderenum DEFAULT 'email' NOT NULL;
ALTER TABLE "user" ADD COLUMN provider_user_id VARCHAR(255);
ALTER TABLE "user" ADD COLUMN avatar_url VARCHAR(500);
ALTER TABLE "user" ALTER COLUMN hashed_password DROP NOT NULL;  -- 第三方用户可无密码
```

### 安全考虑

- **令牌验证**：每次登录都验证第三方平台的 access_token
- **用户信息同步**：从可信的第三方平台获取用户基本信息
- **账户保护**：防止恶意账户绑定和信息泄露
- **会话管理**：使用我们自己的 JWT 令牌管理用户会话

### 实现状态

| 功能 | 状态 | 说明 |
|------|------|------|
| 数据模型 | ✅ 完成 | 支持所有第三方登录字段 |
| API 端点 | ✅ 完成 | 提供完整的登录接口 |
| Google OAuth | 🔄 部分实现 | 基础验证逻辑已完成 |
| Apple Sign In | ⚠️ 待实现 | 需要配置 Apple 开发者证书 |
| 微信登录 | ⚠️ 待实现 | 需要微信开放平台配置 |
| Facebook Login | ⚠️ 待实现 | 需要 Facebook 开发者配置 |
| GitHub OAuth | ⚠️ 待实现 | 需要 GitHub OAuth App 配置 |

这个后端为现代 Web 应用程序提供了强大的、生产就绪的基础，具有全面的用户管理、身份认证、CRUD 操作、完整的订阅系统和灵活的第三方登录支持，遵循 FastAPI 最佳实践和现代 Python 开发标准。