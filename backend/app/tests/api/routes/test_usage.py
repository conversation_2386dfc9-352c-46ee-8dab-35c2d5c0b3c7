"""
Tests for usage API routes.

This module tests the usage tracking and consumption endpoints.
"""

import uuid
from datetime import datetime, timezone, timedelta

import pytest
from fastapi.testclient import TestClient
from sqlmodel import Session

from app.models import (
    User, Subscription, UsageRecord, CreditPackage,
    SubscriptionPlatformEnum, UsageTypeEnum
)
from app.tests.utils.user import get_user_from_token


class TestUsageRoutes:
    """Test cases for usage API routes."""
    
    def test_consume_usage_with_subscription(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test consuming usage with active subscription."""
        # Get the user from token
        user = get_user_from_token(db, normal_user_token_headers)
        
        # Create active subscription
        now = datetime.now(timezone.utc)
        subscription = Subscription(
            user_id=user.id,
            product_id="sub_monthly_40",
            platform=SubscriptionPlatformEnum.apple,
            start_date=now - timedelta(days=5),
            end_date=now + timedelta(days=25),
            is_active=True,
            original_transaction_id="test_transaction_123"
        )
        db.add(subscription)
        db.commit()
        
        response = client.post(
            "/api/v1/usage/consume",
            headers=normal_user_token_headers,
            json={
                "usage_type": "image_generation",
                "count": 1
            }
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "subscription" in data["message"].lower()
        assert data["remaining_monthly_usage"] == 39
        
        # Verify usage record was created
        usage_records = db.query(UsageRecord).filter(
            UsageRecord.user_id == user.id
        ).all()
        assert len(usage_records) == 1
        assert usage_records[0].usage_type == UsageTypeEnum.image_generation
    
    def test_consume_usage_with_credits(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test consuming usage with credits."""
        # Get the user from token
        user = get_user_from_token(db, normal_user_token_headers)
        
        # Create credit package
        credit_package = CreditPackage(
            user_id=user.id,
            credits=50,
            remaining_credits=30,
            product_id="credits_pack_50",
            platform=SubscriptionPlatformEnum.stripe
        )
        db.add(credit_package)
        db.commit()
        
        response = client.post(
            "/api/v1/usage/consume",
            headers=normal_user_token_headers,
            json={
                "usage_type": "image_generation",
                "count": 1
            }
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "credits" in data["message"].lower()
        assert data["remaining_credits"] == 25  # 30 - 5 (cost per use)
    
    def test_consume_usage_no_access(
        self, client: TestClient, normal_user_token_headers: dict
    ):
        """Test consuming usage with no subscription or credits."""
        response = client.post(
            "/api/v1/usage/consume",
            headers=normal_user_token_headers,
            json={
                "usage_type": "image_generation",
                "count": 1
            }
        )
        assert response.status_code == 403
        assert "No active subscription or credits available" in response.json()["detail"]
    
    def test_get_usage_history(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test getting usage history."""
        # Get the user from token
        user = get_user_from_token(db, normal_user_token_headers)
        
        # Create usage records
        usage_records = [
            UsageRecord(
                user_id=user.id,
                usage_type=UsageTypeEnum.image_generation,
                count=1,
                used_at=datetime.now(timezone.utc) - timedelta(hours=1)
            ),
            UsageRecord(
                user_id=user.id,
                usage_type=UsageTypeEnum.hd_export,
                count=1,
                used_at=datetime.now(timezone.utc) - timedelta(hours=2)
            )
        ]
        
        for record in usage_records:
            db.add(record)
        db.commit()
        
        response = client.get(
            "/api/v1/usage/history",
            headers=normal_user_token_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["count"] == 2
        assert len(data["data"]) == 2
        
        # Should be ordered by used_at desc (newest first)
        assert data["data"][0]["usage_type"] == "image_generation"
        assert data["data"][1]["usage_type"] == "hd_export"
    
    def test_get_usage_history_filtered(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test getting usage history filtered by type."""
        # Get the user from token
        user = get_user_from_token(db, normal_user_token_headers)
        
        # Create usage records of different types
        usage_records = [
            UsageRecord(
                user_id=user.id,
                usage_type=UsageTypeEnum.image_generation,
                count=1
            ),
            UsageRecord(
                user_id=user.id,
                usage_type=UsageTypeEnum.hd_export,
                count=1
            )
        ]
        
        for record in usage_records:
            db.add(record)
        db.commit()
        
        response = client.get(
            "/api/v1/usage/history?usage_type=image_generation",
            headers=normal_user_token_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["count"] == 1
        assert len(data["data"]) == 1
        assert data["data"][0]["usage_type"] == "image_generation"
    
    def test_get_usage_stats(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test getting usage statistics."""
        # Get the user from token
        user = get_user_from_token(db, normal_user_token_headers)
        
        # Create usage records
        now = datetime.now(timezone.utc)
        usage_records = [
            UsageRecord(
                user_id=user.id,
                usage_type=UsageTypeEnum.image_generation,
                count=3,
                used_at=now - timedelta(days=1)
            ),
            UsageRecord(
                user_id=user.id,
                usage_type=UsageTypeEnum.hd_export,
                count=2,
                used_at=now - timedelta(days=2)
            )
        ]
        
        for record in usage_records:
            db.add(record)
        db.commit()
        
        response = client.get(
            "/api/v1/usage/stats?days=30",
            headers=normal_user_token_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["user_id"] == str(user.id)
        assert data["period_days"] == 30
        assert data["total_usage"] == 5  # 3 + 2
        assert "usage_by_type" in data
        assert "daily_usage" in data
    
    def test_get_current_month_usage(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test getting current month usage."""
        # Get the user from token
        user = get_user_from_token(db, normal_user_token_headers)
        
        # Create usage record for current month
        usage_record = UsageRecord(
            user_id=user.id,
            usage_type=UsageTypeEnum.image_generation,
            count=5
        )
        db.add(usage_record)
        db.commit()
        
        response = client.get(
            "/api/v1/usage/current-month",
            headers=normal_user_token_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["user_id"] == str(user.id)
        assert data["total_usage"] == 5
        assert "usage_by_type" in data
        assert "month" in data
    
    def test_get_usage_limits_with_subscription(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test getting usage limits for user with subscription."""
        # Get the user from token
        user = get_user_from_token(db, normal_user_token_headers)
        
        # Create active subscription
        now = datetime.now(timezone.utc)
        subscription = Subscription(
            user_id=user.id,
            product_id="sub_monthly_40",
            platform=SubscriptionPlatformEnum.apple,
            start_date=now - timedelta(days=5),
            end_date=now + timedelta(days=25),
            is_active=True,
            original_transaction_id="test_transaction_123"
        )
        db.add(subscription)
        db.commit()
        
        response = client.get(
            "/api/v1/usage/limits",
            headers=normal_user_token_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["user_id"] == str(user.id)
        assert data["subscription_active"] is True
        assert data["subscription_product"] == "sub_monthly_40"
        assert data["monthly_limit"] == 40
        assert data["monthly_used"] == 0
        assert data["monthly_remaining"] == 40
        assert data["can_use_service"] is True
    
    def test_batch_consume_usage(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test batch consuming multiple usage items."""
        # Get the user from token
        user = get_user_from_token(db, normal_user_token_headers)
        
        # Create active subscription
        now = datetime.now(timezone.utc)
        subscription = Subscription(
            user_id=user.id,
            product_id="sub_monthly_60",
            platform=SubscriptionPlatformEnum.apple,
            start_date=now - timedelta(days=5),
            end_date=now + timedelta(days=25),
            is_active=True,
            original_transaction_id="test_transaction_123"
        )
        db.add(subscription)
        db.commit()
        
        response = client.post(
            "/api/v1/usage/batch-consume",
            headers=normal_user_token_headers,
            json=[
                {"usage_type": "image_generation", "count": 1},
                {"usage_type": "hd_export", "count": 1}
            ]
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["total_requests"] == 2
        assert data["successful"] == 2
        assert data["failed"] == 0
        assert len(data["results"]) == 2
        
        # Verify usage records were created
        usage_records = db.query(UsageRecord).filter(
            UsageRecord.user_id == user.id
        ).all()
        assert len(usage_records) == 2
    
    def test_batch_consume_usage_too_many_requests(
        self, client: TestClient, normal_user_token_headers: dict
    ):
        """Test batch consuming with too many requests."""
        # Create 11 usage requests (exceeds limit of 10)
        usage_requests = [
            {"usage_type": "image_generation", "count": 1}
            for _ in range(11)
        ]
        
        response = client.post(
            "/api/v1/usage/batch-consume",
            headers=normal_user_token_headers,
            json=usage_requests
        )
        assert response.status_code == 400
        assert "Maximum 10 usage items" in response.json()["detail"]
