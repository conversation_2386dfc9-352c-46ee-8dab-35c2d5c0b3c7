"""
Tests for subscription API routes.

This module tests the subscription management endpoints.
"""

import uuid
from datetime import datetime, timezone, timedelta

import pytest
from fastapi.testclient import TestClient
from sqlmodel import Session

from app.models import (
    User, Subscription, SubscriptionPlatformEnum
)
from app.tests.utils.utils import get_superuser_token_headers


class TestSubscriptionRoutes:
    """Test cases for subscription API routes."""
    
    def test_get_subscription_status_no_subscription(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test getting subscription status for user with no subscription."""
        response = client.get(
            "/api/v1/subscriptions/status",
            headers=normal_user_token_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["has_active_subscription"] is False
        assert data["total_credits"] == 0
        assert data["can_use_service"] is False
    
    def test_get_subscription_status_with_active_subscription(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test getting subscription status for user with active subscription."""
        # Get the user from token
        from app.tests.utils.user import get_user_from_token
        user = get_user_from_token(db, normal_user_token_headers)
        
        # Create active subscription
        now = datetime.now(timezone.utc)
        subscription = Subscription(
            user_id=user.id,
            product_id="sub_monthly_40",
            platform=SubscriptionPlatformEnum.apple,
            start_date=now - timedelta(days=5),
            end_date=now + timedelta(days=25),
            is_active=True,
            original_transaction_id="test_transaction_123"
        )
        db.add(subscription)
        db.commit()
        
        response = client.get(
            "/api/v1/subscriptions/status",
            headers=normal_user_token_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["has_active_subscription"] is True
        assert data["subscription_product_id"] == "sub_monthly_40"
        assert data["monthly_limit"] == 40
        assert data["can_use_service"] is True
    
    def test_check_access_with_subscription(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test access check for user with active subscription."""
        # Get the user from token
        from app.tests.utils.user import get_user_from_token
        user = get_user_from_token(db, normal_user_token_headers)
        
        # Create active subscription
        now = datetime.now(timezone.utc)
        subscription = Subscription(
            user_id=user.id,
            product_id="sub_monthly_40",
            platform=SubscriptionPlatformEnum.apple,
            start_date=now - timedelta(days=5),
            end_date=now + timedelta(days=25),
            is_active=True,
            original_transaction_id="test_transaction_123"
        )
        db.add(subscription)
        db.commit()
        
        response = client.get(
            "/api/v1/subscriptions/check-access",
            headers=normal_user_token_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["can_access"] is True
        assert data["remaining_monthly_usage"] == 40
        assert data["subscription_status"] == "active"
    
    def test_check_access_no_subscription(
        self, client: TestClient, normal_user_token_headers: dict
    ):
        """Test access check for user with no subscription."""
        response = client.get(
            "/api/v1/subscriptions/check-access",
            headers=normal_user_token_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["can_access"] is False
        assert "No active subscription or credits available" in data["reason"]
        assert data["subscription_status"] == "inactive"
    
    def test_get_user_subscriptions(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test getting user's subscription history."""
        # Get the user from token
        from app.tests.utils.user import get_user_from_token
        user = get_user_from_token(db, normal_user_token_headers)
        
        # Create multiple subscriptions
        now = datetime.now(timezone.utc)
        subscriptions = [
            Subscription(
                user_id=user.id,
                product_id="sub_monthly_40",
                platform=SubscriptionPlatformEnum.apple,
                start_date=now - timedelta(days=60),
                end_date=now - timedelta(days=30),
                is_active=False,
                original_transaction_id="old_transaction"
            ),
            Subscription(
                user_id=user.id,
                product_id="sub_monthly_60",
                platform=SubscriptionPlatformEnum.apple,
                start_date=now - timedelta(days=5),
                end_date=now + timedelta(days=25),
                is_active=True,
                original_transaction_id="current_transaction"
            )
        ]
        
        for sub in subscriptions:
            db.add(sub)
        db.commit()
        
        response = client.get(
            "/api/v1/subscriptions/",
            headers=normal_user_token_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["count"] == 2
        assert len(data["data"]) == 2
        
        # Should be ordered by start_date desc (newest first)
        assert data["data"][0]["product_id"] == "sub_monthly_60"
        assert data["data"][1]["product_id"] == "sub_monthly_40"
    
    def test_validate_apple_receipt_missing_data(
        self, client: TestClient, normal_user_token_headers: dict
    ):
        """Test Apple receipt validation with missing data."""
        response = client.post(
            "/api/v1/subscriptions/apple/validate",
            headers=normal_user_token_headers,
            json={}
        )
        assert response.status_code == 400
        assert "receipt_data is required" in response.json()["detail"]
    
    def test_validate_google_subscription_missing_data(
        self, client: TestClient, normal_user_token_headers: dict
    ):
        """Test Google subscription validation with missing data."""
        response = client.post(
            "/api/v1/subscriptions/google/validate",
            headers=normal_user_token_headers,
            json={"subscription_id": "test_id"}  # Missing purchase_token
        )
        assert response.status_code == 400
        assert "purchase_token is required" in response.json()["detail"]
    
    def test_get_specific_subscription(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test getting specific subscription details."""
        # Get the user from token
        from app.tests.utils.user import get_user_from_token
        user = get_user_from_token(db, normal_user_token_headers)
        
        # Create subscription
        now = datetime.now(timezone.utc)
        subscription = Subscription(
            user_id=user.id,
            product_id="sub_monthly_40",
            platform=SubscriptionPlatformEnum.apple,
            start_date=now - timedelta(days=5),
            end_date=now + timedelta(days=25),
            is_active=True,
            original_transaction_id="test_transaction_123"
        )
        db.add(subscription)
        db.commit()
        db.refresh(subscription)
        
        response = client.get(
            f"/api/v1/subscriptions/{subscription.id}",
            headers=normal_user_token_headers
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["id"] == str(subscription.id)
        assert data["product_id"] == "sub_monthly_40"
        assert data["platform"] == "apple"
        assert data["is_active"] is True
    
    def test_get_subscription_not_found(
        self, client: TestClient, normal_user_token_headers: dict
    ):
        """Test getting non-existent subscription."""
        fake_id = str(uuid.uuid4())
        response = client.get(
            f"/api/v1/subscriptions/{fake_id}",
            headers=normal_user_token_headers
        )
        assert response.status_code == 404
        assert "Subscription not found" in response.json()["detail"]
    
    def test_get_subscription_unauthorized(
        self, client: TestClient, normal_user_token_headers: dict, db: Session
    ):
        """Test getting subscription that belongs to another user."""
        # Create another user
        other_user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            full_name="Other User"
        )
        db.add(other_user)
        db.commit()
        db.refresh(other_user)
        
        # Create subscription for other user
        now = datetime.now(timezone.utc)
        subscription = Subscription(
            user_id=other_user.id,
            product_id="sub_monthly_40",
            platform=SubscriptionPlatformEnum.apple,
            start_date=now - timedelta(days=5),
            end_date=now + timedelta(days=25),
            is_active=True,
            original_transaction_id="test_transaction_123"
        )
        db.add(subscription)
        db.commit()
        db.refresh(subscription)
        
        response = client.get(
            f"/api/v1/subscriptions/{subscription.id}",
            headers=normal_user_token_headers
        )
        assert response.status_code == 403
        assert "Not authorized" in response.json()["detail"]
    
    def test_update_subscription_as_superuser(
        self, client: TestClient, db: Session
    ):
        """Test updating subscription as superuser."""
        superuser_token_headers = get_superuser_token_headers(client)
        
        # Create a user and subscription
        user = User(
            email="<EMAIL>",
            hashed_password="hashed_password",
            full_name="Test User"
        )
        db.add(user)
        db.commit()
        db.refresh(user)
        
        now = datetime.now(timezone.utc)
        subscription = Subscription(
            user_id=user.id,
            product_id="sub_monthly_40",
            platform=SubscriptionPlatformEnum.apple,
            start_date=now - timedelta(days=5),
            end_date=now + timedelta(days=25),
            is_active=True,
            original_transaction_id="test_transaction_123"
        )
        db.add(subscription)
        db.commit()
        db.refresh(subscription)
        
        # Update subscription
        new_end_date = now + timedelta(days=60)
        response = client.put(
            f"/api/v1/subscriptions/{subscription.id}",
            headers=superuser_token_headers,
            json={
                "end_date": new_end_date.isoformat(),
                "is_active": False
            }
        )
        assert response.status_code == 200
        
        data = response.json()
        assert data["is_active"] is False
        # Note: The exact date comparison might need adjustment based on timezone handling
