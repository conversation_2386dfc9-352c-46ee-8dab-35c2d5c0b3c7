"""
Tests for billing service.

This module tests the billing and subscription logic.
"""

import uuid
from datetime import datetime, timezone, timedelta

import pytest
from sqlmodel import Session

from app.models import (
    User, Subscription, UsageRecord, CreditPackage,
    SubscriptionPlatformEnum, UsageTypeEnum
)
from app.services.billing import BillingService


@pytest.fixture
def test_user(db: Session) -> User:
    """Create a test user."""
    user = User(
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="Test User"
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


@pytest.fixture
def active_subscription(db: Session, test_user: User) -> Subscription:
    """Create an active subscription for test user."""
    now = datetime.now(timezone.utc)
    subscription = Subscription(
        user_id=test_user.id,
        product_id="sub_monthly_40",
        platform=SubscriptionPlatformEnum.apple,
        start_date=now - timedelta(days=5),
        end_date=now + timedelta(days=25),
        is_active=True,
        original_transaction_id="test_transaction_123"
    )
    db.add(subscription)
    db.commit()
    db.refresh(subscription)
    return subscription


@pytest.fixture
def credit_package(db: Session, test_user: User) -> CreditPackage:
    """Create a credit package for test user."""
    package = CreditPackage(
        user_id=test_user.id,
        credits=50,
        remaining_credits=30,
        product_id="credits_pack_50",
        platform=SubscriptionPlatformEnum.stripe
    )
    db.add(package)
    db.commit()
    db.refresh(package)
    return package


class TestBillingService:
    """Test cases for BillingService."""
    
    def test_get_user_subscription_status_with_active_subscription(
        self, db: Session, test_user: User, active_subscription: Subscription
    ):
        """Test getting subscription status for user with active subscription."""
        billing_service = BillingService(db)
        status = billing_service.get_user_subscription_status(test_user.id)
        
        assert status.user_id == test_user.id
        assert status.has_active_subscription is True
        assert status.subscription_product_id == "sub_monthly_40"
        assert status.monthly_limit == 40
        assert status.can_use_service is True
    
    def test_get_user_subscription_status_with_credits_only(
        self, db: Session, test_user: User, credit_package: CreditPackage
    ):
        """Test getting subscription status for user with only credits."""
        billing_service = BillingService(db)
        status = billing_service.get_user_subscription_status(test_user.id)
        
        assert status.user_id == test_user.id
        assert status.has_active_subscription is False
        assert status.total_credits == 30
        assert status.monthly_limit == 0
        assert status.can_use_service is True  # Can use because has credits
    
    def test_get_user_subscription_status_no_access(
        self, db: Session, test_user: User
    ):
        """Test getting subscription status for user with no access."""
        billing_service = BillingService(db)
        status = billing_service.get_user_subscription_status(test_user.id)
        
        assert status.user_id == test_user.id
        assert status.has_active_subscription is False
        assert status.total_credits == 0
        assert status.can_use_service is False
    
    def test_check_access_with_subscription(
        self, db: Session, test_user: User, active_subscription: Subscription
    ):
        """Test access check for user with active subscription."""
        billing_service = BillingService(db)
        access = billing_service.check_access(test_user.id, UsageTypeEnum.image_generation)
        
        assert access.can_access is True
        assert access.remaining_monthly_usage == 40  # No usage yet
        assert access.subscription_status == "active"
    
    def test_check_access_with_credits(
        self, db: Session, test_user: User, credit_package: CreditPackage
    ):
        """Test access check for user with credits."""
        billing_service = BillingService(db)
        access = billing_service.check_access(test_user.id, UsageTypeEnum.image_generation)
        
        assert access.can_access is True
        assert access.remaining_credits == 30
        assert access.subscription_status == "credits_only"
    
    def test_check_access_no_access(self, db: Session, test_user: User):
        """Test access check for user with no access."""
        billing_service = BillingService(db)
        access = billing_service.check_access(test_user.id, UsageTypeEnum.image_generation)
        
        assert access.can_access is False
        assert "No active subscription or credits available" in access.reason
        assert access.subscription_status == "inactive"
    
    def test_consume_usage_with_subscription(
        self, db: Session, test_user: User, active_subscription: Subscription
    ):
        """Test consuming usage with active subscription."""
        billing_service = BillingService(db)
        
        success, message, details = billing_service.consume_usage(
            test_user.id, UsageTypeEnum.image_generation, 1
        )
        
        assert success is True
        assert "subscription" in message.lower()
        assert details["remaining_monthly_usage"] == 39
        
        # Verify usage record was created
        usage_records = db.query(UsageRecord).filter(
            UsageRecord.user_id == test_user.id
        ).all()
        assert len(usage_records) == 1
        assert usage_records[0].usage_type == UsageTypeEnum.image_generation
        assert usage_records[0].count == 1
    
    def test_consume_usage_with_credits(
        self, db: Session, test_user: User, credit_package: CreditPackage
    ):
        """Test consuming usage with credits."""
        billing_service = BillingService(db)
        
        success, message, details = billing_service.consume_usage(
            test_user.id, UsageTypeEnum.image_generation, 1
        )
        
        assert success is True
        assert "credits" in message.lower()
        assert details["remaining_credits"] == 25  # 30 - 5 (cost per use)
        
        # Verify credit package was updated
        db.refresh(credit_package)
        assert credit_package.remaining_credits == 25
    
    def test_consume_usage_insufficient_credits(
        self, db: Session, test_user: User
    ):
        """Test consuming usage with insufficient credits."""
        # Create package with only 3 credits (less than cost of 5)
        package = CreditPackage(
            user_id=test_user.id,
            credits=3,
            remaining_credits=3,
            product_id="credits_pack_10",
            platform=SubscriptionPlatformEnum.stripe
        )
        db.add(package)
        db.commit()
        
        billing_service = BillingService(db)
        
        success, message, details = billing_service.consume_usage(
            test_user.id, UsageTypeEnum.image_generation, 1
        )
        
        assert success is False
        assert "Insufficient credits" in message
    
    def test_monthly_usage_limit_reached(
        self, db: Session, test_user: User, active_subscription: Subscription
    ):
        """Test behavior when monthly usage limit is reached."""
        billing_service = BillingService(db)
        
        # Create 40 usage records (reaching the limit)
        for i in range(40):
            usage = UsageRecord(
                user_id=test_user.id,
                usage_type=UsageTypeEnum.image_generation,
                count=1
            )
            db.add(usage)
        db.commit()
        
        # Try to consume one more
        success, message, details = billing_service.consume_usage(
            test_user.id, UsageTypeEnum.image_generation, 1
        )
        
        # Should fail because limit reached and no credits
        assert success is False
        assert "No available usage quota" in message
    
    def test_get_total_credits_multiple_packages(self, db: Session, test_user: User):
        """Test getting total credits from multiple packages."""
        # Create multiple credit packages
        package1 = CreditPackage(
            user_id=test_user.id,
            credits=10,
            remaining_credits=5,
            product_id="credits_pack_10",
            platform=SubscriptionPlatformEnum.apple
        )
        package2 = CreditPackage(
            user_id=test_user.id,
            credits=50,
            remaining_credits=25,
            product_id="credits_pack_50",
            platform=SubscriptionPlatformEnum.google
        )
        db.add_all([package1, package2])
        db.commit()
        
        billing_service = BillingService(db)
        total_credits = billing_service._get_total_credits(test_user.id)
        
        assert total_credits == 30  # 5 + 25
    
    def test_get_monthly_usage_count(self, db: Session, test_user: User):
        """Test getting monthly usage count."""
        now = datetime.now(timezone.utc)
        
        # Create usage records for current month
        for i in range(3):
            usage = UsageRecord(
                user_id=test_user.id,
                usage_type=UsageTypeEnum.image_generation,
                count=2,
                used_at=now - timedelta(days=i)
            )
            db.add(usage)
        
        # Create usage record for previous month (should not count)
        old_usage = UsageRecord(
            user_id=test_user.id,
            usage_type=UsageTypeEnum.image_generation,
            count=5,
            used_at=now - timedelta(days=35)
        )
        db.add(old_usage)
        db.commit()
        
        billing_service = BillingService(db)
        monthly_count = billing_service._get_monthly_usage_count(test_user.id)
        
        assert monthly_count == 6  # 3 records * 2 count each
