"""
Credit system API routes.

This module provides endpoints for managing user credits,
including purchasing, consuming, and tracking credit packages.
"""

import uuid
from typing import Any, Dict

from fastapi import APIRouter, HTTPException
from sqlmodel import select, func

from app.api.deps import CurrentUser, SessionDep
from app.models import (
    CreditPackage, CreditPackageCreate, CreditPackagePublic, CreditPackagesPublic,
    SubscriptionPlatformEnum, Message
)

router = APIRouter(prefix="/credits", tags=["credits"])


@router.get("/balance", response_model=Dict[str, Any])
def get_credit_balance(
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Get user's current credit balance.
    
    Returns total available credits and breakdown by package.
    """
    # Get total credits
    total_statement = select(func.sum(CreditPackage.remaining_credits)).where(
        CreditPackage.user_id == current_user.id,
        CreditPackage.remaining_credits > 0
    )
    total_credits = session.exec(total_statement).first() or 0
    
    # Get active credit packages
    packages_statement = select(CreditPackage).where(
        CreditPackage.user_id == current_user.id,
        CreditPackage.remaining_credits > 0
    ).order_by(CreditPackage.purchased_at)
    
    active_packages = session.exec(packages_statement).all()
    
    # Get total purchased credits (all time)
    total_purchased_statement = select(func.sum(CreditPackage.credits)).where(
        CreditPackage.user_id == current_user.id
    )
    total_purchased = session.exec(total_purchased_statement).first() or 0
    
    # Get total used credits
    total_used = total_purchased - total_credits
    
    return {
        "user_id": str(current_user.id),
        "total_credits": total_credits,
        "total_purchased": total_purchased,
        "total_used": total_used,
        "active_packages": len(active_packages),
        "packages": [
            {
                "id": str(package.id),
                "product_id": package.product_id,
                "remaining_credits": package.remaining_credits,
                "original_credits": package.credits,
                "purchased_at": package.purchased_at.isoformat(),
                "platform": package.platform
            }
            for package in active_packages
        ]
    }


@router.get("/packages", response_model=CreditPackagesPublic)
def get_credit_packages(
    current_user: CurrentUser,
    session: SessionDep,
    skip: int = 0,
    limit: int = 100,
    active_only: bool = False
) -> Any:
    """
    Get user's credit package history.
    
    Args:
        active_only: If True, only return packages with remaining credits
    """
    statement = select(CreditPackage).where(
        CreditPackage.user_id == current_user.id
    )
    
    if active_only:
        statement = statement.where(CreditPackage.remaining_credits > 0)
    
    statement = statement.offset(skip).limit(limit).order_by(CreditPackage.purchased_at.desc())
    
    packages = session.exec(statement).all()
    
    # Get total count
    count_statement = select(func.count()).select_from(CreditPackage).where(
        CreditPackage.user_id == current_user.id
    )
    if active_only:
        count_statement = count_statement.where(CreditPackage.remaining_credits > 0)
    
    count = session.exec(count_statement).one()
    
    return CreditPackagesPublic(data=packages, count=count)


@router.post("/purchase", response_model=CreditPackagePublic)
def purchase_credits(
    purchase_data: Dict[str, Any],
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Record a credit package purchase.
    
    This endpoint is typically called after successful payment processing
    through Apple/Google/Stripe.
    
    Expected payload:
    {
        "product_id": "credits_pack_10",
        "credits": 10,
        "platform": "apple|google|stripe",
        "transaction_id": "platform_transaction_id"
    }
    """
    required_fields = ["product_id", "credits", "platform"]
    for field in required_fields:
        if field not in purchase_data:
            raise HTTPException(
                status_code=400,
                detail=f"{field} is required"
            )
    
    # Validate platform
    try:
        platform = SubscriptionPlatformEnum(purchase_data["platform"])
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail="Invalid platform. Must be 'apple', 'google', or 'stripe'"
        )
    
    # Validate credits amount
    credits = purchase_data["credits"]
    if not isinstance(credits, int) or credits <= 0:
        raise HTTPException(
            status_code=400,
            detail="Credits must be a positive integer"
        )
    
    # Create credit package
    credit_package = CreditPackage(
        user_id=current_user.id,
        credits=credits,
        remaining_credits=credits,
        product_id=purchase_data["product_id"],
        platform=platform
    )
    
    session.add(credit_package)
    session.commit()
    session.refresh(credit_package)
    
    return credit_package


@router.post("/apple/validate", response_model=Dict[str, Any])
async def validate_apple_credit_purchase(
    current_user: CurrentUser,
    session: SessionDep,
    receipt_data: Dict[str, str]
) -> Any:
    """
    Validate Apple credit package purchase.
    
    Similar to subscription validation but for one-time credit purchases.
    """
    if "receipt_data" not in receipt_data:
        raise HTTPException(
            status_code=400,
            detail="receipt_data is required"
        )
    
    # This would integrate with Apple's validation service
    # For now, return a placeholder response
    
    return {
        "success": True,
        "message": "Apple credit purchase validation not yet implemented",
        "receipt_data": receipt_data["receipt_data"][:50] + "..."
    }


@router.post("/google/validate", response_model=Dict[str, Any])
async def validate_google_credit_purchase(
    current_user: CurrentUser,
    session: SessionDep,
    purchase_data: Dict[str, str]
) -> Any:
    """
    Validate Google Play credit package purchase.
    """
    required_fields = ["product_id", "purchase_token"]
    for field in required_fields:
        if field not in purchase_data:
            raise HTTPException(
                status_code=400,
                detail=f"{field} is required"
            )
    
    # This would integrate with Google Play validation service
    # For now, return a placeholder response
    
    return {
        "success": True,
        "message": "Google Play credit purchase validation not yet implemented",
        "product_id": purchase_data["product_id"],
        "purchase_token": purchase_data["purchase_token"][:20] + "..."
    }


@router.get("/pricing", response_model=Dict[str, Any])
def get_credit_pricing() -> Any:
    """
    Get available credit packages and their pricing.
    
    This endpoint returns the available credit packages
    that users can purchase.
    """
    credit_packages = {
        "credits_pack_10": {
            "credits": 10,
            "price_usd": 4.99,
            "cost_per_credit": 0.499,
            "description": "Small credit pack - perfect for trying premium features"
        },
        "credits_pack_50": {
            "credits": 50,
            "price_usd": 19.99,
            "cost_per_credit": 0.400,
            "description": "Medium credit pack - great value for regular users",
            "savings_percent": 20
        },
        "credits_pack_100": {
            "credits": 100,
            "price_usd": 34.99,
            "cost_per_credit": 0.350,
            "description": "Large credit pack - best value for power users",
            "savings_percent": 30
        }
    }
    
    return {
        "packages": credit_packages,
        "currency": "USD",
        "usage_costs": {
            "image_generation": 5,
            "hd_export": 5,
            "premium_filter": 3
        }
    }


@router.get("/{package_id}", response_model=CreditPackagePublic)
def get_credit_package(
    package_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Get specific credit package details.
    
    Users can only access their own credit packages.
    """
    package = session.get(CreditPackage, package_id)
    
    if not package:
        raise HTTPException(
            status_code=404,
            detail="Credit package not found"
        )
    
    if package.user_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to access this credit package"
        )
    
    return package


@router.delete("/{package_id}", response_model=Message)
def delete_credit_package(
    package_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Delete a credit package.
    
    This endpoint is typically used for refunds or corrections.
    Only superusers can delete credit packages.
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="Only administrators can delete credit packages"
        )
    
    package = session.get(CreditPackage, package_id)
    
    if not package:
        raise HTTPException(
            status_code=404,
            detail="Credit package not found"
        )
    
    session.delete(package)
    session.commit()
    
    return Message(message="Credit package deleted successfully")
