"""
Subscription management API routes.

This module provides endpoints for managing user subscriptions,
including status checks, validation, and synchronization.
"""

import uuid
from typing import Any, Dict

from fastapi import APIRouter, HTTPException, Depends
from sqlmodel import select

from app.api.deps import CurrentUser, SessionDep
from app.models import (
    Subscription, SubscriptionCreate, SubscriptionUpdate, SubscriptionPublic,
    SubscriptionsPublic, UserSubscriptionStatus, AccessCheckResponse,
    UsageTypeEnum, Message, SubscriptionPlatformEnum, User,
    SubscriptionUpgradeRequest, SubscriptionUpgradeResponse, CreditPackage
)
from app.services.billing import BillingService
from app.services.apple_validator import AppleValidator
from app.services.google_validator import GooglePlayValidator

router = APIRouter(prefix="/subscriptions", tags=["subscriptions"])


@router.get("/status", response_model=UserSubscriptionStatus)
def get_subscription_status(
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Get current user's subscription status.
    
    Returns comprehensive subscription information including:
    - Active subscription details
    - Available credits
    - Monthly usage and limits
    - Service access status
    """
    billing_service = BillingService(session)
    return billing_service.get_user_subscription_status(current_user.id)


@router.get("/check-access", response_model=AccessCheckResponse)
def check_access(
    current_user: CurrentUser,
    session: SessionDep,
    usage_type: UsageTypeEnum = UsageTypeEnum.image_generation
) -> Any:
    """
    Check if user can access a specific service.
    
    This endpoint should be called before allowing users to use
    premium features to ensure they have sufficient quota or credits.
    """
    billing_service = BillingService(session)
    return billing_service.check_access(current_user.id, usage_type)


@router.get("/", response_model=SubscriptionsPublic)
def get_user_subscriptions(
    current_user: CurrentUser,
    session: SessionDep,
    skip: int = 0,
    limit: int = 100
) -> Any:
    """
    Get user's subscription history.
    
    Returns all subscriptions (active and expired) for the current user.
    """
    statement = select(Subscription).where(
        Subscription.user_id == current_user.id
    ).offset(skip).limit(limit).order_by(Subscription.start_date.desc())
    
    subscriptions = session.exec(statement).all()
    
    count_statement = select(Subscription).where(
        Subscription.user_id == current_user.id
    )
    count = len(session.exec(count_statement).all())
    
    return SubscriptionsPublic(data=subscriptions, count=count)


@router.post("/apple/validate", response_model=Dict[str, Any])
async def validate_apple_receipt(
    current_user: CurrentUser,
    session: SessionDep,
    receipt_data: Dict[str, str]
) -> Any:
    """
    Validate Apple App Store receipt.
    
    Expected payload:
    {
        "receipt_data": "base64_encoded_receipt_data"
    }
    """
    if "receipt_data" not in receipt_data:
        raise HTTPException(
            status_code=400,
            detail="receipt_data is required"
        )
    
    apple_validator = AppleValidator(session)
    
    is_valid, receipt_info, error_message = await apple_validator.validate_receipt(
        receipt_data["receipt_data"],
        current_user.id
    )
    
    if not is_valid:
        raise HTTPException(
            status_code=400,
            detail=error_message or "Invalid receipt"
        )
    
    return {
        "success": True,
        "message": "Receipt validated successfully",
        "receipt_info": receipt_info
    }


@router.post("/google/validate", response_model=Dict[str, Any])
async def validate_google_subscription(
    current_user: CurrentUser,
    session: SessionDep,
    subscription_data: Dict[str, str]
) -> Any:
    """
    Validate Google Play subscription.
    
    Expected payload:
    {
        "subscription_id": "product_id",
        "purchase_token": "purchase_token_from_google"
    }
    """
    required_fields = ["subscription_id", "purchase_token"]
    for field in required_fields:
        if field not in subscription_data:
            raise HTTPException(
                status_code=400,
                detail=f"{field} is required"
            )
    
    google_validator = GooglePlayValidator(session)
    
    is_valid, sub_info, error_message = await google_validator.validate_subscription(
        subscription_data["subscription_id"],
        subscription_data["purchase_token"],
        current_user.id
    )
    
    if not is_valid:
        raise HTTPException(
            status_code=400,
            detail=error_message or "Invalid subscription"
        )
    
    return {
        "success": True,
        "message": "Subscription validated successfully",
        "subscription_info": sub_info
    }


@router.post("/sync", response_model=Message)
async def sync_subscriptions(
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Sync user's subscriptions with platform stores.
    
    This endpoint can be used to refresh subscription status
    by re-validating with Apple/Google servers.
    """
    # Get user's existing subscriptions
    statement = select(Subscription).where(
        Subscription.user_id == current_user.id,
        Subscription.is_active == True
    )
    active_subscriptions = session.exec(statement).all()
    
    synced_count = 0
    
    for subscription in active_subscriptions:
        try:
            if subscription.platform == "apple":
                apple_validator = AppleValidator(session)
                is_active, updated_sub = await apple_validator.verify_subscription_status(
                    current_user.id,
                    subscription.original_transaction_id
                )
                if updated_sub:
                    synced_count += 1
                    
            elif subscription.platform == "google":
                google_validator = GooglePlayValidator(session)
                is_active, updated_sub = await google_validator.verify_subscription_status(
                    current_user.id,
                    subscription.original_transaction_id
                )
                if updated_sub:
                    synced_count += 1
                    
        except Exception as e:
            # Log error but continue with other subscriptions
            print(f"Error syncing subscription {subscription.id}: {e}")
            continue
    
    return Message(
        message=f"Synced {synced_count} subscription(s) successfully"
    )


@router.get("/{subscription_id}", response_model=SubscriptionPublic)
def get_subscription(
    subscription_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Get specific subscription details.
    
    Users can only access their own subscriptions.
    """
    subscription = session.get(Subscription, subscription_id)
    
    if not subscription:
        raise HTTPException(
            status_code=404,
            detail="Subscription not found"
        )
    
    if subscription.user_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to access this subscription"
        )
    
    return subscription


@router.put("/{subscription_id}", response_model=SubscriptionPublic)
def update_subscription(
    subscription_id: uuid.UUID,
    subscription_update: SubscriptionUpdate,
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Update subscription details.
    
    This endpoint is typically used by admin users or webhook handlers.
    Regular users should use the sync endpoint instead.
    """
    subscription = session.get(Subscription, subscription_id)
    
    if not subscription:
        raise HTTPException(
            status_code=404,
            detail="Subscription not found"
        )
    
    if subscription.user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to update this subscription"
        )
    
    # Update subscription fields
    update_data = subscription_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(subscription, field, value)
    
    session.add(subscription)
    session.commit()
    session.refresh(subscription)

    return subscription


@router.post("/create", response_model=SubscriptionPublic)
def create_subscription(
    subscription_create: SubscriptionCreate,
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Create a new subscription for a user.

    This endpoint is typically used by admin users or payment processors
    to create subscriptions after successful payment.

    Only superusers can create subscriptions for other users.
    Regular users can only create subscriptions for themselves.
    """
    # Check permissions
    if subscription_create.user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to create subscription for this user"
        )

    # Verify target user exists
    target_user = session.get(User, subscription_create.user_id)
    if not target_user:
        raise HTTPException(
            status_code=404,
            detail="Target user not found"
        )

    # Check if user already has an active subscription
    existing_subscription = session.exec(
        select(Subscription).where(
            Subscription.user_id == subscription_create.user_id,
            Subscription.is_active == True
        )
    ).first()

    if existing_subscription:
        raise HTTPException(
            status_code=400,
            detail="User already has an active subscription"
        )

    # Create new subscription
    subscription = Subscription.model_validate(subscription_create)
    session.add(subscription)
    session.commit()
    session.refresh(subscription)

    return subscription


@router.post("/upgrade", response_model=SubscriptionUpgradeResponse)
def upgrade_user_subscription(
    upgrade_request: SubscriptionUpgradeRequest,
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Upgrade a user from free status to subscription status.

    This endpoint creates a new subscription for a user or extends existing subscription.

    Request body should contain:
    - user_id: Target user UUID
    - product_id: Subscription product ID (e.g., "sub_monthly_40", "sub_yearly_480")
    - duration_days: Subscription duration in days (30 for monthly, 365 for yearly)
    - platform: Payment platform (stripe, apple, google)

    Only superusers can upgrade other users.
    Regular users can only upgrade themselves.

    Common product IDs:
    - "sub_monthly_40": Monthly subscription with 40 images
    - "sub_monthly_60": Monthly subscription with 60 images
    - "sub_yearly_480": Yearly subscription with 480 images (40 images/month)
    - "sub_yearly_600": Yearly subscription with 600 images (50 images/month)
    - "sub_yearly_720": Yearly subscription with 720 images (60 images/month)
    """
    from datetime import datetime, timezone, timedelta

    # Extract values from request
    user_id = upgrade_request.user_id
    product_id = upgrade_request.product_id
    duration_days = upgrade_request.duration_days
    platform = upgrade_request.platform

    # Check permissions
    if user_id != current_user.id and not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to upgrade this user"
        )

    # Verify target user exists
    target_user = session.get(User, user_id)
    if not target_user:
        raise HTTPException(
            status_code=404,
            detail="User not found"
        )

    # Check for existing active subscription
    existing_subscription = session.exec(
        select(Subscription).where(
            Subscription.user_id == user_id,
            Subscription.is_active == True
        )
    ).first()

    now = datetime.now(timezone.utc)

    if existing_subscription:
        # Extend existing subscription
        # Ensure both dates are timezone-aware for comparison
        end_date = existing_subscription.end_date
        if end_date.tzinfo is None:
            end_date = end_date.replace(tzinfo=timezone.utc)

        if end_date > now:
            # Extend from current end date
            new_end_date = end_date + timedelta(days=duration_days)
        else:
            # Extend from now if expired
            new_end_date = now + timedelta(days=duration_days)

        existing_subscription.end_date = new_end_date
        existing_subscription.product_id = product_id
        existing_subscription.platform = platform
        existing_subscription.is_active = True
        existing_subscription.last_verified_at = now

        session.add(existing_subscription)
        session.commit()
        session.refresh(existing_subscription)

        # 🆕 为订阅用户创建赠送积分
        billing_service = BillingService(session)
        try:
            bonus_credits = billing_service.create_subscription_bonus_credits(
                user_id=user_id,
                product_id=product_id,
                platform=platform
            )
            if bonus_credits:
                message = f"Subscription extended successfully. Bonus credits added: {bonus_credits.credits}"
            else:
                message = "Subscription extended successfully"
        except Exception as e:
            # 积分创建失败不影响订阅，只记录错误
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to create bonus credits for user {user_id}: {str(e)}")
            message = "Subscription extended successfully (bonus credits creation failed)"

        return SubscriptionUpgradeResponse(
            subscription=existing_subscription,
            message=message,
            is_new_subscription=False
        )
    else:
        # Create new subscription
        new_subscription = Subscription(
            user_id=user_id,
            product_id=product_id,
            platform=platform,
            start_date=now,
            end_date=now + timedelta(days=duration_days),
            is_active=True,
            original_transaction_id=f"admin_upgrade_{uuid.uuid4().hex[:8]}",
            last_verified_at=now
        )

        session.add(new_subscription)
        session.commit()
        session.refresh(new_subscription)

        # 🆕 为新订阅用户创建赠送积分
        billing_service = BillingService(session)
        try:
            bonus_credits = billing_service.create_subscription_bonus_credits(
                user_id=user_id,
                product_id=product_id,
                platform=platform
            )
            if bonus_credits:
                message = f"New subscription created successfully. Bonus credits added: {bonus_credits.credits}"
            else:
                message = "New subscription created successfully"
        except Exception as e:
            # 积分创建失败不影响订阅，只记录错误
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Failed to create bonus credits for user {user_id}: {str(e)}")
            message = "New subscription created successfully (bonus credits creation failed)"

        return SubscriptionUpgradeResponse(
            subscription=new_subscription,
            message=message,
            is_new_subscription=True
        )


@router.post("/upgrade-to-monthly", response_model=SubscriptionUpgradeResponse)
def upgrade_to_monthly(
    user_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep,
    images_per_month: int = 40
) -> Any:
    """
    Quick upgrade to monthly subscription.

    Args:
        user_id: Target user UUID
        images_per_month: Number of images per month (40 or 60)

    This is a convenience endpoint for upgrading to monthly subscription.
    """
    if images_per_month == 40:
        product_id = "sub_monthly_40"
    elif images_per_month == 60:
        product_id = "sub_monthly_60"
    else:
        raise HTTPException(
            status_code=400,
            detail="Invalid images_per_month. Must be 40 or 60"
        )

    upgrade_request = SubscriptionUpgradeRequest(
        user_id=user_id,
        product_id=product_id,
        duration_days=30,
        platform=SubscriptionPlatformEnum.stripe
    )

    return upgrade_user_subscription(upgrade_request, current_user, session)


@router.post("/upgrade-to-yearly", response_model=SubscriptionUpgradeResponse)
def upgrade_to_yearly(
    user_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep,
    images_per_year: int = 480
) -> Any:
    """
    Quick upgrade to yearly subscription.

    Args:
        user_id: Target user UUID
        images_per_year: Number of images per year (480, 600, or 720)

    This is a convenience endpoint for upgrading to yearly subscription.
    
    Available yearly plans:
    - 480 images/year (40 images/month): 2400 bonus credits
    - 600 images/year (50 images/month): 3000 bonus credits  
    - 720 images/year (60 images/month): 3600 bonus credits
    """
    if images_per_year == 480:
        product_id = "sub_yearly_480"
    elif images_per_year == 600:
        product_id = "sub_yearly_600"
    elif images_per_year == 720:
        product_id = "sub_yearly_720"
    else:
        raise HTTPException(
            status_code=400,
            detail="Invalid images_per_year. Must be 480, 600, or 720"
        )

    upgrade_request = SubscriptionUpgradeRequest(
        user_id=user_id,
        product_id=product_id,
        duration_days=365,
        platform=SubscriptionPlatformEnum.stripe
    )

    return upgrade_user_subscription(upgrade_request, current_user, session)
