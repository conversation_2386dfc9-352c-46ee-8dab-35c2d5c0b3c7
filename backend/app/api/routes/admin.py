"""
Admin API routes.

This module provides administrative endpoints for managing users,
subscriptions, and system analytics.
"""

import uuid
from typing import Any, Dict
from datetime import datetime, timezone, timedelta

from fastapi import APIRouter, HTTPException, Depends
from sqlmodel import select, func

from app.api.deps import CurrentUser, SessionDep, get_current_active_superuser
from app.models import (
    User, Subscription, UsageRecord, CreditPackage,
    UserSubscriptionStatus, UsageTypeEnum, PlatformEnum
)
from app.services.billing import BillingService
from app.services.usage_tracker import UsageTracker

router = APIRouter(
    prefix="/admin", 
    tags=["admin"],
    dependencies=[Depends(get_current_active_superuser)]
)


@router.get("/dashboard", response_model=Dict[str, Any])
def get_admin_dashboard(session: SessionDep) -> Any:
    """
    Get admin dashboard statistics.
    
    Returns key metrics for system overview.
    """
    # User statistics
    total_users = session.exec(select(func.count()).select_from(User)).one()
    active_users_30d = session.exec(
        select(func.count(func.distinct(UsageRecord.user_id))).where(
            UsageRecord.used_at >= datetime.now(timezone.utc) - timedelta(days=30)
        )
    ).one()
    
    # Subscription statistics
    active_subscriptions = session.exec(
        select(func.count()).select_from(Subscription).where(
            Subscription.is_active == True,
            Subscription.end_date > datetime.now(timezone.utc)
        )
    ).one()
    
    # Usage statistics (last 30 days)
    total_usage_30d = session.exec(
        select(func.sum(UsageRecord.count)).where(
            UsageRecord.used_at >= datetime.now(timezone.utc) - timedelta(days=30)
        )
    ).one() or 0
    
    # Revenue statistics (simplified - based on active subscriptions)
    # In a real system, you'd track actual revenue
    estimated_monthly_revenue = active_subscriptions * 40  # Assuming average $40/month
    
    # Platform distribution
    platform_stats = {}
    for platform in PlatformEnum:
        count = session.exec(
            select(func.count()).select_from(User).where(User.platform == platform)
        ).one()
        platform_stats[platform.value] = count
    
    return {
        "users": {
            "total": total_users,
            "active_30d": active_users_30d,
            "platform_distribution": platform_stats
        },
        "subscriptions": {
            "active": active_subscriptions,
            "estimated_monthly_revenue": estimated_monthly_revenue
        },
        "usage": {
            "total_30d": total_usage_30d,
            "average_per_active_user": round(total_usage_30d / max(active_users_30d, 1), 2)
        },
        "generated_at": datetime.now(timezone.utc).isoformat()
    }


@router.get("/users/{user_id}/status", response_model=Dict[str, Any])
def get_user_admin_status(
    user_id: uuid.UUID,
    session: SessionDep
) -> Any:
    """
    Get comprehensive user status for admin view.
    
    Returns detailed information about user's subscriptions,
    credits, usage patterns, and account status.
    """
    user = session.get(User, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Get subscription status
    billing_service = BillingService(session)
    subscription_status = billing_service.get_user_subscription_status(user_id)
    
    # Get usage statistics
    usage_tracker = UsageTracker(session)
    usage_stats_30d = usage_tracker.get_user_usage_stats(user_id, 30)
    current_month_usage = usage_tracker.get_current_month_usage(user_id)
    
    # Get subscription history
    subscriptions = session.exec(
        select(Subscription).where(Subscription.user_id == user_id)
        .order_by(Subscription.start_date.desc())
    ).all()
    
    # Get credit packages
    credit_packages = session.exec(
        select(CreditPackage).where(CreditPackage.user_id == user_id)
        .order_by(CreditPackage.purchased_at.desc())
    ).all()
    
    return {
        "user": {
            "id": str(user.id),
            "email": user.email,
            "full_name": user.full_name,
            "platform": user.platform,
            "is_active": user.is_active,
            "created_at": user.created_at.isoformat(),
            "last_login": user.last_login.isoformat() if user.last_login else None
        },
        "subscription_status": subscription_status,
        "usage_stats": {
            "last_30_days": usage_stats_30d,
            "current_month": current_month_usage
        },
        "subscriptions": [
            {
                "id": str(sub.id),
                "product_id": sub.product_id,
                "platform": sub.platform,
                "start_date": sub.start_date.isoformat(),
                "end_date": sub.end_date.isoformat(),
                "is_active": sub.is_active,
                "last_verified_at": sub.last_verified_at.isoformat() if sub.last_verified_at else None
            }
            for sub in subscriptions
        ],
        "credit_packages": [
            {
                "id": str(pkg.id),
                "product_id": pkg.product_id,
                "credits": pkg.credits,
                "remaining_credits": pkg.remaining_credits,
                "platform": pkg.platform,
                "purchased_at": pkg.purchased_at.isoformat()
            }
            for pkg in credit_packages
        ]
    }


@router.get("/analytics/usage", response_model=Dict[str, Any])
def get_usage_analytics(
    session: SessionDep,
    days: int = 30
) -> Any:
    """
    Get system-wide usage analytics.
    """
    usage_tracker = UsageTracker(session)
    platform_stats = usage_tracker.get_platform_usage_stats(days)
    top_users = usage_tracker.get_top_users(limit=20, days=days)
    
    # Usage trends by day
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=days)
    
    daily_usage_stmt = select(
        func.date(UsageRecord.used_at).label('usage_date'),
        func.sum(UsageRecord.count).label('daily_count'),
        func.count(func.distinct(UsageRecord.user_id)).label('unique_users')
    ).where(
        UsageRecord.used_at >= start_date,
        UsageRecord.used_at <= end_date
    ).group_by(func.date(UsageRecord.used_at)).order_by('usage_date')
    
    daily_trends = []
    for row in session.exec(daily_usage_stmt):
        daily_trends.append({
            'date': row.usage_date.isoformat(),
            'total_usage': row.daily_count,
            'unique_users': row.unique_users
        })
    
    return {
        "period_days": days,
        "platform_stats": platform_stats,
        "top_users": top_users,
        "daily_trends": daily_trends
    }


@router.get("/analytics/revenue", response_model=Dict[str, Any])
def get_revenue_analytics(
    session: SessionDep,
    days: int = 30
) -> Any:
    """
    Get revenue analytics (simplified version).
    
    In a real system, this would integrate with payment processors
    to get actual revenue data.
    """
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=days)
    
    # New subscriptions in period
    new_subscriptions = session.exec(
        select(func.count()).select_from(Subscription).where(
            Subscription.start_date >= start_date,
            Subscription.start_date <= end_date
        )
    ).one()
    
    # Credit packages purchased in period
    credit_packages_sold = session.exec(
        select(func.count()).select_from(CreditPackage).where(
            CreditPackage.purchased_at >= start_date,
            CreditPackage.purchased_at <= end_date
        )
    ).one()
    
    # Active subscriptions by product
    subscription_breakdown = {}
    for product_id in ["sub_monthly_40", "sub_monthly_60", "sub_yearly_480", "sub_yearly_720"]:
        count = session.exec(
            select(func.count()).select_from(Subscription).where(
                Subscription.product_id == product_id,
                Subscription.is_active == True,
                Subscription.end_date > datetime.now(timezone.utc)
            )
        ).one()
        subscription_breakdown[product_id] = count
    
    return {
        "period_days": days,
        "new_subscriptions": new_subscriptions,
        "credit_packages_sold": credit_packages_sold,
        "active_subscriptions_by_product": subscription_breakdown,
        "estimated_metrics": {
            "note": "These are estimated metrics. Integrate with payment processors for actual revenue data."
        }
    }


@router.post("/users/{user_id}/credits", response_model=Dict[str, Any])
def grant_credits_to_user(
    user_id: uuid.UUID,
    credits_data: Dict[str, Any],
    session: SessionDep
) -> Any:
    """
    Grant credits to a user (admin action).
    
    This can be used for customer support, refunds, or promotional credits.
    """
    user = session.get(User, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    credits = credits_data.get("credits", 0)
    reason = credits_data.get("reason", "Admin grant")
    
    if not isinstance(credits, int) or credits <= 0:
        raise HTTPException(
            status_code=400,
            detail="Credits must be a positive integer"
        )
    
    # Create credit package
    credit_package = CreditPackage(
        user_id=user_id,
        credits=credits,
        remaining_credits=credits,
        product_id="admin_grant",
        platform="stripe"  # Use stripe as default for admin grants
    )
    
    session.add(credit_package)
    session.commit()
    session.refresh(credit_package)
    
    return {
        "success": True,
        "message": f"Granted {credits} credits to user {user.email}",
        "credit_package_id": str(credit_package.id),
        "reason": reason
    }


@router.put("/users/{user_id}/subscription/{subscription_id}", response_model=Dict[str, Any])
def update_user_subscription(
    user_id: uuid.UUID,
    subscription_id: uuid.UUID,
    update_data: Dict[str, Any],
    session: SessionDep
) -> Any:
    """
    Update user subscription (admin action).
    
    This can be used to extend subscriptions, change status, etc.
    """
    subscription = session.get(Subscription, subscription_id)
    if not subscription:
        raise HTTPException(status_code=404, detail="Subscription not found")
    
    if subscription.user_id != user_id:
        raise HTTPException(
            status_code=400,
            detail="Subscription does not belong to specified user"
        )
    
    # Update allowed fields
    allowed_fields = ["end_date", "is_active"]
    updated_fields = []
    
    for field in allowed_fields:
        if field in update_data:
            if field == "end_date":
                # Parse datetime string
                try:
                    end_date = datetime.fromisoformat(update_data[field].replace('Z', '+00:00'))
                    subscription.end_date = end_date
                    updated_fields.append(f"end_date to {end_date}")
                except ValueError:
                    raise HTTPException(
                        status_code=400,
                        detail="Invalid end_date format. Use ISO format."
                    )
            elif field == "is_active":
                subscription.is_active = bool(update_data[field])
                updated_fields.append(f"is_active to {subscription.is_active}")
    
    if updated_fields:
        subscription.last_verified_at = datetime.now(timezone.utc)
        session.add(subscription)
        session.commit()
        session.refresh(subscription)
    
    return {
        "success": True,
        "message": f"Updated subscription: {', '.join(updated_fields)}",
        "subscription": {
            "id": str(subscription.id),
            "end_date": subscription.end_date.isoformat(),
            "is_active": subscription.is_active,
            "last_verified_at": subscription.last_verified_at.isoformat()
        }
    }
