"""
OAuth 第三方登录 API 路由

This module provides endpoints for third-party OAuth authentication.
"""

from typing import Any

from fastapi import APIRouter, HTTPException, Depends
from sqlmodel import Session

from app.api.deps import SessionDep
from app.models import (
    OAuthLoginRequest, OAuthLoginResponse, AuthProviderEnum,
    AppleLoginRequest, Message
)
from app.services.oauth_service import OAuthService

router = APIRouter(prefix="/oauth", tags=["oauth"])


@router.post("/login", response_model=OAuthLoginResponse)
async def oauth_login(
    oauth_request: OAuthLoginRequest,
    session: SessionDep
) -> Any:
    """
    第三方平台OAuth登录
    
    支持的平台：
    - Apple Sign In
    - Google OAuth
    - WeChat Login
    - Facebook Login
    - GitHub OAuth
    
    流程：
    1. 客户端从第三方平台获取access_token
    2. 将access_token发送到此端点
    3. 服务器验证token并获取用户信息
    4. 返回我们系统的JWT token
    """
    try:
        oauth_service = OAuthService(session)
        result = await oauth_service.oauth_login(oauth_request)
        return result
        
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"OAuth login failed: {str(e)}"
        )


@router.get("/providers", response_model=dict)
def get_supported_providers() -> Any:
    """
    获取支持的第三方登录平台列表
    """
    providers = {
        "apple": {
            "name": "Apple Sign In",
            "description": "使用 Apple ID 登录",
            "icon": "apple",
            "supported_platforms": ["ios", "web"],
            "implementation_status": "pending"
        },
        "google": {
            "name": "Google",
            "description": "使用 Google 账户登录",
            "icon": "google",
            "supported_platforms": ["ios", "android", "web"],
            "implementation_status": "implemented"
        },
        "wechat": {
            "name": "微信",
            "description": "使用微信账户登录",
            "icon": "wechat",
            "supported_platforms": ["ios", "android", "web"],
            "implementation_status": "pending"
        },
        "facebook": {
            "name": "Facebook",
            "description": "使用 Facebook 账户登录",
            "icon": "facebook",
            "supported_platforms": ["ios", "android", "web"],
            "implementation_status": "pending"
        },
        "github": {
            "name": "GitHub",
            "description": "使用 GitHub 账户登录",
            "icon": "github",
            "supported_platforms": ["web"],
            "implementation_status": "pending"
        }
    }
    
    return {
        "providers": providers,
        "total_count": len(providers),
        "implemented_count": sum(1 for p in providers.values() if p["implementation_status"] == "implemented")
    }


@router.post("/apple/login", response_model=OAuthLoginResponse)
async def apple_login(
    request: AppleLoginRequest,
    session: SessionDep
) -> Any:
    """
    Apple Sign In 专用登录端点

    接收 Apple Sign In 的 identity token 和可选的用户信息

    请求格式：
    {
        "identity_token": "eyJraWQiOiJVYUlJRlkyZlc0IiwiYWxnIjoiUlMyNTYifQ...",
        "platform": "ios",
        "user_info": {  // 可选，首次授权时提供
            "firstName": "John",
            "lastName": "Doe",
            "email": "<EMAIL>"  // 可选，真实邮箱
        },
        "real_email": "<EMAIL>"  // 可选，客户端获取的真实邮箱
    }
    """
    try:
        oauth_request = OAuthLoginRequest(
            provider=AuthProviderEnum.apple,
            access_token=request.identity_token,
            platform=request.platform,
            device_token=request.device_token
        )

        oauth_service = OAuthService(session)
        result = await oauth_service.oauth_login(oauth_request, request.user_info, request.real_email)
        return result

    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Apple login failed: {str(e)}"
        )


@router.post("/google/login", response_model=OAuthLoginResponse)
async def google_login(
    request: dict,
    session: SessionDep
) -> Any:
    """
    Google OAuth 专用登录端点
    """
    try:
        access_token = request.get("access_token")
        if not access_token:
            raise HTTPException(
                status_code=400,
                detail="access_token is required for Google OAuth"
            )
        
        oauth_request = OAuthLoginRequest(
            provider=AuthProviderEnum.google,
            access_token=access_token,
            platform=request.get("platform", "web"),
            device_token=request.get("device_token")  # 🆕 支持设备token
        )
        
        oauth_service = OAuthService(session)
        result = await oauth_service.oauth_login(oauth_request)
        return result
        
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Google login failed: {str(e)}"
        )


@router.post("/wechat/login", response_model=OAuthLoginResponse)
async def wechat_login(
    request: dict,
    session: SessionDep
) -> Any:
    """
    微信登录专用端点
    """
    try:
        access_token = request.get("access_token")
        if not access_token:
            raise HTTPException(
                status_code=400,
                detail="access_token is required for WeChat login"
            )
        
        oauth_request = OAuthLoginRequest(
            provider=AuthProviderEnum.wechat,
            access_token=access_token,
            platform=request.get("platform", "web"),
            device_token=request.get("device_token")  # 🆕 支持设备token
        )
        
        oauth_service = OAuthService(session)
        result = await oauth_service.oauth_login(oauth_request)
        return result
        
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"WeChat login failed: {str(e)}"
        )


# @router.post("/unlink/{provider}", response_model=Message)
# async def unlink_oauth_provider(
#     provider: AuthProviderEnum,
#     session: SessionDep
# ) -> Any:
#     """
#     解除第三方平台绑定
#
#     注意：如果用户只有第三方登录方式，需要先设置密码才能解绑
#     """
#     # 这里需要实现解绑逻辑
#     # 检查用户是否有其他登录方式
#     # 如果没有密码且只有一个第三方登录，则不允许解绑
#
#     return Message(message=f"OAuth provider {provider} unlink not implemented yet")
