"""
Usage tracking and management API routes.

This module provides endpoints for tracking usage, consuming credits,
and managing user quotas.
"""

import uuid
from typing import Any, Dict

from fastapi import APIRouter, HTTPException
from sqlmodel import select, func

from app.api.deps import CurrentUser, SessionDep
from app.models import (
    UsageRecord, UsageRecordPublic, UsageRecordsPublic,
    UsageRequest, UsageResponse, UsageTypeEnum, Message
)
from app.services.billing import BillingService
from app.services.usage_tracker import UsageTracker

router = APIRouter(prefix="/usage", tags=["usage"])


@router.post("/consume", response_model=UsageResponse)
def consume_usage(
    usage_request: UsageRequest,
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Consume usage quota or credits.
    
    This endpoint should be called when a user actually uses a service
    (e.g., generates an image, exports HD version, etc.).
    
    It will:
    1. Check if user has sufficient quota/credits
    2. Record the usage
    3. Deduct from subscription quota or credits
    4. Return updated status
    """
    billing_service = BillingService(session)
    
    success, message, details = billing_service.consume_usage(
        current_user.id,
        usage_request.usage_type,
        usage_request.count
    )
    
    if not success:
        raise HTTPException(
            status_code=403,
            detail=message
        )
    
    return UsageResponse(
        success=success,
        message=message,
        remaining_credits=details.get("remaining_credits", 0),
        remaining_monthly_usage=details.get("remaining_monthly_usage", 0)
    )


@router.get("/history", response_model=UsageRecordsPublic)
def get_usage_history(
    current_user: CurrentUser,
    session: SessionDep,
    skip: int = 0,
    limit: int = 100,
    usage_type: UsageTypeEnum | None = None
) -> Any:
    """
    Get user's usage history.
    
    Optionally filter by usage type.
    """
    statement = select(UsageRecord).where(
        UsageRecord.user_id == current_user.id
    )
    
    if usage_type:
        statement = statement.where(UsageRecord.usage_type == usage_type)
    
    statement = statement.offset(skip).limit(limit).order_by(UsageRecord.used_at.desc())
    
    usage_records = session.exec(statement).all()
    
    # Get total count
    count_statement = select(func.count()).select_from(UsageRecord).where(
        UsageRecord.user_id == current_user.id
    )
    if usage_type:
        count_statement = count_statement.where(UsageRecord.usage_type == usage_type)
    
    count = session.exec(count_statement).one()
    
    return UsageRecordsPublic(data=usage_records, count=count)


@router.get("/stats", response_model=Dict[str, Any])
def get_usage_stats(
    current_user: CurrentUser,
    session: SessionDep,
    days: int = 30
) -> Any:
    """
    Get usage statistics for the user.
    
    Returns usage patterns, totals by type, and daily breakdown
    for the specified number of days (default: 30).
    """
    usage_tracker = UsageTracker(session)
    return usage_tracker.get_user_usage_stats(current_user.id, days)


@router.get("/current-month", response_model=Dict[str, Any])
def get_current_month_usage(
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Get current month usage summary.
    
    Returns total usage and breakdown by type for the current month.
    This is useful for showing users their progress toward monthly limits.
    """
    usage_tracker = UsageTracker(session)
    return usage_tracker.get_current_month_usage(current_user.id)


@router.get("/limits", response_model=Dict[str, Any])
def get_usage_limits(
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Get user's current usage limits and remaining quota.
    
    Returns information about subscription limits, used quota,
    and available credits.
    """
    billing_service = BillingService(session)
    status = billing_service.get_user_subscription_status(current_user.id)
    
    return {
        "user_id": str(current_user.id),
        "subscription_active": status.has_active_subscription,
        "subscription_product": status.subscription_product_id,
        "monthly_limit": status.monthly_limit,
        "monthly_used": status.monthly_usage_count,
        "monthly_remaining": max(0, status.monthly_limit - status.monthly_usage_count),
        "total_credits": status.total_credits,
        "can_use_service": status.can_use_service
    }


@router.get("/{usage_id}", response_model=UsageRecordPublic)
def get_usage_record(
    usage_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Get specific usage record details.
    
    Users can only access their own usage records.
    """
    usage_record = session.get(UsageRecord, usage_id)
    
    if not usage_record:
        raise HTTPException(
            status_code=404,
            detail="Usage record not found"
        )
    
    if usage_record.user_id != current_user.id:
        raise HTTPException(
            status_code=403,
            detail="Not authorized to access this usage record"
        )
    
    return usage_record


@router.delete("/{usage_id}", response_model=Message)
def delete_usage_record(
    usage_id: uuid.UUID,
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Delete a usage record.
    
    This endpoint is typically used for corrections or refunds.
    Only superusers can delete usage records.
    """
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403,
            detail="Only administrators can delete usage records"
        )
    
    usage_record = session.get(UsageRecord, usage_id)
    
    if not usage_record:
        raise HTTPException(
            status_code=404,
            detail="Usage record not found"
        )
    
    session.delete(usage_record)
    session.commit()
    
    return Message(message="Usage record deleted successfully")


# Batch operations for efficiency
@router.post("/batch-consume", response_model=Dict[str, Any])
def batch_consume_usage(
    usage_requests: list[UsageRequest],
    current_user: CurrentUser,
    session: SessionDep
) -> Any:
    """
    Consume multiple usage items in a single transaction.
    
    This is useful for operations that consume multiple types of quota
    (e.g., generating an image + HD export).
    """
    if len(usage_requests) > 10:
        raise HTTPException(
            status_code=400,
            detail="Maximum 10 usage items per batch request"
        )
    
    billing_service = BillingService(session)
    results = []
    total_success = 0
    
    for usage_request in usage_requests:
        success, message, details = billing_service.consume_usage(
            current_user.id,
            usage_request.usage_type,
            usage_request.count
        )
        
        results.append({
            "usage_type": usage_request.usage_type,
            "count": usage_request.count,
            "success": success,
            "message": message,
            "details": details
        })
        
        if success:
            total_success += 1
        else:
            # If any item fails, we might want to rollback
            # For now, we continue processing
            pass
    
    return {
        "total_requests": len(usage_requests),
        "successful": total_success,
        "failed": len(usage_requests) - total_success,
        "results": results
    }
