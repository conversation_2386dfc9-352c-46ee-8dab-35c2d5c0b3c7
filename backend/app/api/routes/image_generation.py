import logging
import json
import time
from typing import Any, Dict
from fastapi import APIRouter, Depends, HTTPException, Query, Request, Response
from sqlalchemy.orm import Session
from sqlmodel import select, func

from app.api import deps
from app.core.config import settings
from app.models import (
    ImageGenerationR<PERSON>ord, ImageGenerationRecordPublic, ImageGenerationRecordsPublic
)
from app.services.generation_image_service import (
    ImageGenerationRequest,
    ImageGenerationService
)
# 标准响应代码常量
STANDARD_SUCCESS_CODE = 0
STANDARD_ERROR_CODE = 1

# 详细错误代码定义
class ErrorCode:
    SUCCESS = 0                    # 成功
    GENERAL_ERROR = 1             # 通用错误
    INSUFFICIENT_CREDITS = 1001   # 积分不足
    NO_SUBSCRIPTION = 1002        # 无有效订阅
    MONTHLY_LIMIT_REACHED = 1003  # 月度限制已达
    PERMISSION_DENIED = 1004      # 权限被拒绝
    INVALID_PARAMETERS = 2001     # 参数错误
    INVALID_PROMPT = 2002         # 提示词无效
    INVALID_SIZE = 2003           # 尺寸无效
    INVALID_VARIANTS = 2004       # 变体数量无效
    API_TOKEN_ERROR = 3001        # API令牌错误
    API_AUTH_FAILED = 3002        # API认证失败
    API_REQUEST_FAILED = 3003     # API请求失败
    SERVICE_UNAVAILABLE = 3004    # 服务不可用
    SERVICE_MAINTENANCE = 3005    # 服务维护中
    QUEUE_FULL = 3006            # 队列已满
    INTERNAL_ERROR = 5001         # 内部错误

logger = logging.getLogger(__name__)

router = APIRouter()


def generate_mock_record_info(task_id: str) -> Dict[str, Any]:
    """
    生成mock的图片生成记录信息

    Args:
        task_id: 任务ID

    Returns:
        Dict[str, Any]: mock的记录信息数据
    """
    current_time_ms = int(time.time() * 1000)

    return {
        "code": 200,
        "msg": "success",
        "data": {
            "taskId": task_id,  # 使用传入的任务ID
            "paramJson": json.dumps({
                "callBackUrl": f"{settings.IMAGE_GENERATION_CALLBACK_URL}",
                "enableFallback": False,
                "fallbackModel": "FLUX_MAX",
                "fileUrlOrPromptNotEmpty": True,
                "filesUrl": [
                    "https://img-bridal.wenhaofree.com/uploads/image_1_8C839A46-3123-4B32-B4E3-0AA50A091AA8.jpeg"
                ],
                "isEnhance": False,
                "nVariants": 1,
                "nVariantsValid": True,
                "prompt": "Keep the same face from reference image, preserve facial identity, outdoor wedding photography, natural landscape, beautiful scenery, fresh air atmosphere, natural lighting, scenic background, countryside setting, garden wedding, outdoor ceremony, nature-inspired, rustic charm, outdoor romance, natural beauty, landscape wedding",
                "size": "1:1",
                "uploadCn": False
            }),
            "completeTime": current_time_ms,
            "response": {
                "resultUrls": [
                    f"https://tempfile.aiquickdraw.com/s/0f2db4ff915b12f8cd38871d27971d3f_0_1754016809_3965.png"
                ]
            },
            "successFlag": 1,
            "status": "SUCCESS",
            "errorCode": None,
            "errorMessage": None,
            "createTime": current_time_ms - 143000,  # 创建时间比完成时间早143秒
            "progress": "1.00"
        }
    }


def generate_mock_image_generation_response() -> Dict[str, Any]:
    """
    生成mock的图片生成提交响应数据

    Returns:
        Dict[str, Any]: mock的图片生成提交响应数据，格式参考：
        {
          "code": 200,
          "msg": "success",
          "data": {
            "taskId": "test-随机生成"
          }
        }
    """
    import random
    import string

    # 生成随机任务ID，前缀为 "test-"
    random_suffix = ''.join(random.choices(string.ascii_lowercase + string.digits, k=12))
    mock_task_id = f"test-{random_suffix}"

    return {
        "code": 200,
        "msg": "success",
        "data": {
            "taskId": mock_task_id
        }
    }


@router.post("/generate-image")
def submit_image_generation(
    *,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
    request: ImageGenerationRequest,
) -> Dict[str, Any]:
    """
    提交图片生成任务

    需要用户登录才能使用此功能
    此端点会自动检查用户订阅状态和使用次数限制

    ⚠️ **注意**: 这是异步接口，返回任务ID后需要通过回调或状态查询获取结果

    ## 返回格式：

    ### 成功响应 (HTTP 200)
    ```json
    {
        "code": 0,
        "message": "Image generation task submitted successfully",
        "data": {
            "task_id": "img_gen_123456789",
            "generation_record_id": "uuid-string",
            "status": "pending",
            "estimated_completion_time": "30-60 seconds",
            "remaining_credits": 45,
            "remaining_monthly_usage": 35,
            "callback_url": "https://your-callback-url.com/callback"
        }
    }
    ```

    ### 错误响应 (HTTP 4xx/5xx)
    ```json
    {
        "code": 1,
        "message": "错误描述信息",
        "data": {...}
    }
    ```

    ## HTTP状态码说明：
    - **200**: 任务提交成功 (code=0)
    - **400**: 请求参数错误或API调用失败 (code=1)
    - **401**: 用户未登录或token无效 (code=1)
    - **403**: 权限不足（无订阅且积分不够、月度次数用完等） (code=1)
    - **422**: 请求参数验证失败 (code=1)
    - **500**: 服务器内部错误 (code=1)
    - **503**: 图片生成服务不可用 (code=1)

    ## 获取结果的方式：
    1. **回调通知**: 如果提供了callback_url，结果会主动推送
    2. **状态查询**: 使用返回的task_id调用 `/generation-status/{task_id}` 查询结果
    3. **推送通知**: iOS用户会收到APNS推送通知
    """
    try:
        # 记录请求信息
        logger.info(f"Image generation request from user {current_user.id}: "
                   f"prompt='{request.prompt[:50]}...', size={request.size}, "
                   f"n_variants={request.n_variants}, is_enhance={request.is_enhance}")

        if request.files_url:
            logger.info(f"Request includes {len(request.files_url)} reference images")

        # 🆕 Mock模式检查
        if settings.ENABLE_IMAGE_GENERATION_MOCK:
            logger.info(f"Mock mode enabled, returning mock data for user {current_user.id}")
            mock_response = generate_mock_image_generation_response()
            logger.info(f"Successfully returned mock image generation response for user {current_user.id}")
            return mock_response

        # 创建带数据库会话的服务实例
        service = ImageGenerationService(session=db)

        # 调用带用户检查的同步图片生成服务
        result = service.generate_image_sync_with_user_check(current_user.id, request)

        # 记录响应信息
        logger.info(f"Image generation response for user {current_user.id}: "
                   f"success={result.success}, error_code={getattr(result, 'error_code', None)}")

        # 🆕 使用标准化的错误处理
        if not result.success:
            # 使用服务返回的HTTP状态码，如果没有则根据错误类型推断
            status_code = getattr(result, 'http_status_code', None)
            error_code = getattr(result, 'error_code', 'GENERATION_FAILED')
            error_message = result.error or result.message

            # 🆕 根据错误类型确定业务错误代码
            business_error_code = ErrorCode.GENERAL_ERROR  # 默认通用错误

            if error_code == 'INSUFFICIENT_CREDITS':
                business_error_code = ErrorCode.INSUFFICIENT_CREDITS
            elif error_code == 'NO_SUBSCRIPTION_OR_CREDITS':
                business_error_code = ErrorCode.NO_SUBSCRIPTION
            elif error_code == 'MONTHLY_LIMIT_REACHED':
                business_error_code = ErrorCode.MONTHLY_LIMIT_REACHED
            elif error_code == 'PERMISSION_DENIED':
                business_error_code = ErrorCode.PERMISSION_DENIED
            elif error_code == 'INVALID_PROMPT':
                business_error_code = ErrorCode.INVALID_PROMPT
            elif error_code == 'INVALID_SIZE':
                business_error_code = ErrorCode.INVALID_SIZE
            elif error_code == 'INVALID_VARIANTS_COUNT':
                business_error_code = ErrorCode.INVALID_VARIANTS
            elif error_code == 'API_TOKEN_NOT_CONFIGURED':
                business_error_code = ErrorCode.API_TOKEN_ERROR
            elif error_code == 'API_AUTHENTICATION_FAILED':
                business_error_code = ErrorCode.API_AUTH_FAILED
            elif error_code == 'API_REQUEST_FAILED':
                business_error_code = ErrorCode.API_REQUEST_FAILED
            elif error_code == 'SERVICE_UNAVAILABLE':
                business_error_code = ErrorCode.SERVICE_UNAVAILABLE
            elif error_code.startswith('API_ERROR_'):
                # 第三方API错误，根据具体状态码映射
                api_status = error_code.replace('API_ERROR_', '')
                if api_status == '401':
                    business_error_code = ErrorCode.API_AUTH_FAILED
                elif api_status == '402':
                    business_error_code = ErrorCode.INSUFFICIENT_CREDITS
                elif api_status == '422':
                    business_error_code = ErrorCode.INVALID_PARAMETERS
                elif api_status == '429':
                    business_error_code = ErrorCode.API_REQUEST_FAILED
                elif api_status == '455':
                    business_error_code = ErrorCode.SERVICE_MAINTENANCE
                elif api_status == '550':
                    business_error_code = ErrorCode.QUEUE_FULL
                else:
                    business_error_code = ErrorCode.API_REQUEST_FAILED

            if not status_code:
                # 根据错误代码推断HTTP状态码
                if error_code in ['NO_SUBSCRIPTION_OR_CREDITS', 'INSUFFICIENT_CREDITS', 'MONTHLY_LIMIT_REACHED', 'PERMISSION_DENIED']:
                    status_code = 403
                elif error_code in ['API_TOKEN_NOT_CONFIGURED', 'SERVICE_UNAVAILABLE']:
                    status_code = 503
                elif error_code in ['API_AUTHENTICATION_FAILED', 'API_REQUEST_FAILED']:
                    status_code = 400
                elif error_code in ['INVALID_PROMPT', 'INVALID_SIZE', 'INVALID_VARIANTS_COUNT']:
                    status_code = 422
                elif error_code.startswith('API_ERROR_'):
                    # 第三方API错误，状态码已经在服务中设置
                    status_code = getattr(result, 'http_status_code', 500)
                else:
                    status_code = 500

            # 直接返回标准格式的错误响应，不包装在HTTPException中
            error_response = {
                "code": business_error_code,  # 🆕 使用具体的业务错误代码
                "message": error_message,
                "data": result.data or {}
            }

            response = Response(
                content=json.dumps(error_response, ensure_ascii=False),
                status_code=status_code,
                media_type="application/json"
            )
            return response

        # 成功情况 - 返回优化的标准格式
        response_data = result.data or {}

        # 提取task_id
        task_id = None
        if isinstance(response_data, dict):
            # 从API响应中提取task_id
            api_data = response_data.get("data", {})
            if isinstance(api_data, dict):
                task_id = api_data.get("taskId")

        # 构建优化的响应数据
        optimized_data = {
            "task_id": task_id,
            "generation_record_id": response_data.get("generation_record_id"),
            "status": "pending",  # 任务已提交，状态为待处理
            "estimated_completion_time": "30-60 seconds",
            "callback_url": request.callback_url,
            # 保留用户配额信息
            "remaining_credits": response_data.get("remaining_credits", 0),
            "remaining_monthly_usage": response_data.get("remaining_monthly_usage", 0),
            "subscription_status": response_data.get("subscription_status"),
            "trial_message": response_data.get("trial_message"),
            "is_trial_user": response_data.get("is_trial_user", False),
            # 原始API响应（用于调试）
            "api_response": response_data if logger.isEnabledFor(logging.DEBUG) else None
        }

        return {
            "code": ErrorCode.SUCCESS,  # 0表示成功
            "message": "Image generation task submitted successfully",
            "data": optimized_data
        }

    except Exception as e:
        # 未预期的服务器错误 - 500 Internal Server Error
        error_response = {
            "code": ErrorCode.INTERNAL_ERROR,  # 5001表示内部错误
            "message": "An unexpected error occurred while processing your request",
            "data": {"debug_info": str(e) if db else "Debug info unavailable"}
        }

        response = Response(
            content=json.dumps(error_response, ensure_ascii=False),
            status_code=500,
            media_type="application/json"
        )
        return response


@router.get("/generation-status/{task_id}")
def get_generation_status(
    *,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
    task_id: str,
) -> Dict[str, Any]:
    """
    查询图片生成任务状态

    根据task_id查询图片生成任务的当前状态和结果

    Args:
        task_id: 图片生成任务ID

    Returns:
        Dict: 标准响应格式，包含任务状态和结果信息

    ## 返回格式：

    ### 任务进行中 (HTTP 200)
    ```json
    {
        "code": 0,
        "message": "Task is in progress",
        "data": {
            "task_id": "img_gen_123456789",
            "status": "pending",
            "progress": 50,
            "estimated_remaining_time": "15-30 seconds"
        }
    }
    ```

    ### 任务完成 (HTTP 200)
    ```json
    {
        "code": 0,
        "message": "Task completed successfully",
        "data": {
            "task_id": "img_gen_123456789",
            "status": "completed",
            "result_urls": ["https://example.com/image1.jpg"],
            "result_url": "https://example.com/image1.jpg",
            "completed_at": "2025-01-31T12:00:00Z"
        }
    }
    ```

    ### 任务失败 (HTTP 200)
    ```json
    {
        "code": 0,
        "message": "Task failed",
        "data": {
            "task_id": "img_gen_123456789",
            "status": "failed",
            "error_message": "Content policy violation",
            "failed_at": "2025-01-31T12:00:00Z"
        }
    }
    ```
    """
    try:
        logger.info(f"Querying generation status for task_id: {task_id}, user: {current_user.id}")

        # 查找生成记录
        statement = select(ImageGenerationRecord).where(
            ImageGenerationRecord.task_id == task_id,
            ImageGenerationRecord.user_id == current_user.id
        )

        record = db.exec(statement).first()

        if not record:
            # 如果没有找到记录，尝试通过第三方API查询
            service = ImageGenerationService(session=db)
            api_result = service.get_generation_record_info(task_id)

            if not api_result.success:
                return {
                    "code": ErrorCode.GENERAL_ERROR,
                    "message": "Task not found",
                    "data": {"task_id": task_id}
                }

            # 返回API查询结果
            return {
                "code": ErrorCode.SUCCESS,
                "message": "Task status retrieved from API",
                "data": {
                    "task_id": task_id,
                    "status": "unknown",
                    "api_data": api_result.data
                }
            }

        # 构建状态响应
        status_data = {
            "task_id": task_id,
            "generation_record_id": str(record.id),
            "status": record.status,
            "created_at": record.created_at.isoformat(),
            "prompt": record.prompt[:100] + "..." if len(record.prompt) > 100 else record.prompt,
            "size": record.size,
            "n_variants": record.n_variants
        }

        if record.status == "success":
            # 任务完成，返回结果
            status_data.update({
                "result_url": record.result_url,
                "result_urls": json.loads(record.result_urls) if record.result_urls else [],
                "completed_at": record.created_at.isoformat()  # 可以后续添加completed_at字段
            })
            message = "Task completed successfully"

        elif record.status == "failed":
            # 任务失败，返回错误信息
            status_data.update({
                "error_message": record.error_message,
                "failed_at": record.created_at.isoformat()
            })
            message = "Task failed"

        else:
            # 任务进行中
            status_data.update({
                "progress": 50,  # 可以后续实现真实进度
                "estimated_remaining_time": "15-30 seconds"
            })
            message = "Task is in progress"

        return {
            "code": ErrorCode.SUCCESS,
            "message": message,
            "data": status_data
        }

    except Exception as e:
        logger.error(f"Failed to get generation status for task_id {task_id}: {str(e)}")
        error_response = {
            "code": ErrorCode.INTERNAL_ERROR,
            "message": "Failed to retrieve task status",
            "data": {"task_id": task_id, "error": str(e)}
        }

        response = Response(
            content=json.dumps(error_response, ensure_ascii=False),
            status_code=500,
            media_type="application/json"
        )
        return response


@router.post("/generate-image-sync")
def generate_image_sync_deprecated(
    *,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
    request: ImageGenerationRequest,
) -> Dict[str, Any]:
    """
    同步生成图片 (已弃用)

    ⚠️ **此接口已弃用，请使用 `/generate-image` 接口**

    为了向后兼容性保留此接口，但建议迁移到新接口。
    此接口实际上是异步的，返回任务ID而非直接的图片结果。
    """
    logger.warning(f"User {current_user.id} is using deprecated endpoint /generate-image-sync")

    # 调用新的接口实现
    result = submit_image_generation(
        db=db,
        current_user=current_user,
        request=request
    )

    # 为了兼容性，调整响应格式
    if isinstance(result, dict) and result.get("code") == 0:
        # 成功响应，保持原有格式但添加弃用警告
        result["message"] = f"⚠️ DEPRECATED: {result['message']}. Please use /generate-image instead."
        result["data"]["deprecation_notice"] = {
            "message": "This endpoint is deprecated. Please use /generate-image instead.",
            "new_endpoint": "/api/v1/image/generate-image",
            "migration_guide": "https://docs.example.com/migration-guide"
        }

    return result


@router.get("/image-generation/health")
def check_image_generation_health() -> dict:
    """
    检查图片生成服务的健康状态
    """
    try:
        # 创建服务实例检查配置
        service = ImageGenerationService()

        # 检查API配置是否正确
        if not service.api_token:
            return {
                "status": "unhealthy",
                "message": "API token not configured",
                "api_url": service.api_url
            }

        return {
            "status": "healthy",
            "message": "Image generation service is configured",
            "api_url": service.api_url
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"Error checking service health: {str(e)}"
        }


@router.get("/history")
def get_image_generation_history(
    *,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
    skip: int = Query(default=0, ge=0, description="Number of records to skip"),
    limit: int = Query(default=20, ge=1, le=100, description="Number of records to return"),
    status: str = Query(default=None, description="Filter by status (pending, success, failed)"),
) -> Dict[str, Any]:
    """
    获取用户的图片生成历史记录

    支持分页和状态过滤，返回优化的标准格式

    ## 返回格式：
    ```json
    {
        "code": 0,
        "message": "success",
        "data": {
            "records": [...],
            "pagination": {
                "total": 100,
                "count": 20,
                "skip": 0,
                "limit": 20,
                "has_more": true
            },
            "summary": {
                "total_generations": 100,
                "successful": 85,
                "failed": 10,
                "pending": 5
            }
        }
    }
    ```
    """
    try:
        # 构建查询
        statement = select(ImageGenerationRecord).where(
            ImageGenerationRecord.user_id == current_user.id
        )

        # 添加状态过滤
        if status:
            statement = statement.where(ImageGenerationRecord.status == status)

        # 获取总数（用于分页）
        count_statement = select(func.count()).select_from(
            statement.subquery()
        )
        total = db.exec(count_statement).one()

        # 添加排序和分页
        statement = statement.order_by(ImageGenerationRecord.created_at.desc()).offset(skip).limit(limit)

        # 执行查询
        records = db.exec(statement).all()

        # 获取状态统计
        summary_statement = select(
            ImageGenerationRecord.status,
            func.count(ImageGenerationRecord.id).label('count')
        ).where(
            ImageGenerationRecord.user_id == current_user.id
        ).group_by(ImageGenerationRecord.status)

        status_counts = db.exec(summary_statement).all()
        summary = {
            "total_generations": total,
            "successful": 0,
            "failed": 0,
            "pending": 0
        }

        for status_count in status_counts:
            if status_count.status == "success":
                summary["successful"] = status_count.count
            elif status_count.status == "failed":
                summary["failed"] = status_count.count
            elif status_count.status == "pending":
                summary["pending"] = status_count.count

        # 转换记录格式
        formatted_records = []
        for record in records:
            formatted_record = {
                "id": str(record.id),
                "task_id": record.task_id,
                "prompt": record.prompt,
                "size": record.size,
                "n_variants": record.n_variants,
                "status": record.status,
                "created_at": record.created_at.isoformat(),
                "result_url": record.result_url,
                "result_urls": json.loads(record.result_urls) if record.result_urls else [],
                "error_message": record.error_message,
                "is_enhance": record.is_enhance
            }
            formatted_records.append(formatted_record)

        return {
            "code": ErrorCode.SUCCESS,
            "message": "success",
            "data": {
                "records": formatted_records,
                "pagination": {
                    "total": total,
                    "count": len(records),
                    "skip": skip,
                    "limit": limit,
                    "has_more": skip + len(records) < total
                },
                "summary": summary
            }
        }

    except Exception as e:
        logger.error(f"Failed to get image generation history: {str(e)}")
        error_response = {
            "code": ErrorCode.INTERNAL_ERROR,
            "message": "Failed to retrieve image generation history",
            "data": {"error": str(e)}
        }

        response = Response(
            content=json.dumps(error_response, ensure_ascii=False),
            status_code=500,
            media_type="application/json"
        )
        return response


@router.get("/history/{record_id}", response_model=ImageGenerationRecordPublic)
def get_image_generation_record(
    *,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
    record_id: str,
) -> ImageGenerationRecordPublic:
    """
    获取特定的图片生成记录详情
    """
    try:
        # 查询记录
        statement = select(ImageGenerationRecord).where(
            ImageGenerationRecord.id == record_id,
            ImageGenerationRecord.user_id == current_user.id
        )

        record = db.exec(statement).first()

        if not record:
            raise HTTPException(
                status_code=404,
                detail="Generation record not found"
            )

        return ImageGenerationRecordPublic.model_validate(record)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve generation record: {str(e)}"
        )


@router.get("/record-info/{task_id}")
def get_generation_record_info(
    *,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
    task_id: str,
) -> Dict[str, Any]:
    """
    获取图片生成记录详情 - 直接返回第三方API的响应数据

    根据任务ID获取图片生成的详细信息，包括生成状态、结果URL等。
    此接口直接返回第三方API的原始响应数据，不进行额外包装。

    支持Mock模式：当 ENABLE_IMAGE_GENERATION_MOCK=True 时，返回模拟数据。

    Args:
        task_id: 图片生成任务的ID

    Returns:
        Dict[str, Any]: 第三方API的原始响应数据或Mock数据

    Raises:
        HTTPException: 当任务ID无效或获取失败时
    """
    logger.info(f"Fetching generation record info for task_id: {task_id}, user: {current_user.id}")

    # 🆕 Mock模式检查
    if settings.ENABLE_IMAGE_GENERATION_MOCK:
        logger.info(f"Mock mode enabled, returning mock data for task_id: {task_id}")
        mock_data = generate_mock_record_info(task_id)
        logger.info(f"Successfully returned mock record info for task_id: {task_id}")
        return mock_data

    # 创建带数据库会话的服务实例
    service = ImageGenerationService(session=db)

    # 🎯 核心修改：直接返回服务方法的结果（第三方API的原始响应）
    # 服务方法现在会抛出HTTPException而不是返回包装的响应对象
    result = service.get_generation_record_info(task_id)

    logger.info(f"Successfully fetched record info for task_id: {task_id}")

    # 直接返回第三方API的原始响应数据
    return result


@router.post("/callback")
async def image_generation_callback(
    *,
    db: Session = Depends(deps.get_db),
    request: Request,
) -> Dict[str, Any]:
    """
    接收图片生成API的回调通知

    此接口用于接收第三方图片生成API的回调通知，
    处理生成成功或失败的结果，并发送推送通知给用户。

    不需要用户认证，因为这是第三方API的回调。

    返回统一的标准响应格式，包含错误代码和详细信息。
    """
    import uuid
    from datetime import datetime

    # 生成请求追踪信息
    request_id = str(uuid.uuid4())
    timestamp = datetime.now().isoformat()

    def create_error_response(error_code: str, message: str, status_code: int = 500):
        """创建标准错误响应"""
        return {
            "success": False,
            "error_code": error_code,
            "message": message,
            "timestamp": timestamp,
            "request_id": request_id
        }, status_code

    def create_success_response(data: dict):
        """创建标准成功响应"""
        return {
            "success": True,
            "message": "Callback processed successfully",
            "data": data,
            "timestamp": timestamp,
            "request_id": request_id
        }

    try:
        # 获取回调数据
        try:
            callback_data = await request.json()
        except Exception as e:
            logger.error(f"[{request_id}] Failed to parse JSON: {str(e)}")
            error_response, status_code = create_error_response(
                "CALLBACK_INVALID_JSON",
                f"Invalid JSON format: {str(e)}",
                400
            )
            raise HTTPException(status_code=status_code, detail=error_response)

        # 导入彩色日志工具
        from backend.app.services.generation_image_service import ColoredLogger

        # 🎨 彩色日志：接收回调
        ColoredLogger.log_service_info(
            f"Received image generation callback [{request_id}]",
            callback_data
        )

        # 基本数据验证
        if not isinstance(callback_data, dict):
            error_response, status_code = create_error_response(
                "CALLBACK_INVALID_DATA",
                "Callback data must be a JSON object",
                400
            )
            raise HTTPException(status_code=status_code, detail=error_response)

        # 验证必需字段
        if "code" not in callback_data:
            error_response, status_code = create_error_response(
                "CALLBACK_INVALID_DATA",
                "Missing required field: 'code'",
                400
            )
            raise HTTPException(status_code=status_code, detail=error_response)

        if "data" not in callback_data:
            error_response, status_code = create_error_response(
                "CALLBACK_INVALID_DATA",
                "Missing required field: 'data'",
                400
            )
            raise HTTPException(status_code=status_code, detail=error_response)

        data = callback_data.get("data", {})
        if not isinstance(data, dict) or "taskId" not in data:
            error_response, status_code = create_error_response(
                "CALLBACK_INVALID_DATA",
                "Missing required field: 'data.taskId'",
                400
            )
            raise HTTPException(status_code=status_code, detail=error_response)

        # 导入并使用回调服务
        try:
            from app.services.image_callback_service import ImageCallbackService
            callback_service = ImageCallbackService(db)
            result = await callback_service.handle_callback(callback_data)
        except Exception as e:
            logger.error(f"[{request_id}] Callback service error: {str(e)}")
            error_response, status_code = create_error_response(
                "CALLBACK_PROCESSING_FAILED",
                f"Failed to process callback: {str(e)}",
                500
            )
            raise HTTPException(status_code=status_code, detail=error_response)

        # 处理结果
        if result.get("success"):
            task_id = data.get("taskId", "")
            success_data = {
                "task_id": task_id,
                "record_id": result.get("record_id", ""),
                "status": result.get("status", "success"),
                "result_urls": result.get("result_urls", [])
            }

            # 🎨 彩色日志：回调处理成功
            ColoredLogger.log_service_info(
                f"Callback processed successfully [{request_id}]",
                success_data
            )

            return create_success_response(success_data)
        else:
            error_detail = result.get('error', 'Unknown error')

            # 🎨 彩色日志：回调处理失败
            ColoredLogger.log_service_error(
                f"Callback processing failed [{request_id}]",
                data={
                    "error": error_detail,
                    "task_id": data.get("taskId", "unknown"),
                    "callback_data": callback_data
                }
            )

            # 根据错误类型返回不同状态码
            if "not found" in error_detail.lower():
                error_code = "CALLBACK_TASK_NOT_FOUND"
                status_code = 404
                task_id = data.get("taskId", "unknown")
                message = f"Generation record not found for task ID: {task_id}"
            else:
                error_code = "CALLBACK_PROCESSING_FAILED"
                status_code = 500
                message = f"Failed to process callback: {error_detail}"

            error_response, _ = create_error_response(error_code, message, status_code)
            raise HTTPException(status_code=status_code, detail=error_response)

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 捕获所有其他异常
        logger.error(f"[{request_id}] Unexpected error: {str(e)}", exc_info=True)
        error_response, status_code = create_error_response(
            "INTERNAL_ERROR",
            f"An unexpected error occurred: {str(e)}",
            500
        )
        raise HTTPException(status_code=status_code, detail=error_response)
