"""
图片上传API路由

提供图片上传到Cloudflare R2的API端点
"""

import logging
from typing import Any, Optional

from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel

from app.api.deps import CurrentUser, SessionDep
from app.models import ImageUploadRecord, ImageUploadRecordCreate
from app.services.image_upload_r2 import (
    CloudflareR2Service,
    ImageUploadRequest,
    ImageUploadResponse,
    get_r2_service
)

logger = logging.getLogger(__name__)

router = APIRouter()


class ImageUploadFromUrlRequest(BaseModel):
    """从URL上传图片的请求模型"""
    image_url: str
    file_name: Optional[str] = None
    folder: str = "uploads"
    content_type: Optional[str] = None


class ImageDeleteRequest(BaseModel):
    """删除图片的请求模型"""
    object_name: str


@router.post("/upload", response_model=ImageUploadResponse)
def upload_image_file(
    current_user: CurrentUser,
    session: SessionDep,
    file: UploadFile = File(...),
    folder: str = Form("uploads"),
    custom_name: Optional[str] = Form(None)
) -> Any:
    """
    上传图片文件到Cloudflare R2
    
    ## 功能说明
    - 支持多种图片格式：JPEG, PNG, GIF, WebP, BMP
    - 自动检测文件类型和内容类型
    - 支持自定义文件名和存储文件夹
    - 返回公共访问URL
    
    ## 参数说明
    - **file**: 要上传的图片文件
    - **folder**: 存储文件夹，默认为"uploads"
    - **custom_name**: 可选的自定义文件名
    
    ## 返回结果
    - **success**: 是否成功
    - **message**: 操作消息
    - **url**: 图片的公共访问URL（成功时）
    - **data**: 详细信息（对象名称、内容类型、大小等）
    - **error**: 错误信息（失败时）
    
    ## 错误情况
    - 文件类型不支持
    - 文件过大
    - R2配置错误
    - 网络错误
    """
    try:
        logger.info(f"Image upload request from user {current_user.id}: "
                   f"filename={file.filename}, size={file.size}, "
                   f"content_type={file.content_type}")

        # 检查文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(
                status_code=400,
                detail="Only image files are allowed"
            )

        # 检查文件大小（限制为10MB）
        if file.size and file.size > 10 * 1024 * 1024:
            raise HTTPException(
                status_code=400,
                detail="File size too large. Maximum size is 10MB"
            )

        # 读取文件数据
        file_data = file.file.read()
        
        # 准备上传请求
        upload_request = ImageUploadRequest(
            file_name=custom_name or file.filename,
            folder=folder,
            content_type=file.content_type
        )

        # 执行上传
        service = get_r2_service()
        result = service.upload_image_from_bytes(file_data, upload_request)

        if result.success:
            logger.info(f"Image uploaded successfully for user {current_user.id}: {result.url}")

            # 保存上传记录到数据库
            try:
                upload_record = ImageUploadRecordCreate(
                    user_id=current_user.id,
                    file_name=result.data.get("object_name", custom_name or file.filename) if result.data else (custom_name or file.filename),
                    original_name=file.filename,
                    file_size=file.size,
                    content_type=file.content_type,
                    folder=folder,
                    object_name=result.data.get("object_name", "") if result.data else "",
                    url=result.url,
                    upload_source="file"
                )

                db_record = ImageUploadRecord.model_validate(upload_record)
                session.add(db_record)
                session.commit()
                session.refresh(db_record)

                logger.info(f"Image upload record saved to database: {db_record.id}")

            except Exception as db_error:
                logger.error(f"Failed to save upload record to database: {str(db_error)}", exc_info=True)
                # 不影响上传结果，只记录错误

        else:
            logger.error(f"Image upload failed for user {current_user.id}: {result.error}")

        return result

    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"Unexpected error during image upload: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=error_msg)


@router.post("/upload-from-url", response_model=ImageUploadResponse)
def upload_image_from_url(
    current_user: CurrentUser,
    session: SessionDep,
    request: ImageUploadFromUrlRequest
) -> Any:
    """
    从URL下载图片并上传到Cloudflare R2
    
    ## 功能说明
    - 从指定URL下载图片
    - 自动检测图片格式和内容类型
    - 支持自定义文件名和存储文件夹
    - 返回公共访问URL
    
    ## 参数说明
    - **image_url**: 图片的URL地址
    - **file_name**: 可选的自定义文件名
    - **folder**: 存储文件夹，默认为"uploads"
    - **content_type**: 可选的内容类型
    
    ## 返回结果
    - **success**: 是否成功
    - **message**: 操作消息
    - **url**: 图片的公共访问URL（成功时）
    - **data**: 详细信息（对象名称、内容类型、大小等）
    - **error**: 错误信息（失败时）
    
    ## 错误情况
    - URL无效或无法访问
    - 下载的文件不是图片
    - 文件过大
    - R2配置错误
    - 网络错误
    """
    try:
        logger.info(f"Image upload from URL request from user {current_user.id}: "
                   f"url={request.image_url}")

        # 执行上传
        service = get_r2_service()
        upload_request = ImageUploadRequest(
            file_name=request.file_name,
            folder=request.folder,
            content_type=request.content_type
        )

        result = service.upload_image_from_url(request.image_url, upload_request)

        if result.success:
            logger.info(f"Image uploaded from URL successfully for user {current_user.id}: {result.url}")

            # 保存上传记录到数据库
            try:
                upload_record = ImageUploadRecordCreate(
                    user_id=current_user.id,
                    file_name=result.data.get("object_name", request.file_name) if result.data else (request.file_name or "unknown"),
                    original_name=request.file_name,
                    file_size=result.data.get("size") if result.data else None,
                    content_type=result.data.get("content_type", request.content_type) if result.data else request.content_type,
                    folder=request.folder,
                    object_name=result.data.get("object_name", "") if result.data else "",
                    url=result.url,
                    upload_source="url"
                )

                db_record = ImageUploadRecord.model_validate(upload_record)
                session.add(db_record)
                session.commit()
                session.refresh(db_record)

                logger.info(f"Image upload record saved to database: {db_record.id}")

            except Exception as db_error:
                logger.error(f"Failed to save upload record to database: {str(db_error)}", exc_info=True)
                # 不影响上传结果，只记录错误

        else:
            logger.error(f"Image upload from URL failed for user {current_user.id}: {result.error}")

        return result

    except Exception as e:
        error_msg = f"Unexpected error during image upload from URL: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=error_msg)


@router.delete("/delete", response_model=ImageUploadResponse)
def delete_image(
    current_user: CurrentUser,
    request: ImageDeleteRequest
) -> Any:
    """
    从Cloudflare R2删除图片
    
    ## 功能说明
    - 从R2存储中删除指定的图片
    - 需要提供完整的对象名称（包括文件夹路径）
    
    ## 参数说明
    - **object_name**: R2中的对象名称（如：uploads/image.jpg）
    
    ## 返回结果
    - **success**: 是否成功
    - **message**: 操作消息
    - **data**: 详细信息（对象名称）
    - **error**: 错误信息（失败时）
    
    ## 错误情况
    - 对象不存在
    - 权限不足
    - R2配置错误
    - 网络错误
    """
    try:
        logger.info(f"Image delete request from user {current_user.id}: "
                   f"object_name={request.object_name}")

        # 执行删除
        service = get_r2_service()
        result = service.delete_image(request.object_name)

        if result.success:
            logger.info(f"Image deleted successfully for user {current_user.id}: {request.object_name}")
        else:
            logger.error(f"Image delete failed for user {current_user.id}: {result.error}")

        return result

    except Exception as e:
        error_msg = f"Unexpected error during image delete: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise HTTPException(status_code=500, detail=error_msg)


@router.get("/test-config")
def test_r2_config(current_user: CurrentUser) -> Any:
    """
    测试Cloudflare R2配置
    
    ## 功能说明
    - 验证R2服务配置是否正确
    - 检查必要的环境变量是否设置
    - 返回配置状态信息
    
    ## 返回结果
    - **success**: 配置是否正确
    - **message**: 配置状态消息
    - **data**: 配置详情（不包含敏感信息）
    """
    try:
        logger.info(f"R2 config test request from user {current_user.id}")
        
        service = get_r2_service()
        
        return {
            "success": True,
            "message": "R2 configuration is valid",
            "data": {
                "bucket": service.bucket,
                "endpoint": service.endpoint,
                "has_public_domain": bool(service.public_domain),
                "public_domain": service.public_domain if service.public_domain else None
            }
        }
        
    except ValueError as e:
        logger.error(f"R2 configuration error: {str(e)}")
        return {
            "success": False,
            "message": "R2 configuration is invalid",
            "error": str(e)
        }
    except Exception as e:
        error_msg = f"Unexpected error testing R2 config: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return {
            "success": False,
            "message": "Error testing R2 configuration",
            "error": error_msg
        }
