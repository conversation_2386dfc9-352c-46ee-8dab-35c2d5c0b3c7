"""
Device Token management API routes.
"""

import logging
from typing import Any

from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session

from app.api import deps
from app.models import (
    DeviceTokenRegisterRequest,
    DeviceTokenRegisterResponse,
    DeviceTokensPublic,
    DeviceTokenPublic,
    PlatformEnum,
    Message
)
from app.services.device_token_service import DeviceTokenService

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/register", response_model=DeviceTokenRegisterResponse)
def register_device_token(
    *,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
    request: DeviceTokenRegisterRequest,
) -> DeviceTokenRegisterResponse:
    """
    注册设备Token用于推送通知
    
    用户登录后调用此接口注册设备Token，用于接收推送通知。
    如果设备Token已存在，将更新为活跃状态。
    """
    try:
        logger.info(f"Registering device token for user {current_user.id}, platform: {request.platform}")
        
        service = DeviceTokenService(db)
        result = service.register_device_token(current_user.id, request)
        
        logger.info(f"Device token registration result for user {current_user.id}: {result.success}")
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to register device token for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to register device token: {str(e)}"
        )


@router.delete("/deactivate")
def deactivate_device_token(
    *,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
    device_token: str,
) -> Message:
    """
    停用设备Token
    
    当用户登出或不再需要接收推送通知时调用此接口。
    """
    try:
        logger.info(f"Deactivating device token for user {current_user.id}")
        
        service = DeviceTokenService(db)
        success, message = service.deactivate_device_token(current_user.id, device_token)
        
        if not success:
            raise HTTPException(status_code=400, detail=message)
        
        logger.info(f"Device token deactivated for user {current_user.id}")
        return Message(message=message)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to deactivate device token for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to deactivate device token: {str(e)}"
        )


@router.get("/list", response_model=DeviceTokensPublic)
def list_device_tokens(
    *,
    db: Session = Depends(deps.get_db),
    current_user: Any = Depends(deps.get_current_active_user),
    platform: PlatformEnum | None = None,
    active_only: bool = True,
) -> DeviceTokensPublic:
    """
    获取用户的设备Token列表
    
    Args:
        platform: 平台过滤（可选）
        active_only: 是否只返回活跃的token
    """
    try:
        logger.info(f"Listing device tokens for user {current_user.id}")
        
        service = DeviceTokenService(db)
        tokens = service.get_user_device_tokens(
            current_user.id,
            platform=platform,
            active_only=active_only
        )
        
        # 转换为公开模型
        public_tokens = [
            DeviceTokenPublic(
                id=token.id,
                user_id=token.user_id,
                device_token=token.device_token,
                platform=token.platform,
                is_active=token.is_active,
                created_at=token.created_at,
                updated_at=token.updated_at
            )
            for token in tokens
        ]
        
        logger.info(f"Found {len(public_tokens)} device tokens for user {current_user.id}")
        
        return DeviceTokensPublic(
            data=public_tokens,
            count=len(public_tokens)
        )
        
    except Exception as e:
        logger.error(f"Failed to list device tokens for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list device tokens: {str(e)}"
        )


@router.get("/health")
def check_device_token_health() -> dict:
    """
    检查设备Token服务的健康状态
    """
    try:
        from app.services.apns_service import apns_service
        
        apns_configured = apns_service.is_configured()
        
        return {
            "status": "healthy" if apns_configured else "warning",
            "message": "Device token service is running",
            "apns_configured": apns_configured,
            "apns_sandbox": apns_service.use_sandbox if apns_configured else None
        }
        
    except Exception as e:
        return {
            "status": "error",
            "message": f"Error checking service health: {str(e)}"
        }
