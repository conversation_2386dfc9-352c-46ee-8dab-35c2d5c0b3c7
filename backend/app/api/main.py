from fastapi import APIRouter

from app.api.routes import items, login, private, users, utils, subscriptions, usage, credits, admin, oauth, image_generation, image_upload, device_tokens
from app.core.config import settings

api_router = APIRouter()
api_router.include_router(login.router)
api_router.include_router(users.router)
api_router.include_router(utils.router)
api_router.include_router(items.router)

# OAuth third-party login routes
api_router.include_router(oauth.router)

# Subscription system routes
api_router.include_router(subscriptions.router)
api_router.include_router(usage.router)
api_router.include_router(credits.router)
api_router.include_router(admin.router)

# Image generation routes
api_router.include_router(image_generation.router, prefix="/image", tags=["image-generation"])

# Image upload routes
api_router.include_router(image_upload.router, prefix="/image", tags=["image-upload"])

# Device token routes (for push notifications)
api_router.include_router(device_tokens.router, prefix="/device-tokens", tags=["device-tokens"])

if settings.ENVIRONMENT == "local":
    api_router.include_router(private.router)
