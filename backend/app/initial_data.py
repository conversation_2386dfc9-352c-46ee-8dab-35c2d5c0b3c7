import logging
import uuid
from datetime import datetime, timezone, timedelta

from sqlmodel import Session, select

from app import crud
from app.core.config import settings
from app.core.db import engine, init_db
from app.models import (
    User, UserCreate, Subscription, CreditPackage, UsageRecord,
    SubscriptionPlatformEnum, UsageTypeEnum, PlatformEnum
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def validate_configuration() -> None:
    """Validate that required configuration is present."""
    required_settings = [
        ("FIRST_SUPERUSER", settings.FIRST_SUPERUSER),
        ("FIRST_SUPERUSER_PASSWORD", settings.FIRST_SUPERUSER_PASSWORD),
        ("POSTGRES_SERVER", settings.POSTGRES_SERVER),
        ("POSTGRES_USER", settings.POSTGRES_USER),
        ("POSTGRES_DB", settings.POSTGRES_DB),
    ]

    missing_settings = []
    for setting_name, setting_value in required_settings:
        if not setting_value:
            missing_settings.append(setting_name)

    if missing_settings:
        raise ValueError(f"Missing required configuration: {', '.join(missing_settings)}")

    logger.info("Configuration validation passed")


def create_demo_users(session: Session) -> None:
    """Create demo users with different subscription states."""
    logger.info("Creating demo users...")

    # Demo user with active subscription
    demo_user_email = "<EMAIL>"
    demo_user = session.exec(
        select(User).where(User.email == demo_user_email)
    ).first()

    if not demo_user:
        demo_user_in = UserCreate(
            email=demo_user_email,
            password="demopassword123",
            full_name="Demo User",
        )
        demo_user = crud.create_user(session=session, user_create=demo_user_in)
        logger.info(f"Created demo user: {demo_user_email}")

        # Create active subscription for demo user
        now = datetime.now(timezone.utc)
        demo_subscription = Subscription(
            user_id=demo_user.id,
            product_id="sub_monthly_40",
            platform=SubscriptionPlatformEnum.apple,
            start_date=now - timedelta(days=10),
            end_date=now + timedelta(days=20),
            is_active=True,
            original_transaction_id=f"demo_transaction_{uuid.uuid4().hex[:8]}"
        )
        session.add(demo_subscription)
        logger.info("Created active subscription for demo user")

        # Add some usage history
        for i in range(5):
            usage_record = UsageRecord(
                user_id=demo_user.id,
                usage_type=UsageTypeEnum.image_generation,
                count=1,
                used_at=now - timedelta(days=i)
            )
            session.add(usage_record)
        logger.info("Created usage history for demo user")

    # Demo user with credits only
    credits_user_email = "<EMAIL>"
    credits_user = session.exec(
        select(User).where(User.email == credits_user_email)
    ).first()

    if not credits_user:
        credits_user_in = UserCreate(
            email=credits_user_email,
            password="creditspassword123",
            full_name="Credits User",
        )
        credits_user = crud.create_user(session=session, user_create=credits_user_in)
        logger.info(f"Created credits user: {credits_user_email}")

        # Create credit package for credits user
        credit_package = CreditPackage(
            user_id=credits_user.id,
            credits=50,
            remaining_credits=35,
            product_id="credits_pack_50",
            platform=SubscriptionPlatformEnum.stripe
        )
        session.add(credit_package)
        logger.info("Created credit package for credits user")

        # Add some usage history using credits
        for i in range(3):
            usage_record = UsageRecord(
                user_id=credits_user.id,
                usage_type=UsageTypeEnum.hd_export,
                count=1,
                used_at=datetime.now(timezone.utc) - timedelta(days=i)
            )
            session.add(usage_record)
        logger.info("Created usage history for credits user")

    session.commit()


def create_expired_subscription_demo(session: Session) -> None:
    """Create demo user with expired subscription."""
    logger.info("Creating expired subscription demo...")

    expired_user_email = "<EMAIL>"
    expired_user = session.exec(
        select(User).where(User.email == expired_user_email)
    ).first()

    if not expired_user:
        expired_user_in = UserCreate(
            email=expired_user_email,
            password="expiredpassword123",
            full_name="Expired User",
        )
        expired_user = crud.create_user(session=session, user_create=expired_user_in)
        logger.info(f"Created expired user: {expired_user_email}")

        # Create expired subscription
        now = datetime.now(timezone.utc)
        expired_subscription = Subscription(
            user_id=expired_user.id,
            product_id="sub_monthly_60",
            platform=SubscriptionPlatformEnum.google,
            start_date=now - timedelta(days=60),
            end_date=now - timedelta(days=5),
            is_active=False,
            original_transaction_id=f"expired_transaction_{uuid.uuid4().hex[:8]}"
        )
        session.add(expired_subscription)
        logger.info("Created expired subscription")

    # 移动 commit 到函数外部，保持一致性
    session.commit()


def init() -> None:
    try:
        # Validate configuration before proceeding
        validate_configuration()

        with Session(engine) as session:
            # Initialize basic database (creates superuser)
            init_db(session)

            # Create demo data for development/testing
            if settings.ENVIRONMENT == "local":
                create_demo_users(session)
                create_expired_subscription_demo(session)

            # 确保所有更改都被提交
            session.commit()
            logger.info("Database initialization completed successfully")

    except Exception as e:
        logger.error(f"Error during database initialization: {e}")
        raise


def print_initial_data_summary(session: Session) -> None:
    """Print summary of created initial data."""
    logger.info("=== Initial Data Summary ===")

    # Count users
    total_users = len(session.exec(select(User)).all())
    logger.info(f"Total users: {total_users}")

    # Count subscriptions
    total_subscriptions = len(session.exec(select(Subscription)).all())
    active_subscriptions = len(session.exec(
        select(Subscription).where(Subscription.is_active == True)
    ).all())
    logger.info(f"Total subscriptions: {total_subscriptions} (Active: {active_subscriptions})")

    # Count credit packages
    total_credit_packages = len(session.exec(select(CreditPackage)).all())
    logger.info(f"Total credit packages: {total_credit_packages}")

    # Count usage records
    total_usage_records = len(session.exec(select(UsageRecord)).all())
    logger.info(f"Total usage records: {total_usage_records}")

    if settings.ENVIRONMENT == "local":
        logger.info("\n=== Demo Accounts (Development Only) ===")
        logger.info("1. Superuser:")
        logger.info(f"   Email: {settings.FIRST_SUPERUSER}")
        logger.info(f"   Password: {settings.FIRST_SUPERUSER_PASSWORD}")
        logger.info("   Role: Administrator")

        logger.info("2. Demo User (Active Subscription):")
        logger.info("   Email: <EMAIL>")
        logger.info("   Password: demopassword123")
        logger.info("   Status: Active monthly subscription (40 images/month)")

        logger.info("3. Credits User (Credits Only):")
        logger.info("   Email: <EMAIL>")
        logger.info("   Password: creditspassword123")
        logger.info("   Status: 35 credits remaining")

        logger.info("4. Expired User (Expired Subscription):")
        logger.info("   Email: <EMAIL>")
        logger.info("   Password: expiredpassword123")
        logger.info("   Status: Expired subscription")

        logger.info("\n=== API Endpoints ===")
        logger.info("Subscription Management:")
        logger.info("  GET /api/v1/subscriptions/status - Check subscription status")
        logger.info("  GET /api/v1/subscriptions/check-access - Check service access")
        logger.info("  POST /api/v1/subscriptions/apple/validate - Validate Apple receipt")
        logger.info("  POST /api/v1/subscriptions/google/validate - Validate Google subscription")

        logger.info("Usage Management:")
        logger.info("  POST /api/v1/usage/consume - Consume usage quota")
        logger.info("  GET /api/v1/usage/history - Get usage history")
        logger.info("  GET /api/v1/usage/stats - Get usage statistics")
        logger.info("  GET /api/v1/usage/limits - Get usage limits")

        logger.info("Credits Management:")
        logger.info("  GET /api/v1/credits/balance - Get credit balance")
        logger.info("  GET /api/v1/credits/packages - Get credit packages")
        logger.info("  POST /api/v1/credits/purchase - Record credit purchase")
        logger.info("  GET /api/v1/credits/pricing - Get credit pricing")

        logger.info("Admin Management:")
        logger.info("  GET /api/v1/admin/dashboard - Admin dashboard")
        logger.info("  GET /api/v1/admin/users/{user_id}/status - User admin status")
        logger.info("  GET /api/v1/admin/analytics/usage - Usage analytics")
        logger.info("  GET /api/v1/admin/analytics/revenue - Revenue analytics")

    logger.info("=== End Summary ===")


def main() -> None:
    logger.info("Creating initial data")
    init()

    # Print summary of created data
    with Session(engine) as session:
        print_initial_data_summary(session)

    logger.info("Initial data created successfully!")


if __name__ == "__main__":
    main()
