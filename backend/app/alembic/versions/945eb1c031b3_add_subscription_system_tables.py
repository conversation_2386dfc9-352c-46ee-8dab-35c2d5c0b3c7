"""Add subscription system tables

Revision ID: 945eb1c031b3
Revises: ba338bfbebd6
Create Date: 2025-07-29 15:20:35.856480

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '945eb1c031b3'
down_revision = 'ba338bfbebd6'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # Create enum types first
    op.execute("CREATE TYPE platformenum AS ENUM ('ios', 'android', 'web')")
    op.execute("CREATE TYPE subscriptionplatformenum AS ENUM ('apple', 'google', 'stripe')")
    op.execute("CREATE TYPE usagetypeenum AS ENUM ('image_generation', 'hd_export', 'premium_filter')")

    # Add new columns to user table
    op.add_column('user', sa.Column('platform', sa.Enum('ios', 'android', 'web', name='platformenum'), nullable=False, server_default='web'))
    op.add_column('user', sa.Column('created_at', sa.DateTime(), nullable=False, server_default=sa.text('CURRENT_TIMESTAMP')))
    op.add_column('user', sa.Column('last_login', sa.DateTime(), nullable=True))

    # Create subscription table
    op.create_table('subscription',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('product_id', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
        sa.Column('platform', sa.Enum('apple', 'google', 'stripe', name='subscriptionplatformenum'), nullable=False),
        sa.Column('start_date', sa.DateTime(), nullable=False),
        sa.Column('end_date', sa.DateTime(), nullable=False),
        sa.Column('is_active', sa.Boolean(), nullable=False),
        sa.Column('original_transaction_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
        sa.Column('last_verified_at', sa.DateTime(), nullable=True),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_subscription_original_transaction_id'), 'subscription', ['original_transaction_id'], unique=False)
    op.create_index(op.f('ix_subscription_product_id'), 'subscription', ['product_id'], unique=False)

    # Create usage_record table
    op.create_table('usagerecord',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('used_at', sa.DateTime(), nullable=False),
        sa.Column('usage_type', sa.Enum('image_generation', 'hd_export', 'premium_filter', name='usagetypeenum'), nullable=False),
        sa.Column('count', sa.Integer(), nullable=False),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )

    # Create credit_package table
    op.create_table('creditpackage',
        sa.Column('id', sa.UUID(), nullable=False),
        sa.Column('credits', sa.Integer(), nullable=False),
        sa.Column('remaining_credits', sa.Integer(), nullable=False),
        sa.Column('purchased_at', sa.DateTime(), nullable=False),
        sa.Column('product_id', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
        sa.Column('platform', sa.Enum('apple', 'google', 'stripe', name='subscriptionplatformenum'), nullable=False),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_creditpackage_product_id'), 'creditpackage', ['product_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###

    # Drop tables in reverse order
    op.drop_index(op.f('ix_creditpackage_product_id'), table_name='creditpackage')
    op.drop_table('creditpackage')
    op.drop_table('usagerecord')
    op.drop_index(op.f('ix_subscription_product_id'), table_name='subscription')
    op.drop_index(op.f('ix_subscription_original_transaction_id'), table_name='subscription')
    op.drop_table('subscription')

    # Remove columns from user table
    op.drop_column('user', 'last_login')
    op.drop_column('user', 'created_at')
    op.drop_column('user', 'platform')

    # Drop enums
    op.execute('DROP TYPE IF EXISTS usagetypeenum')
    op.execute('DROP TYPE IF EXISTS subscriptionplatformenum')
    op.execute('DROP TYPE IF EXISTS platformenum')

    # ### end Alembic commands ###
