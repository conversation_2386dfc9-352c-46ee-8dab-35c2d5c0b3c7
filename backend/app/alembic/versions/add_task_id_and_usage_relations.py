"""add_task_id_and_usage_relations

Revision ID: add_task_id_and_usage_relations
Revises: 985da56d273b
Create Date: 2025-01-31 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'add_task_id_and_usage_relations'
down_revision = '985da56d273b'
branch_labels = None
depends_on = None


def upgrade():
    """Add task_id to ImageGenerationRecord and relation fields to UsageRecord"""
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Add task_id column to imagegenerationrecord table
    op.add_column('imagegenerationrecord', 
                  sa.Column('task_id', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True))
    
    # Create index on task_id for better query performance
    op.create_index(op.f('ix_imagegenerationrecord_task_id'), 'imagegenerationrecord', ['task_id'], unique=False)
    
    # Add relation fields to usagerecord table
    op.add_column('usagerecord', 
                  sa.Column('related_record_id', sa.Uuid(), nullable=True))
    op.add_column('usagerecord', 
                  sa.Column('related_record_type', sqlmodel.sql.sqltypes.AutoString(length=50), nullable=True))
    
    # Add foreign key constraint for related_record_id
    op.create_foreign_key('fk_usagerecord_related_record_id', 'usagerecord', 'imagegenerationrecord', 
                         ['related_record_id'], ['id'], ondelete='SET NULL')
    
    # ### end Alembic commands ###


def downgrade():
    """Remove task_id from ImageGenerationRecord and relation fields from UsageRecord"""
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Drop foreign key constraint
    op.drop_constraint('fk_usagerecord_related_record_id', 'usagerecord', type_='foreignkey')
    
    # Drop columns from usagerecord
    op.drop_column('usagerecord', 'related_record_type')
    op.drop_column('usagerecord', 'related_record_id')
    
    # Drop index and column from imagegenerationrecord
    op.drop_index(op.f('ix_imagegenerationrecord_task_id'), table_name='imagegenerationrecord')
    op.drop_column('imagegenerationrecord', 'task_id')
    
    # ### end Alembic commands ###
