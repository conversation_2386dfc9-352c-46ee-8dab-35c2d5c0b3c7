"""add_device_token_table

Revision ID: bc20a935f2ef
Revises: add_task_id_and_usage_relations
Create Date: 2025-07-31 13:39:10.914668

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = 'bc20a935f2ef'
down_revision = 'add_task_id_and_usage_relations'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # 创建 devicetoken 表，使用已存在的 platformenum 枚举类型
    op.create_table('devicetoken',
    sa.Column('device_token', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=False),
    sa.Column('platform', sa.Enum('ios', 'android', 'web', name='platformenum', create_type=False), nullable=False),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_devicetoken_device_token'), 'devicetoken', ['device_token'], unique=False)
    op.drop_constraint('fk_usagerecord_related_record_id', 'usagerecord', type_='foreignkey')
    op.create_foreign_key(None, 'usagerecord', 'imagegenerationrecord', ['related_record_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'usagerecord', type_='foreignkey')
    op.create_foreign_key('fk_usagerecord_related_record_id', 'usagerecord', 'imagegenerationrecord', ['related_record_id'], ['id'], ondelete='SET NULL')
    op.drop_index(op.f('ix_devicetoken_device_token'), table_name='devicetoken')
    op.drop_table('devicetoken')
    # ### end Alembic commands ###
