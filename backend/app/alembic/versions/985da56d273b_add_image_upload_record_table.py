"""add_image_upload_record_table

Revision ID: 985da56d273b
Revises: 235ee111fdbb
Create Date: 2025-07-30 18:23:19.634881

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '985da56d273b'
down_revision = '235ee111fdbb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('imageuploadrecord',
    sa.<PERSON>umn('file_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=False),
    sa.Column('original_name', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True),
    sa.Column('file_size', sa.Integer(), nullable=True),
    sa.Column('content_type', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=True),
    sa.Column('folder', sqlmodel.sql.sqltypes.AutoString(length=100), nullable=False),
    sa.Column('object_name', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=False),
    sa.Column('url', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=False),
    sa.Column('upload_source', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.add_column('imagegenerationrecord', sa.Column('result_url', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True))
    op.add_column('imagegenerationrecord', sa.Column('result_urls', sqlmodel.sql.sqltypes.AutoString(length=5000), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('imagegenerationrecord', 'result_urls')
    op.drop_column('imagegenerationrecord', 'result_url')
    op.drop_table('imageuploadrecord')
    # ### end Alembic commands ###
