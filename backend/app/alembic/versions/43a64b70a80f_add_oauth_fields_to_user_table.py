"""Add OAuth fields to user table

Revision ID: 43a64b70a80f
Revises: 945eb1c031b3
Create Date: 2025-07-29 16:42:12.813391

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '43a64b70a80f'
down_revision = '945eb1c031b3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    # Create enum type first
    op.execute("CREATE TYPE authproviderenum AS ENUM ('email', 'apple', 'google', 'wechat', 'facebook', 'github')")

    op.add_column('user', sa.Column('auth_provider', sa.Enum('email', 'apple', 'google', 'wechat', 'facebook', 'github', name='authproviderenum'), nullable=False, server_default='email'))
    op.add_column('user', sa.Column('provider_user_id', sqlmodel.sql.sqltypes.AutoString(length=255), nullable=True))
    op.add_column('user', sa.Column('avatar_url', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True))
    op.alter_column('user', 'hashed_password',
               existing_type=sa.VARCHAR(),
               nullable=True)
    op.create_index(op.f('ix_user_provider_user_id'), 'user', ['provider_user_id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_provider_user_id'), table_name='user')
    op.alter_column('user', 'hashed_password',
               existing_type=sa.VARCHAR(),
               nullable=False)
    op.drop_column('user', 'avatar_url')
    op.drop_column('user', 'provider_user_id')
    op.drop_column('user', 'auth_provider')
    # ### end Alembic commands ###
