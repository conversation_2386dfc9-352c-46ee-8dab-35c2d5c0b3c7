"""add_image_generation_record_table

Revision ID: 235ee111fdbb
Revises: 43a64b70a80f
Create Date: 2025-07-29 18:11:57.711981

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '235ee111fdbb'
down_revision = '43a64b70a80f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('imagegenerationrecord',
    sa.<PERSON>umn('prompt', sqlmodel.sql.sqltypes.AutoString(length=2000), nullable=False),
    sa.Column('size', sqlmodel.sql.sqltypes.AutoString(length=10), nullable=False),
    sa.Column('files_url', sqlmodel.sql.sqltypes.AutoString(length=5000), nullable=True),
    sa.Column('callback_url', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=True),
    sa.Column('is_enhance', sa.<PERSON>(), nullable=False),
    sa.Column('n_variants', sa.<PERSON>(), nullable=False),
    sa.Column('api_response', sqlmodel.sql.sqltypes.AutoString(length=10000), nullable=True),
    sa.Column('status', sqlmodel.sql.sqltypes.AutoString(length=20), nullable=False),
    sa.Column('error_message', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.Column('user_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('imagegenerationrecord')
    # ### end Alembic commands ###
