import uuid
from datetime import datetime, timezone
from enum import Enum

from pydantic import EmailStr
from sqlmodel import Field, Relationship, SQLModel


# Enums for the subscription system
class PlatformEnum(str, Enum):
    ios = "ios"
    android = "android"
    web = "web"


class AuthProviderEnum(str, Enum):
    """第三方认证提供商枚举"""
    email = "email"          # 邮箱密码登录
    apple = "apple"          # Apple Sign In
    google = "google"        # Google OAuth
    wechat = "wechat"        # 微信登录
    facebook = "facebook"    # Facebook Login
    github = "github"        # GitHub OAuth


class SubscriptionPlatformEnum(str, Enum):
    apple = "apple"
    google = "google"
    stripe = "stripe"


class UsageTypeEnum(str, Enum):
    image_generation = "image_generation"
    hd_export = "hd_export"
    premium_filter = "premium_filter"


# Shared properties
class UserBase(SQLModel):
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_active: bool = True
    is_superuser: bool = False
    full_name: str | None = Field(default=None, max_length=255)
    platform: PlatformEnum = Field(default=PlatformEnum.web)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_login: datetime | None = Field(default=None)

    # 第三方登录相关字段
    auth_provider: AuthProviderEnum = Field(default=AuthProviderEnum.email)
    provider_user_id: str | None = Field(default=None, max_length=255, index=True)
    avatar_url: str | None = Field(default=None, max_length=500)


# Properties to receive via API on creation
class UserCreate(UserBase):
    password: str | None = Field(default=None, min_length=8, max_length=40)  # 第三方登录用户可能没有密码


class UserRegister(SQLModel):
    email: EmailStr = Field(max_length=255)
    password: str = Field(min_length=8, max_length=40)
    full_name: str | None = Field(default=None, max_length=255)


# Properties to receive via API on update, all are optional
class UserUpdate(UserBase):
    email: EmailStr | None = Field(default=None, max_length=255)  # type: ignore
    password: str | None = Field(default=None, min_length=8, max_length=40)


class UserUpdateMe(SQLModel):
    full_name: str | None = Field(default=None, max_length=255)
    email: EmailStr | None = Field(default=None, max_length=255)


class UpdatePassword(SQLModel):
    current_password: str = Field(min_length=8, max_length=40)
    new_password: str = Field(min_length=8, max_length=40)


# Database model, database table inferred from class name
class User(UserBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    hashed_password: str | None = Field(default=None)  # 第三方登录用户可能没有密码
    items: list["Item"] = Relationship(back_populates="owner", cascade_delete=True)
    subscriptions: list["Subscription"] = Relationship(back_populates="user", cascade_delete=True)
    usage_records: list["UsageRecord"] = Relationship(back_populates="user", cascade_delete=True)
    credit_packages: list["CreditPackage"] = Relationship(back_populates="user", cascade_delete=True)
    image_generation_records: list["ImageGenerationRecord"] = Relationship(back_populates="user", cascade_delete=True)
    device_tokens: list["DeviceToken"] = Relationship(back_populates="user", cascade_delete=True)
    image_upload_records: list["ImageUploadRecord"] = Relationship(back_populates="user", cascade_delete=True)


# Properties to return via API, id is always required
class UserPublic(UserBase):
    id: uuid.UUID


class UserPublicWithCredits(UserPublic):
    """用户公开信息，包含积分信息"""
    total_credits: int = Field(default=0, description="用户总积分数")
    remaining_credits: int = Field(default=0, description="用户剩余积分数")


class UsersPublic(SQLModel):
    data: list[UserPublic]
    count: int


# Shared properties
class ItemBase(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=255)


# Properties to receive on item creation
class ItemCreate(ItemBase):
    pass


# Properties to receive on item update
class ItemUpdate(ItemBase):
    title: str | None = Field(default=None, min_length=1, max_length=255)  # type: ignore


# Database model, database table inferred from class name
class Item(ItemBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    owner_id: uuid.UUID = Field(
        foreign_key="user.id", nullable=False, ondelete="CASCADE"
    )
    owner: User | None = Relationship(back_populates="items")


# Properties to return via API, id is always required
class ItemPublic(ItemBase):
    id: uuid.UUID
    owner_id: uuid.UUID


class ItemsPublic(SQLModel):
    data: list[ItemPublic]
    count: int


# Generic message
class Message(SQLModel):
    message: str


# JSON payload containing access token
class Token(SQLModel):
    access_token: str
    token_type: str = "bearer"


# Contents of JWT token
class TokenPayload(SQLModel):
    sub: str | None = None


class NewPassword(SQLModel):
    token: str
    new_password: str = Field(min_length=8, max_length=40)


# 第三方登录相关模型
class OAuthUserInfo(SQLModel):
    """第三方平台返回的用户信息"""
    provider: AuthProviderEnum
    provider_user_id: str
    email: str
    full_name: str | None = None
    avatar_url: str | None = None


class OAuthLoginRequest(SQLModel):
    """第三方登录请求"""
    provider: AuthProviderEnum
    access_token: str  # 第三方平台的访问令牌
    platform: PlatformEnum = PlatformEnum.web
    device_token: str | None = None  # 设备推送令牌（可选）


class AppleLoginRequest(SQLModel):
    """Apple Sign In 专用登录请求"""
    identity_token: str  # Apple Identity Token (JWT)
    platform: PlatformEnum = PlatformEnum.ios
    # 可选的用户信息（首次授权时客户端可能提供）
    user_info: dict | None = None  # 包含 firstName, lastName, email 等信息
    # 客户端获取的真实邮箱（可能与token中的隐私邮箱不同）
    real_email: str | None = None
    # 设备推送令牌（可选）
    device_token: str | None = None


class OAuthLoginResponse(SQLModel):
    """第三方登录响应"""
    access_token: str  # 我们系统的JWT令牌
    token_type: str = "bearer"
    user: UserPublic
    is_new_user: bool  # 是否为新注册用户
    trial_status: dict | None = None  # 🆕 试用状态信息


# ============================================================================
# Subscription System Models
# ============================================================================

# Subscription Models
class SubscriptionBase(SQLModel):
    product_id: str = Field(max_length=100, index=True)
    platform: SubscriptionPlatformEnum
    start_date: datetime
    end_date: datetime
    is_active: bool = True
    original_transaction_id: str = Field(max_length=255, index=True)
    last_verified_at: datetime | None = Field(default=None)


class SubscriptionCreate(SubscriptionBase):
    user_id: uuid.UUID


class SubscriptionUpdate(SQLModel):
    end_date: datetime | None = None
    is_active: bool | None = None
    last_verified_at: datetime | None = None


# Database model
class Subscription(SubscriptionBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False, ondelete="CASCADE")
    user: User | None = Relationship(back_populates="subscriptions")


class SubscriptionPublic(SubscriptionBase):
    id: uuid.UUID
    user_id: uuid.UUID


class SubscriptionsPublic(SQLModel):
    data: list[SubscriptionPublic]
    count: int


# Subscription upgrade models
class SubscriptionUpgradeRequest(SQLModel):
    """Request model for upgrading user subscription"""
    user_id: uuid.UUID
    product_id: str = Field(description="Subscription product ID (e.g., 'sub_monthly_40', 'sub_yearly_480')")
    duration_days: int = Field(description="Subscription duration in days (30 for monthly, 365 for yearly)")
    platform: SubscriptionPlatformEnum = Field(default=SubscriptionPlatformEnum.stripe, description="Payment platform")


class SubscriptionUpgradeResponse(SQLModel):
    """Response model for subscription upgrade"""
    subscription: SubscriptionPublic
    message: str
    is_new_subscription: bool


# Usage Record Models
class UsageRecordBase(SQLModel):
    used_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    usage_type: UsageTypeEnum
    count: int = Field(default=1, ge=1)
    # 🆕 添加关联字段，用于追踪具体的使用记录
    related_record_id: uuid.UUID | None = Field(default=None, foreign_key="imagegenerationrecord.id")  # 关联的生成记录ID
    related_record_type: str | None = Field(default=None, max_length=50)  # 关联记录类型: image_generation, hd_export等


class UsageRecordCreate(UsageRecordBase):
    user_id: uuid.UUID


# Database model
class UsageRecord(UsageRecordBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False, ondelete="CASCADE")
    user: User | None = Relationship(back_populates="usage_records")


class UsageRecordPublic(UsageRecordBase):
    id: uuid.UUID
    user_id: uuid.UUID


class UsageRecordsPublic(SQLModel):
    data: list[UsageRecordPublic]
    count: int


# Credit Package Models
class CreditPackageBase(SQLModel):
    credits: int = Field(ge=1)
    remaining_credits: int = Field(ge=0)
    purchased_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    product_id: str = Field(max_length=100, index=True)
    platform: SubscriptionPlatformEnum


class CreditPackageCreate(CreditPackageBase):
    user_id: uuid.UUID


class CreditPackageUpdate(SQLModel):
    remaining_credits: int | None = Field(default=None, ge=0)


# Database model
class CreditPackage(CreditPackageBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False, ondelete="CASCADE")
    user: User | None = Relationship(back_populates="credit_packages")


class CreditPackagePublic(CreditPackageBase):
    id: uuid.UUID
    user_id: uuid.UUID


class CreditPackagesPublic(SQLModel):
    data: list[CreditPackagePublic]
    count: int


# User Status and Access Control Models
class UserSubscriptionStatus(SQLModel):
    """用户订阅状态响应模型"""
    user_id: uuid.UUID
    has_active_subscription: bool
    subscription_end_date: datetime | None = None
    subscription_product_id: str | None = None
    total_credits: int = 0
    monthly_usage_count: int = 0
    monthly_limit: int = 0
    can_use_service: bool = False


class AccessCheckResponse(SQLModel):
    """权限检查响应模型"""
    can_access: bool
    reason: str | None = None
    remaining_credits: int = 0
    remaining_monthly_usage: int = 0
    subscription_status: str | None = None


class UsageRequest(SQLModel):
    """使用服务请求模型"""
    usage_type: UsageTypeEnum = UsageTypeEnum.image_generation
    count: int = Field(default=1, ge=1, le=10)


class UsageResponse(SQLModel):
    """使用服务响应模型"""
    success: bool
    message: str
    remaining_credits: int = 0
    remaining_monthly_usage: int = 0


# ============================================================================
# Image Generation Models
# ============================================================================

class ImageGenerationRecordBase(SQLModel):
    """图片生成记录基础模型"""
    prompt: str = Field(max_length=2000)
    size: str = Field(max_length=10)
    files_url: str | None = Field(default=None, max_length=5000)  # JSON string of URLs
    callback_url: str | None = Field(default=None, max_length=500)
    is_enhance: bool = False
    n_variants: int = Field(default=1, ge=1, le=10)
    # 🆕 添加task_id字段，提高查询效率
    task_id: str | None = Field(default=None, max_length=100, index=True)  # 第三方API返回的任务ID
    api_response: str | None = Field(default=None, max_length=10000)  # JSON string
    status: str = Field(default="pending", max_length=20)  # pending, success, failed
    error_message: str | None = Field(default=None, max_length=1000)
    # 🆕 添加图片结果字段
    result_url: str | None = Field(default=None, max_length=1000)  # 主要结果图片URL
    result_urls: str | None = Field(default=None, max_length=5000)  # 所有结果图片URLs (JSON string)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class ImageGenerationRecordCreate(ImageGenerationRecordBase):
    user_id: uuid.UUID


class ImageGenerationRecord(ImageGenerationRecordBase, table=True):
    """图片生成记录数据库模型"""
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False, ondelete="CASCADE")
    user: User | None = Relationship(back_populates="image_generation_records")


class ImageGenerationRecordPublic(ImageGenerationRecordBase):
    """图片生成记录公开模型"""
    id: uuid.UUID
    user_id: uuid.UUID


class ImageGenerationRecordsPublic(SQLModel):
    """图片生成记录列表模型"""
    data: list[ImageGenerationRecordPublic]
    count: int


# ============================================================================
# Device Token Models (for Push Notifications)
# ============================================================================

class DeviceTokenBase(SQLModel):
    """设备Token基础模型"""
    device_token: str = Field(max_length=500, index=True)
    platform: PlatformEnum = Field(default=PlatformEnum.ios)
    is_active: bool = Field(default=True)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class DeviceTokenCreate(DeviceTokenBase):
    """创建设备Token请求模型"""
    user_id: uuid.UUID


class DeviceTokenUpdate(SQLModel):
    """更新设备Token模型"""
    device_token: str | None = Field(default=None, max_length=500)
    is_active: bool | None = Field(default=None)
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class DeviceToken(DeviceTokenBase, table=True):
    """设备Token数据库模型"""
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False, ondelete="CASCADE")
    user: User | None = Relationship(back_populates="device_tokens")


class DeviceTokenPublic(DeviceTokenBase):
    """设备Token公开模型"""
    id: uuid.UUID
    user_id: uuid.UUID


class DeviceTokensPublic(SQLModel):
    """设备Token列表模型"""
    data: list[DeviceTokenPublic]
    count: int


class DeviceTokenRegisterRequest(SQLModel):
    """设备Token注册请求模型"""
    device_token: str = Field(max_length=500)
    platform: PlatformEnum = Field(default=PlatformEnum.ios)


class DeviceTokenRegisterResponse(SQLModel):
    """设备Token注册响应模型"""
    success: bool
    message: str
    device_token_id: uuid.UUID | None = None


# ============================================================================
# Image Upload Record Models
# ============================================================================

class ImageUploadRecordBase(SQLModel):
    """图片上传记录基础模型"""
    file_name: str = Field(max_length=255)
    original_name: str | None = Field(default=None, max_length=255)  # 原始文件名
    file_size: int | None = Field(default=None, ge=0)  # 文件大小（字节）
    content_type: str | None = Field(default=None, max_length=100)  # MIME类型
    folder: str = Field(default="uploads", max_length=100)  # 存储文件夹
    object_name: str = Field(max_length=500)  # R2中的完整对象名称
    url: str = Field(max_length=1000)  # 公共访问URL
    upload_source: str = Field(default="file", max_length=20)  # 上传来源：file, url
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class ImageUploadRecordCreate(ImageUploadRecordBase):
    """创建图片上传记录请求模型"""
    user_id: uuid.UUID


class ImageUploadRecord(ImageUploadRecordBase, table=True):
    """图片上传记录数据库模型"""
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False, ondelete="CASCADE")
    user: User | None = Relationship(back_populates="image_upload_records")


class ImageUploadRecordPublic(ImageUploadRecordBase):
    """图片上传记录公开模型"""
    id: uuid.UUID
    user_id: uuid.UUID


class ImageUploadRecordsPublic(SQLModel):
    """图片上传记录列表模型"""
    data: list[ImageUploadRecordPublic]
    count: int
