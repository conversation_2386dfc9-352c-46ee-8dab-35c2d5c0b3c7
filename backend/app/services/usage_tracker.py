"""
Usage tracking service.

This module handles tracking and analytics for user usage patterns.
"""

import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional

from sqlmodel import Session, select, func

from app.models import UsageRecord, UsageTypeEnum, User


class UsageTracker:
    """Service for tracking and analyzing user usage patterns."""
    
    def __init__(self, session: Session):
        self.session = session
    
    def get_user_usage_stats(
        self, 
        user_id: uuid.UUID, 
        days: int = 30
    ) -> Dict:
        """Get usage statistics for a user over specified days."""
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days)
        
        # Total usage count
        total_usage_stmt = select(func.sum(UsageRecord.count)).where(
            UsageRecord.user_id == user_id,
            UsageRecord.used_at >= start_date,
            UsageRecord.used_at <= end_date
        )
        total_usage = self.session.exec(total_usage_stmt).first() or 0
        
        # Usage by type
        usage_by_type_stmt = select(
            UsageRecord.usage_type,
            func.sum(UsageRecord.count).label('total_count')
        ).where(
            UsageRecord.user_id == user_id,
            UsageRecord.used_at >= start_date,
            UsageRecord.used_at <= end_date
        ).group_by(UsageRecord.usage_type)
        
        usage_by_type = {}
        for row in self.session.exec(usage_by_type_stmt):
            usage_by_type[row.usage_type] = row.total_count
        
        # Daily usage pattern
        daily_usage_stmt = select(
            func.date(UsageRecord.used_at).label('usage_date'),
            func.sum(UsageRecord.count).label('daily_count')
        ).where(
            UsageRecord.user_id == user_id,
            UsageRecord.used_at >= start_date,
            UsageRecord.used_at <= end_date
        ).group_by(func.date(UsageRecord.used_at)).order_by('usage_date')
        
        daily_usage = []
        for row in self.session.exec(daily_usage_stmt):
            daily_usage.append({
                'date': row.usage_date.isoformat(),
                'count': row.daily_count
            })
        
        return {
            'user_id': str(user_id),
            'period_days': days,
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat(),
            'total_usage': total_usage,
            'usage_by_type': usage_by_type,
            'daily_usage': daily_usage
        }
    
    def get_current_month_usage(self, user_id: uuid.UUID) -> Dict:
        """Get current month usage for a user."""
        now = datetime.now(timezone.utc)
        start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        # Total usage this month
        total_stmt = select(func.sum(UsageRecord.count)).where(
            UsageRecord.user_id == user_id,
            UsageRecord.used_at >= start_of_month
        )
        total_usage = self.session.exec(total_stmt).first() or 0
        
        # Usage by type this month
        by_type_stmt = select(
            UsageRecord.usage_type,
            func.sum(UsageRecord.count).label('count')
        ).where(
            UsageRecord.user_id == user_id,
            UsageRecord.used_at >= start_of_month
        ).group_by(UsageRecord.usage_type)
        
        usage_by_type = {}
        for row in self.session.exec(by_type_stmt):
            usage_by_type[row.usage_type] = row.count
        
        return {
            'user_id': str(user_id),
            'month': start_of_month.strftime('%Y-%m'),
            'total_usage': total_usage,
            'usage_by_type': usage_by_type
        }
    
    def get_platform_usage_stats(self, days: int = 30) -> Dict:
        """Get usage statistics across all platforms."""
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days)
        
        # Usage by platform
        platform_stmt = select(
            User.platform,
            func.sum(UsageRecord.count).label('total_usage')
        ).join(UsageRecord).where(
            UsageRecord.used_at >= start_date,
            UsageRecord.used_at <= end_date
        ).group_by(User.platform)
        
        platform_usage = {}
        for row in self.session.exec(platform_stmt):
            platform_usage[row.platform] = row.total_usage
        
        # Usage by type across all platforms
        type_stmt = select(
            UsageRecord.usage_type,
            func.sum(UsageRecord.count).label('total_usage')
        ).where(
            UsageRecord.used_at >= start_date,
            UsageRecord.used_at <= end_date
        ).group_by(UsageRecord.usage_type)
        
        type_usage = {}
        for row in self.session.exec(type_stmt):
            type_usage[row.usage_type] = row.total_usage
        
        # Total users with usage
        active_users_stmt = select(func.count(func.distinct(UsageRecord.user_id))).where(
            UsageRecord.used_at >= start_date,
            UsageRecord.used_at <= end_date
        )
        active_users = self.session.exec(active_users_stmt).first() or 0
        
        return {
            'period_days': days,
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat(),
            'active_users': active_users,
            'usage_by_platform': platform_usage,
            'usage_by_type': type_usage
        }
    
    def get_top_users(self, limit: int = 10, days: int = 30) -> List[Dict]:
        """Get top users by usage in the specified period."""
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days)
        
        stmt = select(
            UsageRecord.user_id,
            User.email,
            User.platform,
            func.sum(UsageRecord.count).label('total_usage')
        ).join(User).where(
            UsageRecord.used_at >= start_date,
            UsageRecord.used_at <= end_date
        ).group_by(
            UsageRecord.user_id, User.email, User.platform
        ).order_by(func.sum(UsageRecord.count).desc()).limit(limit)
        
        top_users = []
        for row in self.session.exec(stmt):
            top_users.append({
                'user_id': str(row.user_id),
                'email': row.email,
                'platform': row.platform,
                'total_usage': row.total_usage
            })
        
        return top_users
    
    def record_usage_event(
        self, 
        user_id: uuid.UUID, 
        usage_type: UsageTypeEnum, 
        count: int = 1,
        metadata: Optional[Dict] = None
    ) -> UsageRecord:
        """Record a usage event with optional metadata."""
        usage_record = UsageRecord(
            user_id=user_id,
            usage_type=usage_type,
            count=count
        )
        
        self.session.add(usage_record)
        self.session.commit()
        self.session.refresh(usage_record)
        
        return usage_record
