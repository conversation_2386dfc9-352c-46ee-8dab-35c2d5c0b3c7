"""
Device Token Management Service.

This module handles device token registration and management for push notifications.
"""

import logging
import uuid
from datetime import datetime, timezone
from typing import Optional, Tuple

from sqlmodel import Session, select

from app.models import (
    DeviceToken, DeviceTokenCreate, DeviceTokenUpdate,
    DeviceTokenRegisterRequest, DeviceTokenRegisterResponse,
    PlatformEnum
)

logger = logging.getLogger(__name__)


class DeviceTokenService:
    """设备Token管理服务"""
    
    def __init__(self, session: Session):
        self.session = session
    
    def register_device_token(
        self,
        user_id: uuid.UUID,
        request: DeviceTokenRegisterRequest
    ) -> DeviceTokenRegisterResponse:
        """
        注册或更新设备Token
        
        Args:
            user_id: 用户ID
            request: 设备Token注册请求
            
        Returns:
            DeviceTokenRegisterResponse: 注册结果
        """
        try:
            # 检查是否已存在相同的设备token
            statement = select(DeviceToken).where(
                DeviceToken.user_id == user_id,
                DeviceToken.device_token == request.device_token,
                DeviceToken.platform == request.platform
            )
            existing_token = self.session.exec(statement).first()
            
            if existing_token:
                # 更新现有token为活跃状态
                existing_token.is_active = True
                existing_token.updated_at = datetime.now(timezone.utc)
                self.session.add(existing_token)
                self.session.commit()
                self.session.refresh(existing_token)
                
                logger.info(f"Updated existing device token for user {user_id}")
                return DeviceTokenRegisterResponse(
                    success=True,
                    message="Device token updated successfully",
                    device_token_id=existing_token.id
                )
            else:
                # 创建新的设备token记录
                device_token_data = DeviceTokenCreate(
                    user_id=user_id,
                    device_token=request.device_token,
                    platform=request.platform,
                    is_active=True
                )
                
                device_token = DeviceToken.model_validate(device_token_data)
                self.session.add(device_token)
                self.session.commit()
                self.session.refresh(device_token)
                
                logger.info(f"Registered new device token for user {user_id}")
                return DeviceTokenRegisterResponse(
                    success=True,
                    message="Device token registered successfully",
                    device_token_id=device_token.id
                )
                
        except Exception as e:
            logger.error(f"Failed to register device token for user {user_id}: {str(e)}")
            self.session.rollback()
            return DeviceTokenRegisterResponse(
                success=False,
                message=f"Failed to register device token: {str(e)}"
            )
    
    def deactivate_device_token(
        self,
        user_id: uuid.UUID,
        device_token: str
    ) -> Tuple[bool, str]:
        """
        停用设备Token
        
        Args:
            user_id: 用户ID
            device_token: 设备token
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            statement = select(DeviceToken).where(
                DeviceToken.user_id == user_id,
                DeviceToken.device_token == device_token
            )
            token_record = self.session.exec(statement).first()
            
            if not token_record:
                return False, "Device token not found"
            
            token_record.is_active = False
            token_record.updated_at = datetime.now(timezone.utc)
            self.session.add(token_record)
            self.session.commit()
            
            logger.info(f"Deactivated device token for user {user_id}")
            return True, "Device token deactivated successfully"
            
        except Exception as e:
            logger.error(f"Failed to deactivate device token for user {user_id}: {str(e)}")
            self.session.rollback()
            return False, f"Failed to deactivate device token: {str(e)}"
    
    def get_user_device_tokens(
        self,
        user_id: uuid.UUID,
        platform: Optional[PlatformEnum] = None,
        active_only: bool = True
    ) -> list[DeviceToken]:
        """
        获取用户的设备Token列表
        
        Args:
            user_id: 用户ID
            platform: 平台过滤（可选）
            active_only: 是否只返回活跃的token
            
        Returns:
            list[DeviceToken]: 设备token列表
        """
        try:
            statement = select(DeviceToken).where(DeviceToken.user_id == user_id)
            
            if platform:
                statement = statement.where(DeviceToken.platform == platform)
            
            if active_only:
                statement = statement.where(DeviceToken.is_active == True)
            
            tokens = self.session.exec(statement).all()
            return list(tokens)
            
        except Exception as e:
            logger.error(f"Failed to get device tokens for user {user_id}: {str(e)}")
            return []
    
    def cleanup_inactive_tokens(self, days_threshold: int = 30) -> int:
        """
        清理长时间未更新的非活跃token
        
        Args:
            days_threshold: 天数阈值
            
        Returns:
            int: 清理的token数量
        """
        try:
            from datetime import timedelta
            
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_threshold)
            
            statement = select(DeviceToken).where(
                DeviceToken.is_active == False,
                DeviceToken.updated_at < cutoff_date
            )
            tokens_to_delete = self.session.exec(statement).all()
            
            count = len(tokens_to_delete)
            for token in tokens_to_delete:
                self.session.delete(token)
            
            self.session.commit()
            
            logger.info(f"Cleaned up {count} inactive device tokens")
            return count
            
        except Exception as e:
            logger.error(f"Failed to cleanup inactive tokens: {str(e)}")
            self.session.rollback()
            return 0
    
    def update_device_token_status(
        self,
        device_token_id: uuid.UUID,
        is_active: bool
    ) -> Tuple[bool, str]:
        """
        更新设备Token状态
        
        Args:
            device_token_id: 设备Token ID
            is_active: 是否活跃
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            token_record = self.session.get(DeviceToken, device_token_id)
            
            if not token_record:
                return False, "Device token not found"
            
            token_record.is_active = is_active
            token_record.updated_at = datetime.now(timezone.utc)
            self.session.add(token_record)
            self.session.commit()
            
            status = "activated" if is_active else "deactivated"
            logger.info(f"Device token {device_token_id} {status}")
            return True, f"Device token {status} successfully"
            
        except Exception as e:
            logger.error(f"Failed to update device token {device_token_id}: {str(e)}")
            self.session.rollback()
            return False, f"Failed to update device token: {str(e)}"


# 示例使用方法
if __name__ == "__main__":
    # 这里可以添加测试代码
    print("Device Token Service module loaded")
