"""
新用户试用服务

为新注册用户提供试用配额
"""

import uuid
import logging
from datetime import datetime, timezone
from sqlmodel import Session

from app.models import (
    User, CreditPackage, SubscriptionPlatformEnum
)

logger = logging.getLogger(__name__)


class TrialService:
    """新用户试用服务"""
    
    # 试用配置
    TRIAL_CONFIG = {
        "credits": 10,  # 试用积分数量（2次生成 * 5积分/次）
        "product_id": "trial_credits_new_user",
        "description": "New user trial credits"
    }
    
    def __init__(self, session: Session):
        self.session = session
    
    def create_trial_credits_for_new_user(self, user: User) -> CreditPackage:
        """
        为新用户创建试用积分包
        
        Args:
            user: 新创建的用户
            
        Returns:
            CreditPackage: 创建的试用积分包
        """
        try:
            # 检查用户是否已经有试用积分
            existing_trial = self.session.query(CreditPackage).filter(
                CreditPackage.user_id == user.id,
                CreditPackage.product_id == self.TRIAL_CONFIG["product_id"]
            ).first()
            
            if existing_trial:
                logger.info(f"User {user.id} already has trial credits")
                return existing_trial
            
            # 创建试用积分包
            trial_credits = CreditPackage(
                user_id=user.id,
                credits=self.TRIAL_CONFIG["credits"],
                remaining_credits=self.TRIAL_CONFIG["credits"],
                product_id=self.TRIAL_CONFIG["product_id"],
                platform=SubscriptionPlatformEnum.stripe,  # 使用stripe作为默认平台
                purchased_at=datetime.now(timezone.utc)
            )
            
            self.session.add(trial_credits)
            self.session.commit()
            self.session.refresh(trial_credits)
            
            logger.info(
                f"Created trial credits for user {user.id}: "
                f"{self.TRIAL_CONFIG['credits']} credits"
            )
            
            return trial_credits
            
        except Exception as e:
            logger.error(f"Failed to create trial credits for user {user.id}: {str(e)}")
            self.session.rollback()
            raise
    
    def get_trial_status(self, user_id: uuid.UUID) -> dict:
        """
        获取用户的试用状态
        
        Args:
            user_id: 用户ID
            
        Returns:
            dict: 试用状态信息
        """
        try:
            # 查找试用积分包
            trial_credits = self.session.query(CreditPackage).filter(
                CreditPackage.user_id == user_id,
                CreditPackage.product_id == self.TRIAL_CONFIG["product_id"]
            ).first()
            
            if not trial_credits:
                return {
                    "has_trial": False,
                    "trial_credits": 0,
                    "remaining_credits": 0,
                    "used_credits": 0,
                    "trial_exhausted": False
                }
            
            used_credits = trial_credits.credits - trial_credits.remaining_credits
            
            return {
                "has_trial": True,
                "trial_credits": trial_credits.credits,
                "remaining_credits": trial_credits.remaining_credits,
                "used_credits": used_credits,
                "trial_exhausted": trial_credits.remaining_credits == 0,
                "created_at": trial_credits.purchased_at
            }
            
        except Exception as e:
            logger.error(f"Failed to get trial status for user {user_id}: {str(e)}")
            return {
                "has_trial": False,
                "trial_credits": 0,
                "remaining_credits": 0,
                "used_credits": 0,
                "trial_exhausted": False,
                "error": str(e)
            }
    
    def is_trial_user(self, user_id: uuid.UUID) -> bool:
        """
        检查用户是否为试用用户（只有试用积分，没有付费订阅）
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 是否为试用用户
        """
        try:
            from app.services.billing import BillingService
            
            billing_service = BillingService(self.session)
            status = billing_service.get_user_subscription_status(user_id)
            
            # 如果有有效订阅，不是试用用户
            if status.has_active_subscription:
                return False
            
            # 检查是否有试用积分
            trial_status = self.get_trial_status(user_id)
            
            # 如果有试用积分且没有其他积分包，则为试用用户
            total_credits = status.total_credits
            trial_credits = trial_status.get("remaining_credits", 0)
            
            # 如果总积分等于试用积分，说明只有试用积分
            return trial_credits > 0 and total_credits == trial_credits
            
        except Exception as e:
            logger.error(f"Failed to check if user {user_id} is trial user: {str(e)}")
            return False
    
    def get_trial_usage_message(self, user_id: uuid.UUID) -> str:
        """
        获取试用用户的使用提示信息
        
        Args:
            user_id: 用户ID
            
        Returns:
            str: 提示信息
        """
        trial_status = self.get_trial_status(user_id)
        
        if not trial_status["has_trial"]:
            return "Welcome! You can purchase credits or subscribe to use image generation."
        
        remaining = trial_status["remaining_credits"]
        total = trial_status["trial_credits"]
        used = trial_status["used_credits"]
        
        # 计算剩余生成次数（每次5积分）
        remaining_generations = remaining // 5
        
        if remaining_generations > 0:
            return (
                f"Trial user: {remaining_generations} free generations remaining "
                f"({remaining}/{total} trial credits left)"
            )
        else:
            return (
                f"Trial exhausted: You've used all {total} trial credits. "
                f"Please purchase credits or subscribe to continue."
            )


def create_trial_credits_for_user(session: Session, user: User) -> CreditPackage:
    """
    便捷函数：为新用户创建试用积分
    
    Args:
        session: 数据库会话
        user: 新用户
        
    Returns:
        CreditPackage: 创建的试用积分包
    """
    trial_service = TrialService(session)
    return trial_service.create_trial_credits_for_new_user(user)
