"""
Cloudflare R2 图片上传服务

提供图片上传到Cloudflare R2存储的功能，支持多种图片格式和自动内容类型检测。
使用boto3库实现AWS S3兼容的API调用。
"""

import logging
import hashlib
import mimetypes
import uuid
from typing import Optional, Dict, Any
from pathlib import Path
from io import BytesIO

import requests
import boto3
from botocore.client import Config
from botocore.exceptions import ClientError, NoCredentialsError
from pydantic import BaseModel

from app.core.config import settings

logger = logging.getLogger(__name__)


class ImageUploadRequest(BaseModel):
    """图片上传请求模型"""
    file_name: Optional[str] = None  # 可选的文件名，如果不提供则自动生成
    folder: str = "uploads"  # 存储文件夹，默认为uploads
    content_type: Optional[str] = None  # 可选的内容类型，如果不提供则自动检测


class ImageUploadResponse(BaseModel):
    """图片上传响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    url: Optional[str] = None  # 上传成功后的访问URL
    object_name: Optional[str] = None  # R2中的对象名称


class CloudflareR2Service:
    """Cloudflare R2 存储服务类 - 使用boto3实现"""

    def __init__(self):
        self.account_id = settings.R2_ACCOUNT_ID
        self.access_key_id = settings.R2_ACCESS_KEY_ID
        self.secret_access_key = settings.R2_SECRET_ACCESS_KEY
        self.bucket = settings.R2_BUCKET
        self.public_domain = settings.R2_PUBLIC_DOMAIN
        self.endpoint = f"https://{self.account_id}.r2.cloudflarestorage.com"

        # 验证配置
        if not all([self.account_id, self.access_key_id, self.secret_access_key, self.bucket]):
            logger.error("Cloudflare R2 configuration is incomplete")
            raise ValueError("R2 configuration missing: account_id, access_key_id, secret_access_key, or bucket")

        # 初始化boto3 S3客户端
        try:
            self.s3_client = boto3.client(
                's3',
                endpoint_url=self.endpoint,
                aws_access_key_id=self.access_key_id,
                aws_secret_access_key=self.secret_access_key,
                config=Config(signature_version='s3v4')
            )
            logger.info(f"Initialized Cloudflare R2 service for bucket: {self.bucket}")
        except Exception as e:
            logger.error(f"Failed to initialize R2 client: {e}")
            raise ValueError(f"Failed to initialize R2 client: {e}")



    def _detect_content_type(self, file_name: str, file_data: bytes) -> str:
        """检测文件的内容类型"""
        # 首先尝试从文件名推断
        content_type, _ = mimetypes.guess_type(file_name)

        if content_type and content_type.startswith('image/'):
            return content_type

        # 如果无法从文件名推断，尝试从文件头检测
        if file_data.startswith(b'\xff\xd8\xff'):
            return 'image/jpeg'
        elif file_data.startswith(b'\x89PNG\r\n\x1a\n'):
            return 'image/png'
        elif file_data.startswith(b'GIF87a') or file_data.startswith(b'GIF89a'):
            return 'image/gif'
        elif file_data.startswith(b'RIFF') and b'WEBP' in file_data[:12]:
            return 'image/webp'
        elif file_data.startswith(b'BM'):
            return 'image/bmp'

        # 默认返回通用图片类型
        return 'image/jpeg'

    def _generate_object_name(self, folder: str, file_name: Optional[str] = None) -> str:
        """生成R2对象名称"""
        if not file_name:
            file_name = f"{uuid.uuid4().hex}.jpg"

        # 确保文件名安全
        safe_file_name = "".join(c for c in file_name if c.isalnum() or c in '.-_')
        return f"{folder.strip('/')}/{safe_file_name}"

    def _get_public_url(self, object_name: str) -> str:
        """获取文件的公共访问URL"""
        if self.public_domain:
            return f"https://{self.public_domain}/{object_name}"
        else:
            return f"https://pub-{self.account_id}.r2.dev/{object_name}"

    def upload_image_from_bytes(
        self,
        image_data: bytes,
        request: ImageUploadRequest
    ) -> ImageUploadResponse:
        """
        从字节数据上传图片到R2

        Args:
            image_data: 图片的字节数据
            request: 上传请求参数

        Returns:
            ImageUploadResponse: 上传结果
        """
        try:
            logger.info(f"Starting image upload to R2, data size: {len(image_data)} bytes")

            # 生成对象名称
            object_name = self._generate_object_name(request.folder, request.file_name)

            # 检测内容类型
            content_type = request.content_type or self._detect_content_type(
                request.file_name or "image.jpg",
                image_data
            )

            logger.info(f"Uploading to R2: object_name={object_name}, content_type={content_type}")

            # 使用boto3上传
            file_obj = BytesIO(image_data)

            extra_args = {
                'ContentType': content_type,
                'ACL': 'public-read'  # 设置为公开可读
            }

            self.s3_client.upload_fileobj(
                file_obj,
                self.bucket,
                object_name,
                ExtraArgs=extra_args
            )

            public_url = self._get_public_url(object_name)
            logger.info(f"Image uploaded successfully to R2: {public_url}")

            return ImageUploadResponse(
                success=True,
                message="Image uploaded successfully",
                data={
                    "object_name": object_name,
                    "content_type": content_type,
                    "size": len(image_data),
                    "bucket": self.bucket
                },
                url=public_url,
                object_name=object_name
            )

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_msg = f"AWS S3 error ({error_code}): {e.response['Error']['Message']}"
            logger.error(error_msg)
            return ImageUploadResponse(
                success=False,
                message="S3 client error during upload",
                error=error_msg
            )
        except NoCredentialsError as e:
            error_msg = f"AWS credentials error: {str(e)}"
            logger.error(error_msg)
            return ImageUploadResponse(
                success=False,
                message="Credentials error during upload",
                error=error_msg
            )
        except Exception as e:
            error_msg = f"Unexpected error during R2 upload: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ImageUploadResponse(
                success=False,
                message="Unexpected error during upload",
                error=error_msg
            )

    def upload_image_from_file(
        self,
        file_path: str,
        request: ImageUploadRequest
    ) -> ImageUploadResponse:
        """
        从文件路径上传图片到R2

        Args:
            file_path: 本地文件路径
            request: 上传请求参数

        Returns:
            ImageUploadResponse: 上传结果
        """
        try:
            path = Path(file_path)
            if not path.exists():
                error_msg = f"File not found: {file_path}"
                logger.error(error_msg)
                return ImageUploadResponse(
                    success=False,
                    message="File not found",
                    error=error_msg
                )

            if not path.is_file():
                error_msg = f"Path is not a file: {file_path}"
                logger.error(error_msg)
                return ImageUploadResponse(
                    success=False,
                    message="Path is not a file",
                    error=error_msg
                )

            # 读取文件数据
            with open(path, 'rb') as f:
                image_data = f.read()

            # 如果没有指定文件名，使用原文件名
            if not request.file_name:
                request.file_name = path.name

            logger.info(f"Uploading file from path: {file_path}")
            return self.upload_image_from_bytes(image_data, request)

        except Exception as e:
            error_msg = f"Error reading file {file_path}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ImageUploadResponse(
                success=False,
                message="Error reading file",
                error=error_msg
            )

    def upload_image_from_url(
        self,
        image_url: str,
        request: ImageUploadRequest
    ) -> ImageUploadResponse:
        """
        从URL下载图片并上传到R2

        Args:
            image_url: 图片的URL地址
            request: 上传请求参数

        Returns:
            ImageUploadResponse: 上传结果
        """
        try:
            logger.info(f"Downloading image from URL: {image_url}")

            # 下载图片
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()

            image_data = response.content

            # 如果没有指定文件名，尝试从URL推断
            if not request.file_name:
                url_path = Path(image_url)
                if url_path.suffix:
                    request.file_name = f"{uuid.uuid4().hex}{url_path.suffix}"
                else:
                    # 尝试从Content-Type推断扩展名
                    content_type = response.headers.get('content-type', '')
                    if 'jpeg' in content_type or 'jpg' in content_type:
                        request.file_name = f"{uuid.uuid4().hex}.jpg"
                    elif 'png' in content_type:
                        request.file_name = f"{uuid.uuid4().hex}.png"
                    elif 'gif' in content_type:
                        request.file_name = f"{uuid.uuid4().hex}.gif"
                    elif 'webp' in content_type:
                        request.file_name = f"{uuid.uuid4().hex}.webp"
                    else:
                        request.file_name = f"{uuid.uuid4().hex}.jpg"

            # 如果没有指定内容类型，尝试从响应头获取
            if not request.content_type:
                request.content_type = response.headers.get('content-type')

            logger.info(f"Downloaded {len(image_data)} bytes from URL")
            return self.upload_image_from_bytes(image_data, request)

        except requests.exceptions.RequestException as e:
            error_msg = f"Error downloading image from URL {image_url}: {str(e)}"
            logger.error(error_msg)
            return ImageUploadResponse(
                success=False,
                message="Error downloading image",
                error=error_msg
            )
        except Exception as e:
            error_msg = f"Unexpected error downloading from URL {image_url}: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ImageUploadResponse(
                success=False,
                message="Unexpected error downloading image",
                error=error_msg
            )

    def delete_image(self, object_name: str) -> ImageUploadResponse:
        """
        从R2删除图片

        Args:
            object_name: R2中的对象名称

        Returns:
            ImageUploadResponse: 删除结果
        """
        try:
            logger.info(f"Deleting image from R2: {object_name}")

            # 使用boto3删除对象
            self.s3_client.delete_object(
                Bucket=self.bucket,
                Key=object_name
            )

            logger.info(f"Image deleted successfully from R2: {object_name}")
            return ImageUploadResponse(
                success=True,
                message="Image deleted successfully",
                data={"object_name": object_name}
            )

        except ClientError as e:
            error_code = e.response['Error']['Code']
            error_msg = f"AWS S3 error ({error_code}): {e.response['Error']['Message']}"
            logger.error(error_msg)
            return ImageUploadResponse(
                success=False,
                message="S3 client error during delete",
                error=error_msg
            )
        except NoCredentialsError as e:
            error_msg = f"AWS credentials error: {str(e)}"
            logger.error(error_msg)
            return ImageUploadResponse(
                success=False,
                message="Credentials error during delete",
                error=error_msg
            )
        except Exception as e:
            error_msg = f"Unexpected error during R2 delete: {str(e)}"
            logger.error(error_msg, exc_info=True)
            return ImageUploadResponse(
                success=False,
                message="Unexpected error during delete",
                error=error_msg
            )


# 便捷函数
def get_r2_service() -> CloudflareR2Service:
    """获取R2服务实例"""
    return CloudflareR2Service()


def upload_image_bytes(
    image_data: bytes,
    file_name: Optional[str] = None,
    folder: str = "uploads",
    content_type: Optional[str] = None
) -> ImageUploadResponse:
    """
    便捷函数：上传图片字节数据到R2

    Args:
        image_data: 图片字节数据
        file_name: 可选的文件名
        folder: 存储文件夹
        content_type: 可选的内容类型

    Returns:
        ImageUploadResponse: 上传结果
    """
    service = get_r2_service()
    request = ImageUploadRequest(
        file_name=file_name,
        folder=folder,
        content_type=content_type
    )
    return service.upload_image_from_bytes(image_data, request)


def upload_image_file(
    file_path: str,
    file_name: Optional[str] = None,
    folder: str = "uploads",
    content_type: Optional[str] = None
) -> ImageUploadResponse:
    """
    便捷函数：从文件路径上传图片到R2

    Args:
        file_path: 本地文件路径
        file_name: 可选的文件名
        folder: 存储文件夹
        content_type: 可选的内容类型

    Returns:
        ImageUploadResponse: 上传结果
    """
    service = get_r2_service()
    request = ImageUploadRequest(
        file_name=file_name,
        folder=folder,
        content_type=content_type
    )
    return service.upload_image_from_file(file_path, request)


def upload_image_url(
    image_url: str,
    file_name: Optional[str] = None,
    folder: str = "uploads",
    content_type: Optional[str] = None
) -> ImageUploadResponse:
    """
    便捷函数：从URL下载并上传图片到R2

    Args:
        image_url: 图片URL
        file_name: 可选的文件名
        folder: 存储文件夹
        content_type: 可选的内容类型

    Returns:
        ImageUploadResponse: 上传结果
    """
    service = get_r2_service()
    request = ImageUploadRequest(
        file_name=file_name,
        folder=folder,
        content_type=content_type
    )
    return service.upload_image_from_url(image_url, request)


# 测试功能
if __name__ == "__main__":
    # 简单的测试代码
    import sys

    def test_r2_service():
        """测试R2服务配置"""
        try:
            service = CloudflareR2Service()
            logger.info("R2 service initialized successfully")
            logger.info(f"Bucket: {service.bucket}")
            logger.info(f"Endpoint: {service.endpoint}")
            return True
        except Exception as e:
            logger.error(f"R2 service initialization failed: {e}")
            return False

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        logging.basicConfig(level=logging.INFO)
        success = test_r2_service()
        sys.exit(0 if success else 1)