"""
Google Play subscription validation service.

This module handles validation of Google Play subscriptions and purchases.
"""

import json
import uuid
from datetime import datetime, timezone
from typing import Dict, Optional, <PERSON><PERSON>
import httpx

from sqlmodel import Session

from app.models import Subscription, SubscriptionPlatformEnum
from app.core.config import settings


class GooglePlayValidator:
    """Service for validating Google Play subscriptions."""
    
    BASE_URL = "https://androidpublisher.googleapis.com/androidpublisher/v3"
    
    def __init__(self, session: Session):
        self.session = session
        self.package_name = getattr(settings, 'GOOGLE_PACKAGE_NAME', None)
        self.service_account_key = getattr(settings, 'GOOGLE_SERVICE_ACCOUNT_KEY', None)
    
    async def validate_subscription(
        self, 
        subscription_id: str,
        purchase_token: str,
        user_id: uuid.UUID
    ) -> Tuple[bool, Dict, Optional[str]]:
        """
        Validate Google Play subscription.
        
        Args:
            subscription_id: Google Play subscription ID
            purchase_token: Purchase token from Google Play
            user_id: User ID to associate with subscription
            
        Returns:
            Tuple of (is_valid, subscription_info, error_message)
        """
        if not self.package_name or not self.service_account_key:
            return False, {}, "Google Play credentials not configured"
        
        try:
            # Get access token
            access_token = await self._get_access_token()
            if not access_token:
                return False, {}, "Failed to get Google Play access token"
            
            # Validate subscription
            url = f"{self.BASE_URL}/applications/{self.package_name}/purchases/subscriptions/{subscription_id}/tokens/{purchase_token}"
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers, timeout=30.0)
                
                if response.status_code == 200:
                    subscription_data = response.json()
                    return await self._process_valid_subscription(
                        subscription_data, subscription_id, user_id
                    )
                else:
                    error_data = response.json() if response.content else {}
                    error_msg = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")
                    return False, error_data, error_msg
                    
        except httpx.RequestError as e:
            return False, {}, f"Network error: {str(e)}"
        except json.JSONDecodeError:
            return False, {}, "Invalid JSON response from Google Play"
        except Exception as e:
            return False, {}, f"Unexpected error: {str(e)}"
    
    async def _get_access_token(self) -> Optional[str]:
        """Get OAuth2 access token for Google Play API."""
        try:
            # This is a simplified version. In production, you should use
            # Google's official client libraries or implement proper JWT signing
            
            # For now, return None to indicate this needs proper implementation
            # You would typically:
            # 1. Load service account key
            # 2. Create JWT assertion
            # 3. Exchange for access token
            
            return None  # Placeholder - implement proper OAuth2 flow
            
        except Exception as e:
            print(f"Error getting access token: {e}")
            return None
    
    async def _process_valid_subscription(
        self, 
        subscription_data: Dict, 
        subscription_id: str,
        user_id: uuid.UUID
    ) -> Tuple[bool, Dict, Optional[str]]:
        """Process a valid Google Play subscription."""
        try:
            # Extract subscription information
            start_time_millis = int(subscription_data.get("startTimeMillis", 0))
            expiry_time_millis = int(subscription_data.get("expiryTimeMillis", 0))
            
            start_date = datetime.fromtimestamp(start_time_millis / 1000, tz=timezone.utc)
            end_date = datetime.fromtimestamp(expiry_time_millis / 1000, tz=timezone.utc)
            
            # Check subscription state
            payment_state = subscription_data.get("paymentState", 0)  # 0 = pending, 1 = received
            auto_renewing = subscription_data.get("autoRenewing", False)
            
            # Use purchase token as original transaction ID for Google Play
            original_transaction_id = subscription_data.get("linkedPurchaseToken", subscription_id)
            
            # Create or update subscription
            subscription = await self._create_or_update_subscription(
                user_id=user_id,
                product_id=subscription_id,
                start_date=start_date,
                end_date=end_date,
                original_transaction_id=original_transaction_id,
                is_active=(payment_state == 1 and end_date > datetime.now(timezone.utc))
            )
            
            return True, {
                "subscription_data": subscription_data,
                "processed_subscription": subscription.id if subscription else None
            }, None
            
        except Exception as e:
            return False, subscription_data, f"Error processing subscription: {str(e)}"
    
    async def _create_or_update_subscription(
        self,
        user_id: uuid.UUID,
        product_id: str,
        start_date: datetime,
        end_date: datetime,
        original_transaction_id: str,
        is_active: bool
    ) -> Optional[Subscription]:
        """Create or update Google Play subscription."""
        try:
            # Check if subscription already exists
            existing_sub = self.session.query(Subscription).filter(
                Subscription.original_transaction_id == original_transaction_id,
                Subscription.user_id == user_id,
                Subscription.platform == SubscriptionPlatformEnum.google
            ).first()
            
            if existing_sub:
                # Update existing subscription
                existing_sub.end_date = end_date
                existing_sub.is_active = is_active
                existing_sub.last_verified_at = datetime.now(timezone.utc)
                self.session.add(existing_sub)
                subscription = existing_sub
            else:
                # Create new subscription
                subscription = Subscription(
                    user_id=user_id,
                    product_id=product_id,
                    platform=SubscriptionPlatformEnum.google,
                    start_date=start_date,
                    end_date=end_date,
                    is_active=is_active,
                    original_transaction_id=original_transaction_id,
                    last_verified_at=datetime.now(timezone.utc)
                )
                self.session.add(subscription)
            
            self.session.commit()
            return subscription
            
        except Exception as e:
            self.session.rollback()
            print(f"Error creating/updating Google Play subscription: {e}")
            return None
    
    async def verify_subscription_status(
        self, 
        user_id: uuid.UUID, 
        original_transaction_id: str
    ) -> Tuple[bool, Optional[Subscription]]:
        """Verify current status of a Google Play subscription."""
        subscription = self.session.query(Subscription).filter(
            Subscription.user_id == user_id,
            Subscription.original_transaction_id == original_transaction_id,
            Subscription.platform == SubscriptionPlatformEnum.google
        ).first()
        
        if not subscription:
            return False, None
        
        # Check if subscription is still active
        now = datetime.now(timezone.utc)
        is_active = (
            subscription.is_active and 
            subscription.end_date > now
        )
        
        # Update subscription status if needed
        if subscription.is_active != is_active:
            subscription.is_active = is_active
            self.session.add(subscription)
            self.session.commit()
        
        return is_active, subscription
    
    async def handle_webhook(self, webhook_data: Dict) -> Tuple[bool, str]:
        """
        Handle Google Play webhook notifications.
        
        This method processes Real-time Developer Notifications (RTDN)
        from Google Play to update subscription status in real-time.
        """
        try:
            message = webhook_data.get("message", {})
            data = message.get("data", "")
            
            if not data:
                return False, "No data in webhook message"
            
            # Decode base64 data
            import base64
            decoded_data = json.loads(base64.b64decode(data).decode('utf-8'))
            
            subscription_notification = decoded_data.get("subscriptionNotification", {})
            if not subscription_notification:
                return False, "No subscription notification in webhook"
            
            # Extract notification details
            purchase_token = subscription_notification.get("purchaseToken")
            subscription_id = subscription_notification.get("subscriptionId")
            notification_type = subscription_notification.get("notificationType")
            
            # Process notification based on type
            # Types: 1=RECOVERED, 2=RENEWED, 3=CANCELED, 4=PURCHASED, etc.
            
            # This is a placeholder - implement based on your needs
            return True, f"Processed notification type {notification_type}"
            
        except Exception as e:
            return False, f"Error processing webhook: {str(e)}"
