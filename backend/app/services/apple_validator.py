"""
Apple App Store subscription validation service.

This module handles validation of Apple App Store subscriptions and receipts.
"""

import json
import uuid
from datetime import datetime, timezone
from typing import Dict, Optional, Tu<PERSON>
import httpx

from sqlmodel import Session

from app.models import Subscription, SubscriptionPlatformEnum
from app.core.config import settings


class AppleValidator:
    """Service for validating Apple App Store subscriptions."""
    
    # Apple App Store URLs
    PRODUCTION_URL = "https://buy.itunes.apple.com/verifyReceipt"
    SANDBOX_URL = "https://sandbox.itunes.apple.com/verifyReceipt"
    
    def __init__(self, session: Session):
        self.session = session
        self.shared_secret = getattr(settings, 'APPLE_SHARED_SECRET', None)
    
    async def validate_receipt(
        self, 
        receipt_data: str, 
        user_id: uuid.UUID,
        use_sandbox: bool = False
    ) -> Tuple[bool, Dict, Optional[str]]:
        """
        Validate Apple receipt and return validation result.
        
        Args:
            receipt_data: Base64 encoded receipt data
            user_id: User ID to associate with subscription
            use_sandbox: Whether to use sandbox environment
            
        Returns:
            Tuple of (is_valid, receipt_info, error_message)
        """
        if not self.shared_secret:
            return False, {}, "Apple shared secret not configured"
        
        url = self.SANDBOX_URL if use_sandbox else self.PRODUCTION_URL
        
        payload = {
            "receipt-data": receipt_data,
            "password": self.shared_secret,
            "exclude-old-transactions": True
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    url,
                    json=payload,
                    timeout=30.0
                )
                response.raise_for_status()
                
                result = response.json()
                
                # Handle sandbox redirect
                if result.get("status") == 21007 and not use_sandbox:
                    return await self.validate_receipt(receipt_data, user_id, use_sandbox=True)
                
                if result.get("status") == 0:
                    # Receipt is valid, process subscription info
                    return await self._process_valid_receipt(result, user_id)
                else:
                    error_msg = self._get_error_message(result.get("status"))
                    return False, result, error_msg
                    
        except httpx.RequestError as e:
            return False, {}, f"Network error: {str(e)}"
        except json.JSONDecodeError:
            return False, {}, "Invalid JSON response from Apple"
        except Exception as e:
            return False, {}, f"Unexpected error: {str(e)}"
    
    async def _process_valid_receipt(
        self, 
        receipt_data: Dict, 
        user_id: uuid.UUID
    ) -> Tuple[bool, Dict, Optional[str]]:
        """Process a valid receipt and update subscription data."""
        try:
            receipt = receipt_data.get("receipt", {})
            latest_receipt_info = receipt_data.get("latest_receipt_info", [])
            
            if not latest_receipt_info:
                return False, receipt_data, "No subscription info found in receipt"
            
            # Process each subscription in the receipt
            processed_subscriptions = []
            
            for sub_info in latest_receipt_info:
                subscription = await self._create_or_update_subscription(sub_info, user_id)
                if subscription:
                    processed_subscriptions.append(subscription)
            
            return True, {
                "receipt": receipt_data,
                "processed_subscriptions": len(processed_subscriptions)
            }, None
            
        except Exception as e:
            return False, receipt_data, f"Error processing receipt: {str(e)}"
    
    async def _create_or_update_subscription(
        self, 
        sub_info: Dict, 
        user_id: uuid.UUID
    ) -> Optional[Subscription]:
        """Create or update subscription from Apple subscription info."""
        try:
            original_transaction_id = sub_info.get("original_transaction_id")
            product_id = sub_info.get("product_id")
            
            if not original_transaction_id or not product_id:
                return None
            
            # Parse dates
            purchase_date_ms = int(sub_info.get("purchase_date_ms", 0))
            expires_date_ms = int(sub_info.get("expires_date_ms", 0))
            
            start_date = datetime.fromtimestamp(purchase_date_ms / 1000, tz=timezone.utc)
            end_date = datetime.fromtimestamp(expires_date_ms / 1000, tz=timezone.utc)
            
            # Check if subscription already exists
            existing_sub = self.session.query(Subscription).filter(
                Subscription.original_transaction_id == original_transaction_id,
                Subscription.user_id == user_id
            ).first()
            
            if existing_sub:
                # Update existing subscription
                existing_sub.end_date = end_date
                existing_sub.is_active = end_date > datetime.now(timezone.utc)
                existing_sub.last_verified_at = datetime.now(timezone.utc)
                self.session.add(existing_sub)
                subscription = existing_sub
            else:
                # Create new subscription
                subscription = Subscription(
                    user_id=user_id,
                    product_id=product_id,
                    platform=SubscriptionPlatformEnum.apple,
                    start_date=start_date,
                    end_date=end_date,
                    is_active=end_date > datetime.now(timezone.utc),
                    original_transaction_id=original_transaction_id,
                    last_verified_at=datetime.now(timezone.utc)
                )
                self.session.add(subscription)
            
            self.session.commit()
            return subscription
            
        except Exception as e:
            self.session.rollback()
            print(f"Error creating/updating subscription: {e}")
            return None
    
    def _get_error_message(self, status_code: int) -> str:
        """Get human-readable error message for Apple status codes."""
        error_messages = {
            21000: "The App Store could not read the JSON object you provided.",
            21002: "The data in the receipt-data property was malformed or missing.",
            21003: "The receipt could not be authenticated.",
            21004: "The shared secret you provided does not match the shared secret on file for your account.",
            21005: "The receipt server is not currently available.",
            21006: "This receipt is valid but the subscription has expired.",
            21007: "This receipt is from the test environment, but it was sent to the production environment for verification.",
            21008: "This receipt is from the production environment, but it was sent to the test environment for verification.",
            21010: "This receipt could not be authorized. Treat this the same as if a purchase was never made.",
        }
        
        return error_messages.get(
            status_code, 
            f"Unknown error (status code: {status_code})"
        )
    
    async def verify_subscription_status(
        self, 
        user_id: uuid.UUID, 
        original_transaction_id: str
    ) -> Tuple[bool, Optional[Subscription]]:
        """Verify current status of a subscription."""
        subscription = self.session.query(Subscription).filter(
            Subscription.user_id == user_id,
            Subscription.original_transaction_id == original_transaction_id,
            Subscription.platform == SubscriptionPlatformEnum.apple
        ).first()
        
        if not subscription:
            return False, None
        
        # Check if subscription is still active
        now = datetime.now(timezone.utc)
        is_active = (
            subscription.is_active and 
            subscription.end_date > now
        )
        
        # Update subscription status if needed
        if subscription.is_active != is_active:
            subscription.is_active = is_active
            self.session.add(subscription)
            self.session.commit()
        
        return is_active, subscription
