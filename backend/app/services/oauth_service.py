"""
OAuth 第三方登录服务

This module handles third-party OAuth authentication including
Apple Sign In, Google OAuth, WeChat Login, etc.
"""

import uuid
import httpx
import logging
from datetime import datetime, timezone
from typing import Optional, Tuple

from sqlmodel import Session, select

from app.models import (
    User, UserCreate, AuthProviderEnum, PlatformEnum,
    OAuthUserInfo, OAuthLoginRequest, OAuthLoginResponse
)
from app.core.security import create_access_token
from app import crud
from app.services.trial_service import create_trial_credits_for_user

logger = logging.getLogger(__name__)


# 彩色日志工具类（简化版）
class OAuthColoredLogger:
    """OAuth服务专用彩色日志工具类"""

    COLORS = {
        'RESET': '\033[0m',
        'BOLD': '\033[1m',
        'RED': '\033[91m',
        'GREEN': '\033[92m',
        'YELLOW': '\033[93m',
        'BLUE': '\033[94m',
        'MAGENTA': '\033[95m',
        'CYAN': '\033[96m',
        'WHITE': '\033[97m',
        'GRAY': '\033[90m',
    }

    @classmethod
    def _colorize(cls, text: str, color: str) -> str:
        """给文本添加颜色"""
        return f"{cls.COLORS.get(color, '')}{text}{cls.COLORS['RESET']}"

    @classmethod
    def log_login_attempt(cls, provider: str, user_id: str = None, platform: str = None):
        """记录登录尝试"""
        logger.info(
            f"\n{cls._colorize('🔐 OAUTH LOGIN', 'CYAN')}\n"
            f"{cls._colorize('Provider:', 'BLUE')} {cls._colorize(provider, 'WHITE')}\n"
            f"{cls._colorize('User ID:', 'BLUE')} {cls._colorize(user_id or 'Unknown', 'GRAY')}\n"
            f"{cls._colorize('Platform:', 'BLUE')} {cls._colorize(platform or 'Unknown', 'MAGENTA')}"
        )

    @classmethod
    def log_login_success(cls, user_id: str, is_new_user: bool, has_device_token: bool):
        """记录登录成功"""
        status_icon = '🆕' if is_new_user else '👤'
        user_type = 'New User' if is_new_user else 'Existing User'

        logger.info(
            f"\n{cls._colorize(f'{status_icon} LOGIN SUCCESS', 'GREEN')}\n"
            f"{cls._colorize('User ID:', 'BLUE')} {cls._colorize(user_id, 'WHITE')}\n"
            f"{cls._colorize('Type:', 'BLUE')} {cls._colorize(user_type, 'GREEN')}\n"
            f"{cls._colorize('Device Token:', 'BLUE')} {cls._colorize('✓' if has_device_token else '✗', 'GREEN' if has_device_token else 'GRAY')}"
        )

    @classmethod
    def log_device_token_registration(cls, user_id: str, success: bool, platform: str):
        """记录设备令牌注册"""
        status_icon = '📱' if success else '❌'
        status_color = 'GREEN' if success else 'RED'

        logger.info(
            f"\n{cls._colorize(f'{status_icon} DEVICE TOKEN', 'CYAN')}\n"
            f"{cls._colorize('User ID:', 'BLUE')} {cls._colorize(user_id, 'WHITE')}\n"
            f"{cls._colorize('Platform:', 'BLUE')} {cls._colorize(platform, 'MAGENTA')}\n"
            f"{cls._colorize('Status:', 'BLUE')} {cls._colorize('Registered' if success else 'Failed', status_color)}"
        )

    @classmethod
    def log_oauth_error(cls, error: str, provider: str = None):
        """记录OAuth错误"""
        log_msg = f"\n{cls._colorize('💥 OAUTH ERROR', 'RED')}\n"
        if provider:
            log_msg += f"{cls._colorize('Provider:', 'BLUE')} {cls._colorize(provider, 'WHITE')}\n"
        log_msg += f"{cls._colorize('Error:', 'BLUE')} {cls._colorize(error, 'RED')}"
        logger.error(log_msg)


class OAuthService:
    """第三方OAuth登录服务"""
    
    def __init__(self, session: Session):
        self.session = session
    
    async def authenticate_with_provider(
        self,
        oauth_request: OAuthLoginRequest,
        additional_user_info: dict = None,
        real_email: str = None,
        is_existing_user: bool = False
    ) -> Tuple[bool, Optional[OAuthUserInfo], Optional[str]]:
        """
        通过第三方平台验证用户身份

        Args:
            oauth_request: OAuth登录请求
            additional_user_info: 额外的用户信息（如Apple首次授权时的姓名信息）
            real_email: 真实邮箱地址（可能与token中的隐私邮箱不同）

        Returns:
            Tuple of (success, user_info, error_message)
        """
        if oauth_request.provider == AuthProviderEnum.apple:
            return await self._verify_apple_token(oauth_request.access_token, additional_user_info, real_email, is_existing_user)
        elif oauth_request.provider == AuthProviderEnum.google:
            return await self._verify_google_token(oauth_request.access_token)
        elif oauth_request.provider == AuthProviderEnum.wechat:
            return await self._verify_wechat_token(oauth_request.access_token)
        else:
            return False, None, f"Unsupported provider: {oauth_request.provider}"
    
    async def oauth_login(self, oauth_request: OAuthLoginRequest, additional_user_info: dict = None, real_email: str = None) -> OAuthLoginResponse:
        """
        处理第三方登录流程

        1. 验证第三方平台令牌
        2. 获取用户信息
        3. 查找或创建用户
        4. 生成我们系统的JWT令牌
        """
        # 🎨 彩色日志：记录登录尝试
        OAuthColoredLogger.log_login_attempt(
            oauth_request.provider.value,
            platform=oauth_request.platform.value
        )

        # 🔧 修复：先查找现有用户，然后验证令牌时传递是否为现有用户的信息
        # 这样可以避免为现有用户生成不必要的默认姓名

        # 先进行基础令牌验证以获取用户ID
        if oauth_request.provider == AuthProviderEnum.apple:
            # 对于Apple，先解析token获取用户ID
            import jwt
            try:
                decoded_token = jwt.decode(
                    oauth_request.access_token,
                    options={"verify_signature": False, "verify_exp": False}
                )
                provider_user_id = decoded_token.get('sub')
                if not provider_user_id:
                    raise ValueError("Apple token missing subject (user ID)")
            except Exception as e:
                raise ValueError(f"Failed to decode Apple token: {str(e)}")
        else:
            provider_user_id = None

        # 查找现有用户
        existing_user = None
        if provider_user_id:
            existing_user = await self._find_user_by_provider(
                oauth_request.provider,
                provider_user_id
            )

        # 验证第三方平台令牌（传递是否为现有用户的信息）
        success, user_info, error = await self.authenticate_with_provider(
            oauth_request, additional_user_info, real_email, existing_user is not None
        )

        if not success or not user_info:
            OAuthColoredLogger.log_oauth_error(error or "Failed to authenticate with provider", oauth_request.provider.value)
            raise ValueError(error or "Failed to authenticate with provider")
        
        is_new_user = False
        
        if existing_user:
            # 更新现有用户信息
            user = await self._update_user_from_oauth(existing_user, user_info, oauth_request.platform)
        else:
            # 创建新用户
            user = await self._create_user_from_oauth(user_info, oauth_request.platform)
            is_new_user = True
        
        # 🆕 注册设备令牌（如果提供）
        device_token_registered = False
        if oauth_request.device_token:
            device_token_registered = await self._register_device_token(user.id, oauth_request.device_token, oauth_request.platform)

        # 生成JWT令牌
        from datetime import timedelta
        access_token = create_access_token(
            subject=str(user.id),
            expires_delta=timedelta(days=8)  # 默认8天过期
        )

        # 🆕 获取试用状态信息
        trial_status = None
        if is_new_user:
            try:
                from app.services.trial_service import TrialService
                trial_service = TrialService(self.session)
                trial_status = trial_service.get_trial_status(user.id)
            except Exception as e:
                logger.error(f"Failed to get trial status for user {user.id}: {str(e)}")

        # 🎨 彩色日志：记录登录成功
        OAuthColoredLogger.log_login_success(
            str(user.id),
            is_new_user,
            bool(oauth_request.device_token)
        )

        return OAuthLoginResponse(
            access_token=access_token,
            user=user,
            is_new_user=is_new_user,
            trial_status=trial_status
        )
    
    async def _find_user_by_provider(
        self, 
        provider: AuthProviderEnum, 
        provider_user_id: str
    ) -> Optional[User]:
        """根据第三方平台信息查找用户"""
        statement = select(User).where(
            User.auth_provider == provider,
            User.provider_user_id == provider_user_id
        )
        return self.session.exec(statement).first()
    
    async def _create_user_from_oauth(
        self, 
        user_info: OAuthUserInfo, 
        platform: PlatformEnum
    ) -> User:
        """从第三方用户信息创建新用户"""
        # 检查邮箱是否已存在
        existing_email_user = self.session.exec(
            select(User).where(User.email == user_info.email)
        ).first()
        
        if existing_email_user:
            # 如果邮箱已存在，更新现有用户的第三方登录信息
            existing_email_user.auth_provider = user_info.provider
            existing_email_user.provider_user_id = user_info.provider_user_id
            existing_email_user.avatar_url = user_info.avatar_url
            existing_email_user.platform = platform
            existing_email_user.last_login = datetime.now(timezone.utc)
            
            self.session.add(existing_email_user)
            self.session.commit()
            self.session.refresh(existing_email_user)
            return existing_email_user
        
        # 创建新用户
        user_create = UserCreate(
            email=user_info.email,
            password=None,  # 第三方登录用户没有密码
            full_name=user_info.full_name,
            is_superuser=False
        )
        
        user = crud.create_user(session=self.session, user_create=user_create)
        
        # 更新第三方登录相关字段
        user.auth_provider = user_info.provider
        user.provider_user_id = user_info.provider_user_id
        user.avatar_url = user_info.avatar_url
        user.platform = platform
        user.last_login = datetime.now(timezone.utc)
        
        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)

        # 🆕 为新用户创建试用积分（2次免费生成）
        try:
            trial_credits = create_trial_credits_for_user(self.session, user)
            logger.info(f"Created trial credits for new OAuth user {user.id}: {trial_credits.credits} credits")
        except Exception as e:
            logger.error(f"Failed to create trial credits for new OAuth user {user.id}: {str(e)}")
            # 不影响用户创建流程，只记录错误

        return user
    
    async def _update_user_from_oauth(
        self,
        user: User,
        user_info: OAuthUserInfo,
        platform: PlatformEnum
    ) -> User:
        """更新现有用户的第三方登录信息"""
        # 🔧 修复：仅在用户没有姓名或提供了真实姓名时才更新
        # 避免用默认生成的姓名覆盖现有用户的真实姓名
        if user_info.full_name:
            # 检查是否为Apple默认生成的姓名格式
            is_apple_default_name = (
                user_info.provider == AuthProviderEnum.apple and
                user_info.full_name.startswith("Apple User ")
            )

            # 只有在以下情况才更新姓名：
            # 1. 用户当前没有姓名
            # 2. 不是Apple默认生成的姓名（即真实提供的姓名）
            if not user.full_name or not is_apple_default_name:
                logger.info(f"Updating user full_name from '{user.full_name}' to '{user_info.full_name}'")
                user.full_name = user_info.full_name
            else:
                logger.info(f"Keeping existing full_name '{user.full_name}', skipping Apple default name '{user_info.full_name}'")
        else:
            # 如果没有提供姓名信息（现有用户登录），保持原有姓名不变
            logger.info(f"No name provided, keeping existing full_name '{user.full_name}'")

        if user_info.avatar_url:
            user.avatar_url = user_info.avatar_url

        # 🔧 修复：智能邮箱更新逻辑
        # 保护现有用户的真实邮箱，避免被Apple隐私邮箱覆盖
        if user_info.email and user_info.email != user.email:
            # 检查是否为Apple隐私邮箱
            is_apple_private_email = (
                user_info.provider == AuthProviderEnum.apple and
                '@privaterelay.appleid.com' in user_info.email
            )

            # 检查现有邮箱是否为真实邮箱（非Apple隐私邮箱）
            is_existing_real_email = (
                user.email and
                '@privaterelay.appleid.com' not in user.email and
                not user.email.endswith('@example.com')  # 排除测试邮箱
            )

            # 只有在以下情况才更新邮箱：
            # 1. 用户当前没有邮箱
            # 2. 新邮箱不是Apple隐私邮箱（即真实邮箱）
            # 3. 现有邮箱不是真实邮箱（可以被真实邮箱替换）
            should_update_email = (
                not user.email or  # 没有邮箱
                (not is_apple_private_email) or  # 新邮箱是真实邮箱
                (not is_existing_real_email)  # 现有邮箱不是真实邮箱
            )

            if should_update_email:
                # 检查新邮箱是否已被其他用户使用
                existing_email_user = self.session.exec(
                    select(User).where(User.email == user_info.email)
                ).first()

                if existing_email_user and existing_email_user.id != user.id:
                    logger.warning(f"Cannot update email to {user_info.email}: already used by another user")
                else:
                    logger.info(f"Updating user email from '{user.email}' to '{user_info.email}'")
                    user.email = user_info.email
            else:
                logger.info(f"Preserving existing real email '{user.email}', skipping Apple private email '{user_info.email}'")
        else:
            logger.info(f"No email change needed, keeping existing email '{user.email}'")

        user.platform = platform
        user.last_login = datetime.now(timezone.utc)

        self.session.add(user)
        self.session.commit()
        self.session.refresh(user)

        return user
    
    async def _verify_apple_token(self, identity_token: str, additional_user_info: dict = None, real_email: str = None, is_existing_user: bool = False) -> Tuple[bool, Optional[OAuthUserInfo], Optional[str]]:
        """验证 Apple Sign In Identity Token (JWT)"""
        try:
            import jwt
            import json

            # 对于开发测试，我们可以解析 JWT 但跳过签名验证
            # 生产环境中需要从 Apple 获取公钥进行验证

            # 解码 JWT（跳过验证用于测试）
            try:
                # 先尝试不验证签名的解码（仅用于开发测试）
                decoded_token = jwt.decode(
                    identity_token,
                    options={"verify_signature": False, "verify_exp": False}
                )
                logger.info(f"Decoded Apple token: {json.dumps(decoded_token, indent=2)}")
            except jwt.InvalidTokenError as e:
                return False, None, f"Invalid Apple identity token format: {str(e)}"

            # 提取用户信息
            subject = decoded_token.get('sub')  # Apple 用户唯一标识符
            token_email = decoded_token.get('email')  # Token中的邮箱（可能是隐私邮箱）
            email_verified = decoded_token.get('email_verified', False)

            if not subject:
                return False, None, "Apple token missing subject (user ID)"

            if not token_email:
                return False, None, "Apple token missing email"

            if not email_verified:
                return False, None, "Apple email not verified"

            # 🔧 优化：优先使用客户端提供的真实邮箱，否则使用token中的邮箱
            final_email = real_email if real_email else token_email
            logger.info(f"Apple login - Token email: {token_email}, Real email: {real_email}, Final email: {final_email}")

            # 🔧 修复：Apple Identity Token 通常不包含姓名信息
            # 姓名信息通常在首次授权时通过 authorization response 提供，而不是在 identity token 中
            full_name = None

            # 1. 优先使用客户端传递的额外用户信息（首次授权时）
            if additional_user_info:
                first_name = additional_user_info.get('firstName', '') or additional_user_info.get('first_name', '')
                last_name = additional_user_info.get('lastName', '') or additional_user_info.get('last_name', '')
                if first_name or last_name:
                    full_name = f"{first_name} {last_name}".strip()
                    logger.info(f"Using additional user info for name: {full_name}")

            # 2. 尝试从 token 中提取姓名（某些情况下可能存在）
            if not full_name:
                name_info = decoded_token.get('name', {})
                if isinstance(name_info, dict) and name_info:
                    first_name = name_info.get('firstName', '') or name_info.get('first_name', '')
                    last_name = name_info.get('lastName', '') or name_info.get('last_name', '')
                    if first_name or last_name:
                        full_name = f"{first_name} {last_name}".strip()
                        logger.info(f"Using token name info: {full_name}")

            # 3. 🔧 修复：仅为新用户生成默认姓名，现有用户保持原有姓名
            if not full_name and not is_existing_user:
                email_prefix = final_email.split('@')[0]
                # 移除随机字符串，保留可读部分
                if '.' in email_prefix and not email_prefix.startswith('qvcw'):  # 避免Apple隐私邮箱的随机前缀
                    full_name = email_prefix.split('.')[0].capitalize()
                else:
                    full_name = f"Apple User {subject[-4:]}"  # 使用用户ID后4位
                logger.info(f"Using default name for new user: {full_name}")
            elif not full_name and is_existing_user:
                logger.info(f"Existing user login - no name provided, will preserve existing name")

            user_info = OAuthUserInfo(
                provider=AuthProviderEnum.apple,
                provider_user_id=subject,
                email=final_email,
                full_name=full_name,
                avatar_url=None  # Apple 不提供头像
            )

            logger.info(f"Extracted Apple user info: {user_info}")
            return True, user_info, None

        except ImportError:
            return False, None, "PyJWT library required for Apple Sign In verification"
        except Exception as e:
            logger.error(f"Apple token verification failed: {str(e)}")
            return False, None, f"Apple token verification failed: {str(e)}"
    
    async def _verify_google_token(self, access_token: str) -> Tuple[bool, Optional[OAuthUserInfo], Optional[str]]:
        """验证 Google OAuth 令牌"""
        try:
            # 使用 Google 的令牌验证 API
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"https://www.googleapis.com/oauth2/v1/tokeninfo?access_token={access_token}"
                )
                
                if response.status_code != 200:
                    return False, None, "Invalid Google access token"
                
                token_info = response.json()
                
                # 获取用户信息
                user_response = await client.get(
                    "https://www.googleapis.com/oauth2/v2/userinfo",
                    headers={"Authorization": f"Bearer {access_token}"}
                )
                
                if user_response.status_code != 200:
                    return False, None, "Failed to get Google user info"
                
                user_data = user_response.json()
                
                user_info = OAuthUserInfo(
                    provider=AuthProviderEnum.google,
                    provider_user_id=user_data["id"],
                    email=user_data["email"],
                    full_name=user_data.get("name"),
                    avatar_url=user_data.get("picture")
                )
                
                return True, user_info, None
                
        except Exception as e:
            return False, None, f"Google token verification failed: {str(e)}"
    
    async def _verify_wechat_token(self, access_token: str) -> Tuple[bool, Optional[OAuthUserInfo], Optional[str]]:
        """验证微信登录令牌"""
        try:
            # 这里应该实现微信登录的令牌验证逻辑
            # 参考微信开放平台文档
            
            # 临时示例返回
            return False, None, "WeChat login verification not implemented yet"
            
        except Exception as e:
            return False, None, f"WeChat token verification failed: {str(e)}"

    async def _register_device_token(self, user_id: uuid.UUID, device_token: str, platform: PlatformEnum) -> bool:
        """注册设备令牌"""
        try:
            from app.services.device_token_service import DeviceTokenService
            from app.models import DeviceTokenRegisterRequest

            device_service = DeviceTokenService(self.session)

            # 创建设备令牌注册请求
            register_request = DeviceTokenRegisterRequest(
                device_token=device_token,
                platform=platform
            )

            # 注册设备令牌
            result = device_service.register_device_token(user_id, register_request)

            # 🎨 彩色日志：记录设备令牌注册结果
            OAuthColoredLogger.log_device_token_registration(
                str(user_id), result.success, platform.value
            )

            return result.success

        except Exception as e:
            # 🎨 彩色日志：记录设备令牌注册错误
            OAuthColoredLogger.log_device_token_registration(
                str(user_id), False, platform.value
            )
            logger.error(f"Error registering device token for user {user_id}: {str(e)}")
            return False
