"""
Apple Push Notification Service (APNS) integration.

This module handles sending push notifications to iOS devices.
"""

import json
import logging
import os
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any

import httpx
import jwt
from sqlmodel import Session, select

from app.core.config import settings
from app.models import DeviceToken, User, PlatformEnum

logger = logging.getLogger(__name__)


# 彩色日志工具类（简化版）
class APNSColoredLogger:
    """APNS服务专用彩色日志工具类"""

    COLORS = {
        'RESET': '\033[0m',
        'BOLD': '\033[1m',
        'RED': '\033[91m',
        'GREEN': '\033[92m',
        'YELLOW': '\033[93m',
        'BLUE': '\033[94m',
        'MAGENTA': '\033[95m',
        'CYAN': '\033[96m',
        'WHITE': '\033[97m',
        'GRAY': '\033[90m',
    }

    @classmethod
    def _colorize(cls, text: str, color: str) -> str:
        """给文本添加颜色"""
        return f"{cls.COLORS.get(color, '')}{text}{cls.COLORS['RESET']}"

    @classmethod
    def log_notification_send(cls, user_id: str, title: str, device_count: int):
        """记录通知发送信息"""
        logger.info(
            f"\n{cls._colorize('📱 APNS NOTIFICATION', 'CYAN')}\n"
            f"{cls._colorize('User ID:', 'BLUE')} {cls._colorize(user_id, 'WHITE')}\n"
            f"{cls._colorize('Title:', 'BLUE')} {cls._colorize(title, 'YELLOW')}\n"
            f"{cls._colorize('Device Count:', 'BLUE')} {cls._colorize(str(device_count), 'MAGENTA')}"
        )

    @classmethod
    def log_notification_result(cls, user_id: str, success_count: int, total_count: int):
        """记录通知发送结果"""
        status_icon = '✅' if success_count > 0 else '❌'
        status_color = 'GREEN' if success_count > 0 else 'RED'

        logger.info(
            f"\n{cls._colorize(f'{status_icon} APNS RESULT', 'CYAN')}\n"
            f"{cls._colorize('User ID:', 'BLUE')} {cls._colorize(user_id, 'WHITE')}\n"
            f"{cls._colorize('Success:', 'BLUE')} {cls._colorize(f'{success_count}/{total_count}', status_color)}"
        )

    @classmethod
    def log_apns_error(cls, error: str, user_id: str = None):
        """记录APNS错误信息"""
        log_msg = f"\n{cls._colorize('💥 APNS ERROR', 'RED')}\n"
        if user_id:
            log_msg += f"{cls._colorize('User ID:', 'BLUE')} {cls._colorize(user_id, 'WHITE')}\n"
        log_msg += f"{cls._colorize('Error:', 'BLUE')} {cls._colorize(error, 'RED')}"
        logger.error(log_msg)

    @classmethod
    def log_apns_warning(cls, message: str, user_id: str = None):
        """记录APNS警告信息"""
        log_msg = f"\n{cls._colorize('⚠️  APNS WARNING', 'YELLOW')}\n"
        if user_id:
            log_msg += f"{cls._colorize('User ID:', 'BLUE')} {cls._colorize(user_id, 'WHITE')}\n"
        log_msg += f"{cls._colorize('Warning:', 'BLUE')} {cls._colorize(message, 'YELLOW')}"
        logger.warning(log_msg)


class APNSService:
    """Apple Push Notification Service"""
    
    def __init__(self, session: Optional[Session] = None):
        self.session = session
        self.key_id = settings.APNS_KEY_ID
        self.team_id = settings.APNS_TEAM_ID
        self.bundle_id = settings.APNS_BUNDLE_ID
        self.key_path = settings.APNS_KEY_PATH
        self.use_sandbox = settings.APNS_USE_SANDBOX
        
        # APNS URLs
        self.apns_url = (
            "https://api.sandbox.push.apple.com" if self.use_sandbox 
            else "https://api.push.apple.com"
        )
        
        if not all([self.key_id, self.team_id, self.bundle_id, self.key_path]):
            logger.warning("APNS configuration is incomplete")
    
    def _generate_jwt_token(self) -> str:
        """生成APNS JWT token"""
        try:
            # 🔧 修复：检查密钥文件是否存在
            if not self.key_path or not os.path.exists(self.key_path):
                raise FileNotFoundError(f"APNS key file not found: {self.key_path}")

            # 读取私钥文件
            try:
                with open(self.key_path, 'r') as key_file:
                    private_key = key_file.read()
            except Exception as e:
                raise IOError(f"Failed to read APNS key file: {str(e)}")

            # 🔧 修复：验证私钥格式
            if "BEGIN PRIVATE KEY" not in private_key or "END PRIVATE KEY" not in private_key:
                raise ValueError("Invalid APNS private key format")

            # JWT header
            headers = {
                "alg": "ES256",
                "kid": self.key_id
            }

            # JWT payload
            payload = {
                "iss": self.team_id,
                "iat": int(datetime.now(timezone.utc).timestamp())
            }

            # 🔧 修复：更详细的JWT生成错误处理
            try:
                token = jwt.encode(payload, private_key, algorithm="ES256", headers=headers)
                return token
            except Exception as e:
                if "cryptography" in str(e).lower():
                    raise ImportError("cryptography library is required for ES256 algorithm. Install with: pip install cryptography")
                else:
                    raise ValueError(f"JWT encoding failed: {str(e)}")

        except Exception as e:
            logger.error(f"Failed to generate JWT token: {str(e)}")
            raise
    
    def _get_headers(self) -> Dict[str, str]:
        """获取APNS请求头"""
        token = self._generate_jwt_token()
        return {
            "authorization": f"bearer {token}",
            "apns-topic": self.bundle_id,
            "content-type": "application/json"
        }
    
    async def send_notification(
        self,
        device_token: str,
        title: str,
        body: str,
        data: Optional[Dict[str, Any]] = None,
        badge: Optional[int] = None,
        sound: str = "default"
    ) -> bool:
        """
        发送推送通知到指定设备

        Args:
            device_token: 设备token
            title: 通知标题
            body: 通知内容
            data: 自定义数据
            badge: 角标数量
            sound: 声音

        Returns:
            bool: 是否发送成功
        """
        try:
            # 🔧 修复：检查配置
            if not self.is_configured():
                logger.warning("APNS not configured, skipping notification")
                return False

            # 🔧 修复：验证设备令牌格式
            if not device_token or len(device_token) < 64:
                logger.error(f"Invalid device token format: {device_token[:10]}...")
                return False

            # 构建推送payload
            payload = {
                "aps": {
                    "alert": {
                        "title": title,
                        "body": body
                    },
                    "sound": sound
                }
            }

            if badge is not None:
                payload["aps"]["badge"] = badge

            if data:
                payload.update(data)

            # 🔧 修复：更安全的header生成
            try:
                headers = self._get_headers()
            except Exception as e:
                logger.error(f"Failed to generate headers: {str(e)}")
                return False

            url = f"{self.apns_url}/3/device/{device_token}"

            logger.info(f"Sending APNS notification to device: {device_token[:10]}...")
            logger.debug(f"URL: {url}")
            logger.debug(f"Headers: {headers}")
            logger.debug(f"Payload: {payload}")

            # 🔧 修复：更详细的HTTP客户端配置
            async with httpx.AsyncClient(
                timeout=httpx.Timeout(30.0),
                verify=True,  # 验证SSL证书
                http2=True    # 使用HTTP/2（APNS推荐）
            ) as client:
                response = await client.post(
                    url,
                    json=payload,
                    headers=headers
                )

            if response.status_code == 200:
                logger.info(f"APNS notification sent successfully to {device_token[:10]}...")
                return True
            else:
                logger.error(f"APNS notification failed: {response.status_code} - {response.text}")
                # 🔧 修复：详细的错误信息
                try:
                    error_data = response.json()
                    logger.error(f"APNS error details: {error_data}")
                except:
                    pass
                return False

        except httpx.TimeoutException:
            logger.error(f"APNS notification timeout for device: {device_token[:10]}...")
            return False
        except httpx.ConnectError as e:
            logger.error(f"APNS connection error: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Failed to send APNS notification: {str(e)}")
            return False
    
    async def send_to_user(
        self,
        user_id: uuid.UUID,
        title: str,
        body: str,
        data: Optional[Dict[str, Any]] = None,
        badge: Optional[int] = None,
        sound: str = "default"
    ) -> Dict[str, Any]:
        """
        发送推送通知给指定用户的所有iOS设备
        
        Args:
            user_id: 用户ID
            title: 通知标题
            body: 通知内容
            data: 自定义数据
            badge: 角标数量
            sound: 声音
            
        Returns:
            Dict: 发送结果统计
        """
        if not self.session:
            APNSColoredLogger.log_apns_error("Database session not available", str(user_id))
            return {"success": False, "error": "Database session not available"}

        # 🔧 修复：优雅处理APNS配置缺失
        if not self.is_configured():
            APNSColoredLogger.log_apns_warning("APNS not configured, skipping notification", str(user_id))
            # 返回成功但跳过发送，避免阻塞主流程
            return {
                "success": True,
                "total_devices": 0,
                "successful_devices": 0,
                "skipped_reason": "APNS not configured",
                "results": []
            }

        try:
            # 获取用户的所有活跃iOS设备token
            statement = select(DeviceToken).where(
                DeviceToken.user_id == user_id,
                DeviceToken.platform == PlatformEnum.ios,
                DeviceToken.is_active == True
            )
            device_tokens = self.session.exec(statement).all()

            if not device_tokens:
                APNSColoredLogger.log_apns_warning(
                    "No active iOS device tokens found", str(user_id)
                )
                # 🔧 修复：返回成功但无设备，避免阻塞主流程
                return {
                    "success": True,
                    "total_devices": 0,
                    "successful_devices": 0,
                    "skipped_reason": "No active iOS device tokens found",
                    "results": []
                }

            # 🎨 彩色日志：记录通知发送
            APNSColoredLogger.log_notification_send(
                str(user_id), title, len(device_tokens)
            )
            
            # 发送通知到所有设备
            results = []
            for device_token in device_tokens:
                success = await self.send_notification(
                    device_token.device_token,
                    title,
                    body,
                    data,
                    badge,
                    sound
                )
                results.append({
                    "device_token_id": str(device_token.id),
                    "success": success
                })
            
            successful_count = sum(1 for r in results if r["success"])
            total_count = len(results)

            # 🎨 彩色日志：记录发送结果
            APNSColoredLogger.log_notification_result(
                str(user_id), successful_count, total_count
            )

            return {
                "success": successful_count > 0,
                "total_devices": total_count,
                "successful_devices": successful_count,
                "results": results
            }

        except Exception as e:
            # 🎨 彩色日志：记录错误
            APNSColoredLogger.log_apns_error(str(e), str(user_id))
            return {"success": False, "error": str(e)}
    
    def is_configured(self) -> bool:
        """检查APNS是否已正确配置"""
        return all([
            self.key_id,
            self.team_id,
            self.bundle_id,
            self.key_path
        ])


# 创建服务实例
apns_service = APNSService()


# 示例使用方法
if __name__ == "__main__":
    import asyncio
    
    async def test_apns():
        """测试APNS服务"""
        service = APNSService()
        
        if not service.is_configured():
            print("APNS not configured")
            return
        
        # 测试发送通知
        success = await service.send_notification(
            device_token="test_device_token",
            title="Test Notification",
            body="This is a test notification",
            data={"task_id": "test123", "type": "image_generation"}
        )
        
        print(f"Notification sent: {success}")
    
    asyncio.run(test_apns())
