"""
Image Generation Callback Service.

This module handles callbacks from the image generation API and sends push notifications.
"""

import json
import logging
import uuid
from typing import Dict, Any, Optional

from sqlmodel import Session, select

from app.models import ImageGenerationRecord, User, PlatformEnum
from app.services.apns_service import APNSService

logger = logging.getLogger(__name__)


# 彩色日志工具类（简化版）
class CallbackColoredLogger:
    """回调服务专用彩色日志工具类"""

    COLORS = {
        'RESET': '\033[0m',
        'BOLD': '\033[1m',
        'RED': '\033[91m',
        'GREEN': '\033[92m',
        'YELLOW': '\033[93m',
        'BLUE': '\033[94m',
        'MAGENTA': '\033[95m',
        'CYAN': '\033[96m',
        'WHITE': '\033[97m',
        'GRAY': '\033[90m',
    }

    @classmethod
    def _colorize(cls, text: str, color: str) -> str:
        """给文本添加颜色"""
        return f"{cls.COLORS.get(color, '')}{text}{cls.COLORS['RESET']}"

    @classmethod
    def log_callback_received(cls, callback_data: Dict, task_id: str = None):
        """记录回调接收信息"""
        logger.info(
            f"\n{cls._colorize('📞 CALLBACK RECEIVED', 'CYAN')}\n"
            f"{cls._colorize('Task ID:', 'BLUE')} {cls._colorize(task_id or 'Unknown', 'WHITE')}\n"
            f"{cls._colorize('Data:', 'BLUE')} {cls._colorize(json.dumps(callback_data, indent=2, ensure_ascii=False), 'YELLOW')}"
        )

    @classmethod
    def log_record_search(cls, task_id: str, method: str, found: bool, record_id: str = None):
        """记录数据库查找信息"""
        status_icon = '✅' if found else '❌'
        status_color = 'GREEN' if found else 'RED'

        log_msg = (
            f"\n{cls._colorize(f'{status_icon} RECORD SEARCH', 'CYAN')}\n"
            f"{cls._colorize('Task ID:', 'BLUE')} {cls._colorize(task_id, 'WHITE')}\n"
            f"{cls._colorize('Method:', 'BLUE')} {cls._colorize(method, 'MAGENTA')}\n"
            f"{cls._colorize('Found:', 'BLUE')} {cls._colorize(str(found), status_color)}"
        )

        if found and record_id:
            log_msg += f"\n{cls._colorize('Record ID:', 'BLUE')} {cls._colorize(record_id, 'WHITE')}"

        logger.info(log_msg)

    @classmethod
    def log_record_update(cls, record_id: str, success: bool, result_urls: list = None):
        """记录数据库更新信息"""
        status_icon = '✅' if success else '❌'
        status_color = 'GREEN' if success else 'RED'

        log_msg = (
            f"\n{cls._colorize(f'{status_icon} RECORD UPDATE', 'CYAN')}\n"
            f"{cls._colorize('Record ID:', 'BLUE')} {cls._colorize(record_id, 'WHITE')}\n"
            f"{cls._colorize('Success:', 'BLUE')} {cls._colorize(str(success), status_color)}"
        )

        if success and result_urls:
            log_msg += f"\n{cls._colorize('Result URLs:', 'BLUE')} {cls._colorize(str(len(result_urls)), 'MAGENTA')} URLs saved"

        logger.info(log_msg)

    @classmethod
    def log_callback_error(cls, error: str, task_id: str = None):
        """记录回调错误信息"""
        logger.error(
            f"\n{cls._colorize('💥 CALLBACK ERROR', 'RED')}\n"
            f"{cls._colorize('Task ID:', 'BLUE')} {cls._colorize(task_id or 'Unknown', 'WHITE')}\n"
            f"{cls._colorize('Error:', 'BLUE')} {cls._colorize(error, 'RED')}"
        )


class ImageCallbackService:
    """图片生成回调处理服务"""
    
    def __init__(self, session: Session):
        self.session = session
        self.apns_service = APNSService(session)
    
    async def handle_callback(self, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理图片生成回调

        Args:
            callback_data: 回调数据

        Returns:
            Dict: 处理结果
        """
        task_id = None
        try:
            # 解析回调数据
            code = callback_data.get("code")
            msg = callback_data.get("msg", "")
            data = callback_data.get("data", {})
            task_id = data.get("taskId")

            # 🎨 彩色日志：记录回调接收
            CallbackColoredLogger.log_callback_received(callback_data, task_id)

            if not task_id:
                error_msg = "No taskId in callback data"
                CallbackColoredLogger.log_callback_error(error_msg)
                return {"success": False, "error": "Missing taskId"}

            # 查找对应的生成记录
            record = await self._find_generation_record(task_id)
            if not record:
                error_msg = f"No generation record found for taskId: {task_id}"
                CallbackColoredLogger.log_callback_error(error_msg, task_id)
                return {"success": False, "error": "Generation record not found"}
            
            # 更新记录状态
            success = code == 200
            await self._update_generation_record(record, callback_data, success)
            
            # 发送推送通知
            await self._send_push_notification(record, success, msg, data)
            
            logger.info(f"Successfully processed callback for taskId: {task_id}")

            # 构建详细的成功响应
            result_data = {
                "success": True,
                "message": "Callback processed successfully",
                "record_id": str(record.id),
                "status": record.status,
                "task_id": task_id
            }

            # 如果是成功的回调，添加结果URL信息
            if success and record.result_urls:
                try:
                    result_urls = json.loads(record.result_urls)
                    result_data["result_urls"] = result_urls
                except json.JSONDecodeError:
                    pass

            return result_data
            
        except Exception as e:
            logger.error(f"Failed to handle callback: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _find_generation_record(self, task_id: str) -> Optional[ImageGenerationRecord]:
        """
        根据task_id查找生成记录

        Args:
            task_id: 任务ID

        Returns:
            Optional[ImageGenerationRecord]: 生成记录
        """
        try:
            # 🆕 优化：直接使用task_id字段查询，提高效率
            statement = select(ImageGenerationRecord).where(
                ImageGenerationRecord.task_id == task_id
            )
            record = self.session.exec(statement).first()

            if record:
                # 🎨 彩色日志：通过task_id字段找到记录
                CallbackColoredLogger.log_record_search(
                    task_id, "task_id field", True, str(record.id)
                )
                return record

            # 🔄 兼容性：如果task_id字段为空，回退到原有的JSON搜索方式
            CallbackColoredLogger.log_record_search(
                task_id, "task_id field", False
            )

            statement = select(ImageGenerationRecord).where(
                ImageGenerationRecord.api_response.contains(task_id)
            )
            records = self.session.exec(statement).all()

            # 进一步验证task_id匹配
            for record in records:
                if record.api_response:
                    try:
                        api_data = json.loads(record.api_response)
                        if api_data.get("data", {}).get("taskId") == task_id:
                            # 🆕 同时更新task_id字段
                            record.task_id = task_id
                            self.session.add(record)
                            self.session.commit()

                            # 🎨 彩色日志：通过JSON搜索找到记录并更新
                            CallbackColoredLogger.log_record_search(
                                task_id, "JSON search + update", True, str(record.id)
                            )
                            return record
                    except json.JSONDecodeError:
                        continue

            # 🎨 彩色日志：未找到记录
            CallbackColoredLogger.log_record_search(
                task_id, "JSON search", False
            )
            return None

        except Exception as e:
            CallbackColoredLogger.log_callback_error(
                f"Failed to find generation record: {str(e)}", task_id
            )
            return None
    
    async def _update_generation_record(
        self,
        record: ImageGenerationRecord,
        callback_data: Dict[str, Any],
        success: bool
    ) -> None:
        """
        更新生成记录

        Args:
            record: 生成记录
            callback_data: 回调数据
            success: 是否成功
        """
        try:
            # 更新状态
            record.status = "success" if success else "failed"

            if success:
                # 🔧 修复：提取并保存图片结果URL
                data = callback_data.get("data", {})
                info = data.get("info", {})
                result_urls = info.get("result_urls", [])

                if result_urls:
                    # 保存图片URL到数据库
                    record.result_urls = json.dumps(result_urls)

                    # 如果有多个图片，取第一个作为主要结果
                    if len(result_urls) > 0:
                        record.result_url = result_urls[0]

                    # 🎨 彩色日志：记录更新成功
                    CallbackColoredLogger.log_record_update(
                        str(record.id), True, result_urls
                    )
                else:
                    CallbackColoredLogger.log_record_update(
                        str(record.id), True, []
                    )

                # 清除错误信息
                record.error_message = None
            else:
                # 更新错误信息
                record.error_message = callback_data.get("msg", "Generation failed")
                logger.info(f"Updated error message for record {record.id}: {record.error_message}")

            # 更新API响应（合并原有响应和回调数据）
            if record.api_response:
                try:
                    original_response = json.loads(record.api_response)
                    original_response["callback_data"] = callback_data
                    record.api_response = json.dumps(original_response)
                except json.JSONDecodeError:
                    record.api_response = json.dumps(callback_data)
            else:
                record.api_response = json.dumps(callback_data)

            self.session.add(record)
            self.session.commit()

            logger.info(f"Successfully updated generation record {record.id} - Status: {record.status}")

        except Exception as e:
            logger.error(f"Failed to update generation record {record.id}: {str(e)}")
            self.session.rollback()
            raise
    
    async def _send_push_notification(
        self,
        record: ImageGenerationRecord,
        success: bool,
        message: str,
        data: Dict[str, Any]
    ) -> None:
        """
        发送推送通知
        
        Args:
            record: 生成记录
            success: 是否成功
            message: 消息内容
            data: 回调数据
        """
        try:
            if not self.apns_service.is_configured():
                logger.warning("APNS not configured, skipping push notification")
                return
            
            # 构建通知内容
            if success:
                title = "图片生成完成"
                body = "您的图片已成功生成，点击查看结果"
                
                # 提取结果URL
                info = data.get("info", {})
                result_urls = info.get("result_urls", []) if info else []
                
                notification_data = {
                    "type": "image_generation_success",
                    "task_id": data.get("taskId"),
                    "record_id": str(record.id),
                    "result_urls": result_urls
                }
            else:
                title = "图片生成失败"
                body = message or "图片生成过程中出现错误"
                
                notification_data = {
                    "type": "image_generation_failed",
                    "task_id": data.get("taskId"),
                    "record_id": str(record.id),
                    "error_message": message
                }
            
            # 发送推送通知
            result = await self.apns_service.send_to_user(
                user_id=record.user_id,
                title=title,
                body=body,
                data=notification_data
            )
            
            if result.get("success"):
                logger.info(f"Push notification sent to user {record.user_id} for record {record.id}")
            else:
                logger.error(f"Failed to send push notification: {result.get('error')}")
                
        except Exception as e:
            logger.error(f"Failed to send push notification for record {record.id}: {str(e)}")
    
    def validate_callback_data(self, callback_data: Dict[str, Any]) -> tuple[bool, str]:
        """
        验证回调数据格式
        
        Args:
            callback_data: 回调数据
            
        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        try:
            # 检查必需字段
            if "code" not in callback_data:
                return False, "Missing 'code' field"
            
            if "data" not in callback_data:
                return False, "Missing 'data' field"
            
            data = callback_data["data"]
            if not isinstance(data, dict):
                return False, "'data' field must be a dictionary"
            
            if "taskId" not in data:
                return False, "Missing 'taskId' in data"
            
            return True, ""
            
        except Exception as e:
            return False, f"Invalid callback data format: {str(e)}"


# 示例使用方法
if __name__ == "__main__":
    import asyncio
    
    async def test_callback_service():
        """测试回调服务"""
        # 模拟成功回调数据
        success_callback = {
            "code": 200,
            "msg": "success",
            "data": {
                "taskId": "test12345",
                "info": {
                    "result_urls": [
                        "https://example.com/result/image1.png"
                    ]
                }
            }
        }
        
        # 模拟失败回调数据
        failure_callback = {
            "code": 400,
            "msg": "您的内容被 OpenAI 标记为违反内容政策",
            "data": {
                "taskId": "test12345",
                "info": None
            }
        }
        
        print("Callback service test data prepared")
        print(f"Success callback: {success_callback}")
        print(f"Failure callback: {failure_callback}")
    
    asyncio.run(test_callback_service())
