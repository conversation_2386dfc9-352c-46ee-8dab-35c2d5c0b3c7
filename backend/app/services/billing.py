"""
Billing and subscription validation service.

This module handles subscription validation, usage limits, and billing logic.
"""

import uuid
from datetime import datetime, timezone
from typing import Dict, Optional, Tuple

from sqlmodel import Session, select, func

from app.models import (
    User, Subscription, UsageRecord, CreditPackage,
    UsageTypeEnum, UserSubscriptionStatus, AccessCheckResponse,
    SubscriptionPlatformEnum
)


class BillingService:
    """Service for handling billing and subscription logic."""
    
    # Subscription product configurations
    SUBSCRIPTION_LIMITS = {
        "sub_monthly_40": {"monthly_limit": 40, "credits_per_use": 1},
        "sub_monthly_60": {"monthly_limit": 60, "credits_per_use": 1},
        "sub_yearly_480": {"monthly_limit": 40, "credits_per_use": 1},
        "sub_yearly_600": {"monthly_limit": 50, "credits_per_use": 1},  # 🆕 年度600次套餐 (50次/月)
        "sub_yearly_720": {"monthly_limit": 60, "credits_per_use": 1},
    }
    
    # Credit package configurations
    CREDIT_PACKAGES = {
        "credits_pack_10": {"credits": 10, "cost_per_use": 5},
        "credits_pack_50": {"credits": 50, "cost_per_use": 5},
        "credits_pack_100": {"credits": 100, "cost_per_use": 5},
        "trial_credits_new_user": {"credits": 10, "cost_per_use": 5},  # 🆕 试用积分配置
    }
    
    # 🆕 订阅积分配置 - 订阅成功后赠送的积分
    SUBSCRIPTION_BONUS_CREDITS = {
        "sub_monthly_40": {"bonus_credits": 200, "description": "月度40次订阅赠送积分"},
        "sub_monthly_60": {"bonus_credits": 300, "description": "月度60次订阅赠送积分"},
        "sub_yearly_480": {"bonus_credits": 2400, "description": "年度480次订阅赠送积分"},  # 12个月 * 200
        "sub_yearly_600": {"bonus_credits": 3000, "description": "年度600次订阅赠送积分"},  # 600次 * 5积分
        "sub_yearly_720": {"bonus_credits": 3600, "description": "年度720次订阅赠送积分"},  # 12个月 * 300
    }
    
    def __init__(self, session: Session):
        self.session = session
    
    def get_user_subscription_status(self, user_id: uuid.UUID) -> UserSubscriptionStatus:
        """Get comprehensive subscription status for a user."""
        user = self.session.get(User, user_id)
        if not user:
            raise ValueError(f"User {user_id} not found")
        
        # Check for active subscription
        active_subscription = self._get_active_subscription(user_id)
        
        # Calculate total available credits
        total_credits = self._get_total_credits(user_id)
        
        # Calculate monthly usage
        monthly_usage = self._get_monthly_usage_count(user_id)
        
        # Determine monthly limit
        monthly_limit = 0
        subscription_product_id = None
        subscription_end_date = None
        
        if active_subscription:
            subscription_product_id = active_subscription.product_id
            subscription_end_date = active_subscription.end_date
            monthly_limit = self.SUBSCRIPTION_LIMITS.get(
                active_subscription.product_id, {}
            ).get("monthly_limit", 0)
        
        # Determine if user can use service
        can_use_service = (
            (active_subscription and monthly_usage < monthly_limit) or 
            total_credits > 0
        )
        
        return UserSubscriptionStatus(
            user_id=user_id,
            has_active_subscription=active_subscription is not None,
            subscription_end_date=subscription_end_date,
            subscription_product_id=subscription_product_id,
            total_credits=total_credits,
            monthly_usage_count=monthly_usage,
            monthly_limit=monthly_limit,
            can_use_service=can_use_service
        )
    
    def check_access(self, user_id: uuid.UUID, usage_type: UsageTypeEnum) -> AccessCheckResponse:
        """Check if user can access the service."""
        status = self.get_user_subscription_status(user_id)
        
        if not status.can_use_service:
            if not status.has_active_subscription and status.total_credits == 0:
                return AccessCheckResponse(
                    can_access=False,
                    reason="No active subscription or credits available",
                    subscription_status="inactive"
                )
            elif status.has_active_subscription and status.monthly_usage_count >= status.monthly_limit:
                return AccessCheckResponse(
                    can_access=False,
                    reason="Monthly usage limit reached",
                    remaining_monthly_usage=0,
                    subscription_status="limit_reached"
                )
        
        remaining_monthly = max(0, status.monthly_limit - status.monthly_usage_count)
        
        return AccessCheckResponse(
            can_access=True,
            remaining_credits=status.total_credits,
            remaining_monthly_usage=remaining_monthly,
            subscription_status="active" if status.has_active_subscription else "credits_only"
        )
    
    def consume_usage(
        self, 
        user_id: uuid.UUID, 
        usage_type: UsageTypeEnum, 
        count: int = 1
    ) -> Tuple[bool, str, Dict]:
        """Consume usage for a user and return success status."""
        # Check access first
        access_check = self.check_access(user_id, usage_type)
        if not access_check.can_access:
            return False, access_check.reason or "Access denied", {}
        
        status = self.get_user_subscription_status(user_id)
        
        # Determine how to consume usage
        if status.has_active_subscription and status.monthly_usage_count < status.monthly_limit:
            # Use subscription quota
            self._record_usage(user_id, usage_type, count)
            remaining_monthly = max(0, status.monthly_limit - status.monthly_usage_count - count)
            
            return True, "Usage recorded against subscription", {
                "remaining_monthly_usage": remaining_monthly,
                "remaining_credits": status.total_credits
            }
        
        elif status.total_credits > 0:
            # Use credits
            credits_needed = count * self.CREDIT_PACKAGES.get("credits_pack_10", {}).get("cost_per_use", 5)
            
            if status.total_credits < credits_needed:
                return False, f"Insufficient credits. Need {credits_needed}, have {status.total_credits}", {}
            
            # Consume credits and record usage
            self._consume_credits(user_id, credits_needed)
            self._record_usage(user_id, usage_type, count)
            
            remaining_credits = status.total_credits - credits_needed
            
            return True, "Usage recorded against credits", {
                "remaining_credits": remaining_credits,
                "remaining_monthly_usage": max(0, status.monthly_limit - status.monthly_usage_count)
            }
        
        return False, "No available usage quota or credits", {}
    
    def _get_active_subscription(self, user_id: uuid.UUID) -> Optional[Subscription]:
        """Get active subscription for user."""
        now = datetime.now(timezone.utc)
        statement = select(Subscription).where(
            Subscription.user_id == user_id,
            Subscription.is_active == True,
            Subscription.start_date <= now,
            Subscription.end_date > now
        )
        return self.session.exec(statement).first()
    
    def _get_total_credits(self, user_id: uuid.UUID) -> int:
        """Get total available credits for user."""
        statement = select(func.sum(CreditPackage.remaining_credits)).where(
            CreditPackage.user_id == user_id,
            CreditPackage.remaining_credits > 0
        )
        result = self.session.exec(statement).first()
        return result or 0
    
    def _get_monthly_usage_count(self, user_id: uuid.UUID) -> int:
        """Get current month usage count for user."""
        now = datetime.now(timezone.utc)
        start_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        
        statement = select(func.sum(UsageRecord.count)).where(
            UsageRecord.user_id == user_id,
            UsageRecord.used_at >= start_of_month
        )
        result = self.session.exec(statement).first()
        return result or 0
    
    def _record_usage(self, user_id: uuid.UUID, usage_type: UsageTypeEnum, count: int):
        """Record usage in the database."""
        usage_record = UsageRecord(
            user_id=user_id,
            usage_type=usage_type,
            count=count
        )
        self.session.add(usage_record)
        self.session.commit()
    
    def _consume_credits(self, user_id: uuid.UUID, credits_needed: int):
        """
        Consume credits from user's credit packages.
        优先使用试用积分，然后使用购买的积分包
        """
        # 🆕 优先使用试用积分
        trial_statement = select(CreditPackage).where(
            CreditPackage.user_id == user_id,
            CreditPackage.product_id == "trial_credits_new_user",
            CreditPackage.remaining_credits > 0
        )

        # 然后使用其他积分包（按购买时间排序）
        other_statement = select(CreditPackage).where(
            CreditPackage.user_id == user_id,
            CreditPackage.product_id != "trial_credits_new_user",
            CreditPackage.remaining_credits > 0
        ).order_by(CreditPackage.purchased_at)

        # 合并查询结果：试用积分优先
        trial_packages = self.session.exec(trial_statement).all()
        other_packages = self.session.exec(other_statement).all()
        credit_packages = trial_packages + other_packages

        remaining_to_consume = credits_needed
        for package in credit_packages:
            if remaining_to_consume <= 0:
                break

            consume_from_package = min(package.remaining_credits, remaining_to_consume)
            package.remaining_credits -= consume_from_package
            remaining_to_consume -= consume_from_package

            self.session.add(package)

        self.session.commit()
    
    def create_subscription_bonus_credits(
        self, 
        user_id: uuid.UUID, 
        product_id: str, 
        platform: SubscriptionPlatformEnum = SubscriptionPlatformEnum.stripe
    ) -> Optional[CreditPackage]:
        """
        为订阅用户创建赠送积分包
        
        Args:
            user_id: 用户ID
            product_id: 订阅产品ID (如 "sub_monthly_40", "sub_yearly_480")
            platform: 订阅平台
            
        Returns:
            CreditPackage: 创建的积分包，如果该产品不提供赠送积分则返回None
        """
        import logging
        logger = logging.getLogger(__name__)
        
        # 检查该订阅产品是否有赠送积分配置
        bonus_config = self.SUBSCRIPTION_BONUS_CREDITS.get(product_id)
        if not bonus_config:
            logger.info(f"Product {product_id} does not have bonus credits configuration")
            return None
        
        bonus_credits = bonus_config["bonus_credits"]
        description = bonus_config["description"]
        
        try:
            # 检查用户是否已经有该产品的赠送积分包
            # 使用特殊的产品ID格式来标识赠送积分
            bonus_product_id = f"subscription_bonus_{product_id}"
            
            existing_bonus = self.session.exec(
                select(CreditPackage).where(
                    CreditPackage.user_id == user_id,
                    CreditPackage.product_id == bonus_product_id
                )
            ).first()
            
            if existing_bonus:
                logger.info(f"User {user_id} already has bonus credits for product {product_id}")
                # 如果已存在，可以选择增加积分或者跳过
                # 这里选择增加积分到现有包中
                existing_bonus.credits += bonus_credits
                existing_bonus.remaining_credits += bonus_credits
                existing_bonus.purchased_at = datetime.now(timezone.utc)  # 更新时间
                
                self.session.add(existing_bonus)
                self.session.commit()
                self.session.refresh(existing_bonus)
                
                logger.info(f"Added {bonus_credits} bonus credits to existing package for user {user_id}")
                return existing_bonus
            
            # 创建新的赠送积分包
            bonus_credit_package = CreditPackage(
                user_id=user_id,
                credits=bonus_credits,
                remaining_credits=bonus_credits,
                product_id=bonus_product_id,
                platform=platform,
                purchased_at=datetime.now(timezone.utc)
            )
            
            self.session.add(bonus_credit_package)
            self.session.commit()
            self.session.refresh(bonus_credit_package)
            
            logger.info(
                f"Created subscription bonus credits for user {user_id}: "
                f"{bonus_credits} credits for product {product_id} ({description})"
            )
            
            return bonus_credit_package
            
        except Exception as e:
            logger.error(f"Failed to create subscription bonus credits for user {user_id}: {str(e)}")
            self.session.rollback()
            raise
