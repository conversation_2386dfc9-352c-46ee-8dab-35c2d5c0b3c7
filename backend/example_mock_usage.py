#!/usr/bin/env python3
"""
图片生成Mock功能使用示例
Example Usage of Image Generation Mock Feature
"""

import requests
import json
import time
from typing import Dict, Any


class ImageGenerationMockExample:
    """图片生成Mock功能示例类"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.token = None
    
    def login(self, username: str = "<EMAIL>", password: str = "changethis") -> bool:
        """登录获取token"""
        print("🔐 正在登录...")
        
        login_data = {
            "username": username,
            "password": password
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/login/access-token",
                data=login_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"},
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                self.token = result.get("access_token")
                print("✅ 登录成功")
                return True
            else:
                print(f"❌ 登录失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {str(e)}")
            return False
    
    def generate_image_mock(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """调用图片生成接口（Mock模式）"""
        if not self.token:
            raise ValueError("请先登录获取token")
        
        headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
        
        # 准备请求数据
        request_data = {
            "prompt": prompt,
            "size": kwargs.get("size", "1:1"),
            "n_variants": kwargs.get("n_variants", 1),
            "is_enhance": kwargs.get("is_enhance", False),
            "upload_cn": kwargs.get("upload_cn", False),
            "enable_fallback": kwargs.get("enable_fallback", False),
            "fallback_model": kwargs.get("fallback_model", "FLUX_MAX")
        }
        
        # 添加可选参数
        if "files_url" in kwargs:
            request_data["files_url"] = kwargs["files_url"]
        if "callback_url" in kwargs:
            request_data["callback_url"] = kwargs["callback_url"]
        
        print(f"📤 发送图片生成请求: {prompt[:50]}...")
        print(f"📋 请求参数: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
        
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/image/generate-image",
                json=request_data,
                headers=headers,
                timeout=30
            )
            
            print(f"📥 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 请求成功")
                print(f"📋 响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
                return result
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"📋 错误响应: {response.text}")
                return {"error": f"HTTP {response.status_code}", "detail": response.text}
                
        except Exception as e:
            print(f"❌ 请求异常: {str(e)}")
            return {"error": "Exception", "detail": str(e)}
    
    def demo_basic_usage(self):
        """演示基本用法"""
        print("\n" + "="*60)
        print("🎨 演示1: 基本图片生成")
        print("="*60)
        
        result = self.generate_image_mock(
            prompt="Beautiful wedding photography, bride and groom, outdoor ceremony",
            size="1:1",
            n_variants=1,
            is_enhance=False
        )
        
        if "data" in result and "taskId" in result["data"]:
            task_id = result["data"]["taskId"]
            print(f"🎯 生成的任务ID: {task_id}")
            
            # 验证任务ID格式
            if task_id.startswith("test-") and len(task_id) == 17:
                print("✅ 任务ID格式正确")
            else:
                print("❌ 任务ID格式错误")
        
        return result
    
    def demo_with_reference_images(self):
        """演示带参考图片的生成"""
        print("\n" + "="*60)
        print("🖼️  演示2: 带参考图片的生成")
        print("="*60)
        
        result = self.generate_image_mock(
            prompt="Transform this into a romantic wedding scene",
            size="16:9",
            n_variants=2,
            is_enhance=True,
            files_url=[
                "https://img-bridal.wenhaofree.com/uploads/reference1.jpg",
                "https://img-bridal.wenhaofree.com/uploads/reference2.jpg"
            ],
            callback_url="https://myapp.com/webhook/image-complete"
        )
        
        return result
    
    def demo_multiple_requests(self):
        """演示多次请求生成不同任务ID"""
        print("\n" + "="*60)
        print("🔄 演示3: 多次请求验证随机性")
        print("="*60)
        
        task_ids = []
        
        for i in range(3):
            print(f"\n第{i+1}次请求:")
            result = self.generate_image_mock(
                prompt=f"Wedding photography style {i+1}",
                size="1:1"
            )
            
            if "data" in result and "taskId" in result["data"]:
                task_id = result["data"]["taskId"]
                task_ids.append(task_id)
                print(f"  任务ID: {task_id}")
            
            time.sleep(0.5)  # 短暂延迟
        
        # 验证唯一性
        unique_ids = set(task_ids)
        if len(unique_ids) == len(task_ids):
            print(f"✅ 所有任务ID都不同 ({len(task_ids)}个)")
        else:
            print(f"❌ 发现重复任务ID")
        
        return task_ids
    
    def demo_error_handling(self):
        """演示错误处理"""
        print("\n" + "="*60)
        print("⚠️  演示4: 错误处理")
        print("="*60)
        
        # 测试无效token
        original_token = self.token
        self.token = "invalid_token"
        
        result = self.generate_image_mock(
            prompt="This should fail with invalid token"
        )
        
        print("📋 无效token的响应:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 恢复有效token
        self.token = original_token
        
        return result


def main():
    """主演示函数"""
    print("🚀 图片生成Mock功能使用示例")
    print("="*60)
    
    # 创建示例实例
    example = ImageGenerationMockExample()
    
    # 登录
    if not example.login():
        print("❌ 登录失败，无法继续演示")
        return
    
    # 演示各种用法
    try:
        # 基本用法
        example.demo_basic_usage()
        
        # 带参考图片
        example.demo_with_reference_images()
        
        # 多次请求
        example.demo_multiple_requests()
        
        # 错误处理
        example.demo_error_handling()
        
        print("\n" + "="*60)
        print("🎉 所有演示完成!")
        print("="*60)
        
        print("\n💡 Mock模式特点:")
        print("  ✅ 响应格式: {\"code\": 200, \"msg\": \"success\", \"data\": {\"taskId\": \"test-xxx\"}}")
        print("  ✅ 任务ID前缀: test-")
        print("  ✅ 任务ID长度: 17个字符")
        print("  ✅ 随机生成: 每次调用都不同")
        print("  ✅ 无需真实API: 适合开发测试")
        
        print("\n🔧 配置说明:")
        print("  - 环境变量: ENABLE_IMAGE_GENERATION_MOCK=True")
        print("  - 配置文件: .env 中设置")
        print("  - 重启服务: 修改配置后需重启")
        
    except Exception as e:
        print(f"❌ 演示过程中出现异常: {str(e)}")


if __name__ == "__main__":
    main()
