"""Add result_url and result_urls fields to ImageGenerationRecord

Revision ID: add_result_urls
Revises: add_device_tokens
Create Date: 2025-01-30 15:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel

# revision identifiers, used by Alembic.
revision = 'add_result_urls'
down_revision = 'add_device_tokens'
branch_labels = None
depends_on = None


def upgrade():
    """Add result_url and result_urls columns to imagegenerationrecord table"""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('imagegenerationrecord', 
                  sa.Column('result_url', sqlmodel.sql.sqltypes.AutoString(length=1000), nullable=True))
    op.add_column('imagegenerationrecord', 
                  sa.Column('result_urls', sqlmodel.sql.sqltypes.AutoString(length=5000), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    """Remove result_url and result_urls columns from imagegenerationrecord table"""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('imagegenerationrecord', 'result_urls')
    op.drop_column('imagegenerationrecord', 'result_url')
    # ### end Alembic commands ###
