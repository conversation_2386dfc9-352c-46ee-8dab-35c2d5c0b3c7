"""Add device tokens table for push notifications

Revision ID: add_device_tokens
Revises: previous_revision
Create Date: 2025-01-30 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_device_tokens'
down_revision = None  # Replace with actual previous revision
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('devicetoken',
    sa.Column('device_token', sqlmodel.sql.sqltypes.AutoString(length=500), nullable=False),
    sa.Column('platform', sa.Enum('ios', 'android', 'web', name='platformenum'), nullable=False),
    sa.Column('is_active', sa.<PERSON>(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('id', sa.Uuid(), nullable=False),
    sa.<PERSON>umn('user_id', sa.Uuid(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['user.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_devicetoken_device_token'), 'devicetoken', ['device_token'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_devicetoken_device_token'), table_name='devicetoken')
    op.drop_table('devicetoken')
    # ### end Alembic commands ###
